#!/usr/bin/env bash

set -e

file_properties=/app/WEB-INF/classes/application.properties
original_file_properties=/app/application.properties
cp $original_file_properties $file_properties

if [ -f "$file_properties" ]; then
    sed -i "s~app.name=.*~app.name=$CONTEXTO~g" $file_properties
    sed -i "s~app.deploy.datacenter=.*~app.deploy.datacenter=$APP_DEPLOY_DATACENTER~g" $file_properties
    sed -i "s~discovery.url=.*~discovery.url=$DISCOVERY_URL~g" $file_properties
    sed -i "s~environment=.*~environment=$ENVIRONMENT~g" $file_properties
    sed -i "s~autenticacao.url=.*~autenticacao.url=$AUTENTICACAO_URL~g" $file_properties
    sed -i "s~media.url=.*~media.url=$MEDIA_URL~g" $file_properties
    sed -i "s~oamd.app.url=.*~oamd.app.url=$URL_OAMD~g" $file_properties
    sed -i "s~url.fotos.nuvem=.*~url.fotos.nuvem=$URL_FOTOS_NUVEM~g" $file_properties
    sed -i "s~server.port=.*~server.port=$SERVER_PORT~g" $file_properties
    sed -i "s~secret.key.path=.*~secret.key.path=$AUTH_SECRET_PATH~g" $file_properties
    sed -i "s~secret.key.zw.path=.*~secret.key.zw.path=$AUTH_SECRET_ZW_PATH~g" $file_properties
    sed -i "s~server.servlet.context-path=.*~server.servlet.context-path=/$CONTEXTO~g" $file_properties
    sed -i "s~spring.datasource.oamd.jdbcUrl=.*~spring.datasource.oamd.jdbcUrl=$OAMD_URL~g" $file_properties
    sed -i "s~spring.datasource.oamd.username=.*~spring.datasource.oamd.username=$OAMD_USERNAME~g" $file_properties
    sed -i "s~spring.datasource.oamd.password=.*~spring.datasource.oamd.password=$OAMD_PASSWORD~g" $file_properties
    sed -i "s~spring.datasource.oamd2.jdbcUrl=.*~spring.datasource.oamd2.jdbcUrl=$OAMD_2_URL~g" $file_properties
    sed -i "s~spring.datasource.oamd2.username=.*~spring.datasource.oamd2.username=$OAMD_2_USERNAME~g" $file_properties
    sed -i "s~spring.datasource.oamd2.password=.*~spring.datasource.oamd2.password=$OAMD_2_PASSWORD~g" $file_properties
    sed -i "s~app.config.debug=.*~app.config.debug=$APP_CONFIG_DEBUG~g" $file_properties
    echo "Application properties:"
    cat $file_properties
    exit 0
else
    echo "Setup environment failed"
    echo "File $file_properties not found"
    exit 0
fi

