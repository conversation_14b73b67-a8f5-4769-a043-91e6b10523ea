FROM openjdk:8-slim

ENV DEBUG=${DEBUG:-"false"}
ENV ENVIRONMENT=${ENVIRONMENT:-"dev"}
ENV APP_DEPLOY_DATACENTER=${APP_DEPLOY_DATACENTER:-""}
ENV DISCOVERY_URL=${DISCOVERY_URL:-"http://localhost:8086"}
ENV AUTENTICACAO_URL=${AUTENTICACAO_URL:-"http://localhost:8080"}
ENV MEDIA_URL=${MEDIA_URL:-"http://localhost:8098"}
ENV URL_OAMD=${URL_OAMD:-"http://localhost:8202/NewOAMD"}
ENV URL_FOTOS_NUVEM=${URL_FOTOS_NUVEM:-"https://dt39m1atv5spm.cloudfront.net"}
ENV SERVER_PORT=${SERVER_PORT:-"8080"}
ENV AUTH_SECRET_PATH=${AUTH_SECRET_PATH:-"/keys/auth-secret"}
ENV AUTH_SECRET_ZW_PATH=${AUTH_SECRET_ZW_PATH:-"/keys/auth-secret"}
ENV CONTEXTO=${CONTEXTO:-"adm-core"}
ENV TZ=America/Sao_Paulo
ENV OAMD_URL=${OAMD_URL:-"************************************"}
ENV OAMD_USERNAME=${OAMD_USERNAME:-"postgres"}
ENV OAMD_PASSWORD=${OAMD_PASSWORD:-"pactodb"}
ENV OAMD_2_URL=${OAMD_URL:-"*************************************"}
ENV OAMD_2_USERNAME=${OAMD_USERNAME:-"postgres"}
ENV OAMD_2_PASSWORD=${OAMD_PASSWORD:-"pactodb"}
ENV APP_CONFIG_DEBUG=${APP_CONFIG_DEBUG:-"false"}

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY ./target/adm-core-ms /app
COPY src/main/resources/application.properties /app/application.properties
COPY docker/bin/*.sh /bin/
COPY docker/keys/* /keys/

RUN chmod +x /bin/*.sh

ENTRYPOINT ["bash", "/bin/entrypoint.sh"]

