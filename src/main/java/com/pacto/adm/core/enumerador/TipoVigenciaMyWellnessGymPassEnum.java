package com.pacto.adm.core.enumerador;

public enum TipoVigenciaMyWellnessGymPassEnum {

    APENAS_NO_DIA(0, "Apenas um dia ao validar o token"),
    QUANTIDADE_INFORMADA(1, "Por uma quantidade de dias a partir da validação do token"),
    SEM_DATA_FINAL(2, "Sem data de expiração");

    String descricao;
    int id;
    private TipoVigenciaMyWellnessGymPassEnum(int id, String descricao) {
        this.descricao = descricao;
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public static TipoVigenciaMyWellnessGymPassEnum getFromId(int id) {
        for (TipoVigenciaMyWellnessGymPassEnum uso : values()) {
            if (uso.getId() == id) {
                return uso;
            }
        }
        return null;
    }

}
