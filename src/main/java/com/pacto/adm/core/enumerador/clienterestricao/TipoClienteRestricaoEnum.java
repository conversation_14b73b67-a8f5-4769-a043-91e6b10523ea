package com.pacto.adm.core.enumerador.clienterestricao;

public enum TipoClienteRestricaoEnum {

    INADINPLENCIA("IN", "Inadimplência"),
    RESGRISTRO_MANUAL_TELA_CLIENTE("RM", "Registro Manual");

    private String sigla;
    private String descricao;

    TipoClienteRestricaoEnum(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public static TipoClienteRestricaoEnum obterPorSigla(String tipo) {
        for (TipoClienteRestricaoEnum tipoClienteRestricaoEnum : TipoClienteRestricaoEnum.values()) {
            if (tipoClienteRestricaoEnum.getSigla().equals(tipo)) {
                return tipoClienteRestricaoEnum;
            }
        }
        return null;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
