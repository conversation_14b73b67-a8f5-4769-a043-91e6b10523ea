package com.pacto.adm.core.enumerador;

import com.fasterxml.jackson.annotation.JsonValue;

public enum TipoLinhaDoTempoEnum {


    TODOS(      999,    "Todos"),
    ADM(        1,      "Adm"),
    FINANCEIRO( 2,      "Financeiro"),
    CRM(        3,      "CRM"),
    TREINO(     4,      "Treino")
    ;


    TipoLinhaDoTempoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    private Integer codigo;
    private String descricao;

    @JsonValue
    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoLinhaDoTempoEnum getFromCodigo(Integer codigo) {
        for (TipoLinhaDoTempoEnum tipo : TipoLinhaDoTempoEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }
}
