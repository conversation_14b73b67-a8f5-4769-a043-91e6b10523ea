package com.pacto.adm.core.enumerador;

public enum TipoVigenciaEnum {
    PERIODO_FIXO("PF", "Período Fixo"),
    INTERVALO_DIAS("ID", "Intervalo de Dias"),
    VIGENCIA_VARIAVEL("VV", "Vigência Variável");

    private String codigo;
    private String descricao;


    TipoVigenciaEnum(String codigo,
                     String descricao
    ) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}
