package com.pacto.adm.core.enumerador.tipodesconto;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class TipoDescontoConverter implements AttributeConverter<TipoDescontoEnum, String> {
    @Override
    public String convertToDatabaseColumn(TipoDescontoEnum tipoDescontoEnum) {
        if (tipoDescontoEnum == null) {
            return null;
        }
        return tipoDescontoEnum.name();
    }

    @Override
    public TipoDescontoEnum convertToEntityAttribute(String s) {
        return TipoDescontoEnum.getTipoDesconto(s);
    }
}
