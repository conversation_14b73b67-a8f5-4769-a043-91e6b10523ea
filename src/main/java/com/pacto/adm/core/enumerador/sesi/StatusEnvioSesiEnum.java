package com.pacto.adm.core.enumerador.sesi;

public enum StatusEnvioSesiEnum {

    Enviado("EN", "Enviado para  o serviço do SESI"),
    <PERSON><PERSON><PERSON><PERSON>("RE", "Reenviado para  o serviço do SESI"),
    <PERSON><PERSON>o("SU", "OK recebido do serviço SESI"),
    FalhaPacto("FZ", "Falha de validação dos campos enviados"),
    FalhaSESI("FS", "Falha do serviço SESI");

    private String nome;
    private String descricao;

    StatusEnvioSesiEnum(final String nome, final String descricao) {
        this.nome = nome;
        this.descricao = descricao;
    }

    public static StatusEnvioSesiEnum obterConsultarPorCodigo(String nome) {
        for (StatusEnvioSesiEnum conf : values()) {
            if (conf.getNome().equals(nome)) {
                return conf;
            }
        }
        return null;
    }

    public String getNome() {
        return nome;
    }

    public String getDescricao() {
        return descricao;
    }

}
