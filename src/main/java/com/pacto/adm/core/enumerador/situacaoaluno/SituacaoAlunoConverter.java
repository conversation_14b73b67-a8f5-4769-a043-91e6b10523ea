package com.pacto.adm.core.enumerador.situacaoaluno;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class SituacaoAlunoConverter implements AttributeConverter<SituacaoDoAlunoEnum, String> {
    @Override
    public String convertToDatabaseColumn(SituacaoDoAlunoEnum tipoDescontoEnum) {
        if (tipoDescontoEnum == null) {
            return null;
        }
        return tipoDescontoEnum.getCodigo();
    }

    @Override
    public SituacaoDoAlunoEnum convertToEntityAttribute(String s) {
        return SituacaoDoAlunoEnum.getInstance(s);
    }
}
