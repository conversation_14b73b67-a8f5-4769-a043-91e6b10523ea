package com.pacto.adm.core.enumerador.tipodesconto;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR>
 */
public enum TipoDescontoEnum {
    NA(0, ""),
    PE(1, "Percentual"),
    VA(2, "Valor"),
    BO(3, "Bônus");

    private int codigo;
    private String descricao;

    TipoDescontoEnum(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoDescontoEnum getTipoDesconto(int codigo) {
        for (TipoDescontoEnum to : TipoDescontoEnum.values())
            if (to.getCodigo() == codigo)
                return to;
        return null;
    }

    public static TipoDescontoEnum getTipoDesconto(String name) {
        for (TipoDescontoEnum to : TipoDescontoEnum.values())
            if (to.name().equals(name))
                return to;
        return null;
    }

    @JsonValue
    public int getCodigo() {
        return this.codigo;
    }

    public String getDescricao() {
        return this.descricao;
    }

}
