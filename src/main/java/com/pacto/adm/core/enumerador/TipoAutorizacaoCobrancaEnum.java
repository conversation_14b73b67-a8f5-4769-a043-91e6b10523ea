package com.pacto.adm.core.enumerador;

public enum TipoAutorizacaoCobrancaEnum {
    NENHUM(0, "(Nenhum)"),
    CARTAOCREDITO(1, "Cartão de Crédito"),
    DEBITOCONTA(2, "Débito em Conta Corrente"),
    BOLETO_BANCARIO(3, "Boleto Bancário"),
    PIX(4, "Pix");

    private int id;
    private String descricao;

    TipoAutorizacaoCobrancaEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static TipoAutorizacaoCobrancaEnum valueOf(final int id) {
        TipoAutorizacaoCobrancaEnum[] lista = TipoAutorizacaoCobrancaEnum.values();
        for (TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum : lista) {
            if (id == tipoAutorizacaoCobrancaEnum.id) {
                return tipoAutorizacaoCobrancaEnum;
            }
        }
        return TipoAutorizacaoCobrancaEnum.NENHUM;
    }

    public String getDescricao() {
        return descricao;
    }
}
