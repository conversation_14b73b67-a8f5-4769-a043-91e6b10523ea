package com.pacto.adm.core.enumerador;

public enum TipoInfoMigracaoEnum {

//    IMPORTANTE
//    Esse mesmo ENUM existe nos seguintes projetos | ZW - Legado | ADM-CORE-MS | OAMD | ao alterar aqui deve ser alterado nos demais projetos

    NEGOCIACAO(1),
    CONTRATO_LANCADO(2),
    LISTA_PESSOAS(3),
    TELA_ALUNO(4),
    VENDA_AVULSA(5),
    CONFIGURACOES(6),
    CAIXA_ABERTO(7),
    INCLUIR_CLIENTE(8);

    Integer id;

    TipoInfoMigracaoEnum(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public static TipoInfoMigracaoEnum obter(String name) {
        for (TipoInfoMigracaoEnum tipo : values()) {
            if (tipo.name().equalsIgnoreCase(name)) {
                return tipo;
            }
        }
        return null;
    }

    public static TipoInfoMigracaoEnum obterId(Integer id) {
        for (TipoInfoMigracaoEnum tipo : values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return null;
    }
}
