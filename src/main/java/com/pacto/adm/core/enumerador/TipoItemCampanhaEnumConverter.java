package com.pacto.adm.core.enumerador;

import javax.persistence.AttributeConverter;

public class TipoItemCampanhaEnumConverter implements AttributeConverter<TipoItemCampanhaEnum, Integer> {

    @Override
    public Integer convertToDatabaseColumn(TipoItemCampanhaEnum tipoItemCampanhaEnum) {
        if (tipoItemCampanhaEnum == null) {
            return null;
        }
        return tipoItemCampanhaEnum.getCodigo();
    }

    @Override
    public TipoItemCampanhaEnum convertToEntityAttribute(Integer dbData) {
        return TipoItemCampanhaEnum.getTipo(dbData);
    }
}
