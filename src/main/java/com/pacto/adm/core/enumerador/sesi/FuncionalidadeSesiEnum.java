package com.pacto.adm.core.enumerador.sesi;

public enum FuncionalidadeSesiEnum {
    PESSOA("Pessoa", "PE", "PessoaService/inserirPessoaFisicaNacional", "/pessoa", "/pe-su", "/pe-fl"),
    NOTA_FISCAL("Nota Fiscal", "NF", "http://service.fiesc.com.br/notafiscal/notafiscal/v1/NotaFiscal/emitirNotaFiscalRequest", "/emitirNotaFiscal","/nf-su", "/nf-fl"),
    TESOURARIA_INTEGRACAO_DINHEIRO("Tesouraria Integracao Dinheiro", "TSID", "financeiro-tesouraria/1.0.0", "/movimentacao-simples","", ""),
    TESOURARIA_INTEGRACAO_CONSULTA("Tesouraria Integracao Consulta", "TSIC", "financeiro-tesouraria/1.0.0", "/movimentacao-simples","", ""),
    INTEGRACAO_PIX("Integracao PIX", "PX", "financeiro-tesouraria/1.0.0", "/movimentacao-simples","", ""),
    INTEGRACAO_CARTAO("Integracao Cartão", "PC", "MovimentacaoFinanceiraService/incluirTituloFinanceiro", "/integracaoCartao", "/pc-su", "/pc-fl"),
    INTEGRACAO_DEPOSITO("Integracao Deposito", "PD", "financeiro-tesouraria/1.0.0", "/transferencia-bancaria","", ""),
    ;
    private String nome;
    private String sigla;
    private String soapAction;
    private String servicoInclusao;
    private String callBackSucesso;
    private String callBackFalha;

    FuncionalidadeSesiEnum(String nome, String sigla, String soapAction, String servicoInclusao, String callSucesso, String callFalha) {
        this.nome = nome;
        this.sigla = sigla;
        this.soapAction = soapAction;
        this.servicoInclusao = servicoInclusao;
        this.callBackSucesso = callSucesso;
        this.callBackFalha = callFalha;
    }

    public String getNome() {
        return nome;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getSoapAction() {
        return soapAction;
    }

    public String getCallBackSucesso() {
        return callBackSucesso;
    }

    public String getCallBackFalha() {
        return callBackFalha;
    }

    public String getServicoInclusao() {
        return servicoInclusao;
    }

    public void setServicoInclusao(String servicoInclusao) {
        this.servicoInclusao = servicoInclusao;
    }

    public static FuncionalidadeSesiEnum obterConsultarSigla(String sigla) {
        for (FuncionalidadeSesiEnum conf : values()) {
            if (conf.getSigla().equals(sigla)) {
                return conf;
            }
        }
        return null;
    }
}

