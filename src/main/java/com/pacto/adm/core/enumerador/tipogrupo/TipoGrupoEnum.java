package com.pacto.adm.core.enumerador.tipogrupo;

import com.fasterxml.jackson.annotation.JsonValue;

public enum TipoGrupoEnum {
    FAMILIA("FA", "Em Familia"),
    GRUPO("GR", "Em grupo");

    private final String codigo;
    private final String descricao;

    TipoGrupoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    @JsonValue
    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoGrupoEnum getTipoDesconto(String codigo) {
        for (TipoGrupoEnum tipoGrupoEnum : TipoGrupoEnum.values())
            if (tipoGrupoEnum.getCodigo().equals(codigo))
                return tipoGrupoEnum;
        return null;
    }

}
