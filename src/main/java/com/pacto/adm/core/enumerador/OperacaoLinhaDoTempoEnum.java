package com.pacto.adm.core.enumerador;

import com.fasterxml.jackson.annotation.JsonValue;

public enum OperacaoLinhaDoTempoEnum {

    CONTRATO_OPERACAO_TS(1, "COO_TS", "Transferência Saída", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_AD(2, "COO_AD", "Alteração de Duração", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_BA(3, "COO_BA", "Bônus- Acréscimo de dias", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_BR(4, "COO_BR", "Bônus -Redução de dias", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_CR(5, "COO_CR", "Férias", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_TE(6, "COO_TE", "Transferência Entrada", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_CA(7, "COO_CA", "Cancelamento", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_AH(8, "COO_AH", "Alteração Horário", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_TR(9, "COO_TR", "Trancamento", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_TV(10, "COO_TV", "Trancamento Vencido", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_RT(11, "COO_RT", "Retorno Trancamento", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_IM(12, "COO_IM", "Incluir Modalidade", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_AM(13, "COO_AM", "Alterar Modalidade", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_EM(14, "COO_EM", "Excluir Modalidade", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_AC(15, "COO_AC", "Alteração Contrato", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_AT(16, "COO_AT", "Atestado", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_RA(17, "COO_RA", "Retorno de Atestado", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_LV(18, "COO_LV", "Liberar vaga", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_BC(19, "COO_BC", "Bônus Coletivo", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_TD(20, "COO_TD", "Transferência dos Direitos de uso", TipoLinhaDoTempoEnum.ADM),
    CONTRATO_OPERACAO_RD(21, "COO_RD", "Retorno dos Direitos de uso", TipoLinhaDoTempoEnum.ADM),

    HISTORICO_CONTATO_CS(22, "CRM_CS", "Contato SMS", TipoLinhaDoTempoEnum.CRM),
    HISTORICO_CONTATO_EM(23, "CRM_EM", "Contato E-mail", TipoLinhaDoTempoEnum.CRM),
    HISTORICO_CONTATO_LC(24, "CRM_LC", "Ligação sem contato", TipoLinhaDoTempoEnum.CRM),
    HISTORICO_CONTATO_PE(25, "CRM_PE", "Contato pessoal", TipoLinhaDoTempoEnum.CRM),
    HISTORICO_CONTATO_TE(26, "CRM_TE", "Contato Telefônico", TipoLinhaDoTempoEnum.CRM),
    HISTORICO_CONTATO_WA(27, "CRM_WA", "Contato WhatsApp", TipoLinhaDoTempoEnum.CRM),
    HISTORICO_CONTATO_AP(28, "CRM_AP", "Contato APP", TipoLinhaDoTempoEnum.CRM),

    TREINO_TREINOU(30, "TREINOU", "Treinou", TipoLinhaDoTempoEnum.TREINO),
    TREINO_AGENDAMENTO(31, "AGENDAMENTO", "Agendamento", TipoLinhaDoTempoEnum.TREINO),
    TREINO_REVISOU_TREINO(32, "REVISOU_TREINO", "Revisou treino", TipoLinhaDoTempoEnum.TREINO),
    TREINO_RENOVOU_TREINO(33, "RENOVOU_TREINO", "Renovou treino", TipoLinhaDoTempoEnum.TREINO),
    TREINO_ACABOU_TREINO(34, "ACABOU_TREINO", "Acabou treino", TipoLinhaDoTempoEnum.TREINO),
    TREINO_NOTIFICACAO(35, "NOTIFICACAO", "Notificação", TipoLinhaDoTempoEnum.TREINO),
    TREINO_MONTOU_TREINO(36, "MONTOU_TREINO", "Montou treino", TipoLinhaDoTempoEnum.TREINO),
    TREINO_MUDOU_DE_NIVEL(37, "MUDOU_DE_NIVEL", "Mudou de nivel", TipoLinhaDoTempoEnum.TREINO),
    TREINO_GANHOU_BADGE(38, "GANHOU_BADGE", "Ganhou badge", TipoLinhaDoTempoEnum.TREINO),
    TREINO_FEZ_AULA(39, "FEZ_AULA", "Fez aula", TipoLinhaDoTempoEnum.TREINO),
    TREINO_REALIZOU_AVALIACAO(40, "REALIZOU_AVALIACAO", "Realizou avaliação", TipoLinhaDoTempoEnum.TREINO),
    TREINO_REGISTROU_WOD(41, "REGISTROU_WOD", "Registrou Wod", TipoLinhaDoTempoEnum.TREINO),
    TREINO_AGENDOU_BOOKING_GYMPASS(42, "AGENDOU_BOOKING_GYMPASS", "Agendou Booking Gympass", TipoLinhaDoTempoEnum.TREINO),
    TREINO_ALTERACAO_AGENDAMENTO_SERVICOS(43, "ALTERACAO_AGENDAMENTO_SERVICOS", "Alteração agendamento serviços", TipoLinhaDoTempoEnum.TREINO),
    TREINO_AULA_DESMARCADA(44, "AULA_DESMARCADA", "Aula desmarcada", TipoLinhaDoTempoEnum.TREINO),

    PARCELA(45, "PARCELA", "Parcela", TipoLinhaDoTempoEnum.FINANCEIRO),
    PARCELA_CANCELADA(46, "PARCELA_CANCELADA", "Parcela Cancelada", TipoLinhaDoTempoEnum.FINANCEIRO),
    PARCELA_VENCIDA(47, "PARCELA_VENCIDA", "Parcela Venceu", TipoLinhaDoTempoEnum.FINANCEIRO),
    RECIBO(48, "RECIBO", "Pagamento", TipoLinhaDoTempoEnum.FINANCEIRO),

    HISTORICO_CONTRATO_RN(49, "HIS_RN", "Renovação", TipoLinhaDoTempoEnum.ADM),
    HISTORICO_CONTRATO_MA(50, "HIS_MA", "Matrícula", TipoLinhaDoTempoEnum.ADM),
    HISTORICO_CONTRATO_RE(51, "HIS_RE", "Rematrícula", TipoLinhaDoTempoEnum.ADM),
    HISTORICO_CONTRATO_DE(52, "HIS_DE", "Desistente", TipoLinhaDoTempoEnum.ADM),
    HISTORICO_CONTRATO_VE(53, "HIS_VE", "Vencido", TipoLinhaDoTempoEnum.ADM),

    ;


    OperacaoLinhaDoTempoEnum(Integer codigo, String sigla,
                             String descricao, TipoLinhaDoTempoEnum tipo) {
        this.codigo = codigo;
        this.sigla = sigla;
        this.descricao = descricao;
        this.tipo = tipo;
    }

    private Integer codigo;
    private String sigla;
    private String descricao;
    private TipoLinhaDoTempoEnum tipo;


    public static OperacaoLinhaDoTempoEnum getFromSigla(String sigla) {
        for (OperacaoLinhaDoTempoEnum obj : OperacaoLinhaDoTempoEnum.values()) {
            if (obj.getSigla().equalsIgnoreCase(sigla))
                return obj;
        }
        return null;
    }

    public static OperacaoLinhaDoTempoEnum getFromCodigo(Integer codigo) {
        for (OperacaoLinhaDoTempoEnum obj : OperacaoLinhaDoTempoEnum.values()) {
            if (obj.getCodigo().equals(codigo))
                return obj;
        }
        return null;
    }

    @JsonValue
    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public TipoLinhaDoTempoEnum getTipo() {
        return tipo;
    }
}
