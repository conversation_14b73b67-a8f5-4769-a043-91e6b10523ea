package com.pacto.adm.core.enumerador;

public enum SituacaoMovParcelaEnum {

    EA("EA", "Em aberto"),
    PG("PG", "Pago"),
    CA("CA", "Cancelado"),
    RG("RG", "Renegociado");

    private String codigo;
    private String descricao;

    SituacaoMovParcelaEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static SituacaoMovParcelaEnum obterPorCodigo(String codigo){
        return SituacaoMovParcelaEnum.valueOf(codigo);
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
