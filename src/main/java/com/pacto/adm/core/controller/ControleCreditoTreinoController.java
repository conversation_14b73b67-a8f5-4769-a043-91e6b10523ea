package com.pacto.adm.core.controller;


import com.pacto.adm.core.dto.enveloperesposta.creditotreino.EnvelopeRespostaListControleCreditoTreinoDTO;
import com.pacto.adm.core.dto.filtros.FiltroControleCreditoTreinoJSON;
import com.pacto.adm.core.services.interfaces.ControleCreditoTreinoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/controle-credito-treino")
public class ControleCreditoTreinoController {

    private final ControleCreditoTreinoService controleCreditoTreinoService;

    public ControleCreditoTreinoController(ControleCreditoTreinoService controleCreditoTreinoService) {
        this.controleCreditoTreinoService = controleCreditoTreinoService;
    }

    @Operation(
            summary = "Consultar crédito de treinos de um contrato",
            description = "Consulta os créditos de treinos de um contrato pelo códidgo dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "codContrato", required = true, description = "Código do contrato que será consultado", example = "3122"),
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca para créditos de treino.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>aulaMarcadaCreditoExtra:</strong> Define se deve filtrar apenas créditos extras relacionados a aulas marcadas.</li>" +
                                    "<li><strong>tipoOperacaoCreditoTreino:</strong> Tipo da operação do crédito de treino (veja a lista de valores possíveis abaixo).</li>" +
                                    "<li><strong>dataInicioOperacao:</strong> Data inicial para o período da operação do crédito (formato yyyy-MM-dd).</li>" +
                                    "<li><strong>dataFimOperacao:</strong> Data final para o período da operação do crédito (formato yyyy-MM-dd).</li>" +
                                    "</ul>" +
                                    "<br/><strong>Valores possíveis para tipoOperacaoCreditoTreino:</strong>" +
                                    "<ul>" +
                                    "<li>1: COMPRA</li>" +
                                    "<li>2: UTILIZAÇÃO</li>" +
                                    "<li>3: FALTA</li>" +
                                    "<li>4: MARCAÇÃO DE AULA</li>" +
                                    "<li>5: DESMARCAÇÃO DE AULA</li>" +
                                    "<li>6: REPOSIÇÃO DE AULA</li>" +
                                    "<li>7: AJUSTE MANUAL</li>" +
                                    "<li>8: TRANSFERÊNCIA SALDO</li>" +
                                    "<li>9: MANUTENÇÃO DE MODALIDADE</li>" +
                                    "<li>10: CANCELAMENTO DE CONTRATO</li>" +
                                    "<li>11: AJUSTE MENSAL</li>" +
                                    "<li>12: TRANSFERÊNCIA ENTRE ALUNOS (ORIGEM)</li>" +
                                    "<li>13: TRANSFERÊNCIA ENTRE ALUNOS (DESTINO)</li>" +
                                    "<li>'AJUSTE MANUAL POSITIVO': Ajuste manual com valor positivo</li>" +
                                    "<li>'AJUSTE MANUAL NEGATIVO': Ajuste manual com valor negativo</li>" +
                                    "<li>'TODAS': Todas as operações</li>" +
                                    "</ul>",
                            example = "{\"aulaMarcadaCreditoExtra\":true,\"tipoOperacaoCreditoTreino\":4,\"dataInicioOperacao\":\"2024-01-01\",\"dataFimOperacao\":\"2024-12-31\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = String.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListControleCreditoTreinoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListControleCreditoTreinoDTO.resposta)
                            )
                    )}

    )
    @GetMapping("/{codContrato}")
    public ResponseEntity<EnvelopeRespostaDTO> findByCodigoContrato(@PathVariable Integer codContrato, @RequestParam(value = "filters", required = false) JSONObject filtrosJson, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroControleCreditoTreinoJSON filtros = new FiltroControleCreditoTreinoJSON(filtrosJson);
            return ResponseEntityFactory.ok(controleCreditoTreinoService.findByCodigoContrato(codContrato, paginadorDTO, filtros), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
