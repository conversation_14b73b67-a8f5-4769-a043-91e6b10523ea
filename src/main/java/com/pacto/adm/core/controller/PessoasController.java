package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaBiometriaDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaImportacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.pagamento.EnvelopeRespostaMovParcelaDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaListContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaListMovPagamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.log.EnvelopeRespostaListLogDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaListMovProdutoDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioMovelDTO;
import com.pacto.adm.core.dto.filtros.FiltroMovProdutoJSON;
import com.pacto.adm.core.services.interfaces.*;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/pessoas")
public class PessoasController {

    @Autowired
    private ContratoService contratoService;
    @Autowired
    private MovPagamentoService movPagamentoService;
    @Autowired
    private MovParcelaService movParcelaService;
    @Autowired
    private MovProdutoService movProdutoService;
    @Autowired
    private PessoaService pessoaService;
    @Autowired
    private LogService logService;

    @Operation(
            summary = "Consultar as informações de uma pessoa pelo código dela",
            description = "Consulta as informações de uma pessoa pelo código dela.",
            tags = {"Pessoa"},
            parameters = {
                    @Parameter(name = "codPessoa", required = true, description = "Código da pessoa que será usada para obter as informações", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Pessoa encontrada com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPessoaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaPessoaDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/{codPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> pessoa(@PathVariable Integer codPessoa) {
        try {
            return ResponseEntityFactory.ok(pessoaService.obterPessoa(codPessoa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar os contratos de uma pessoa",
            description = "Consulta os contratos de uma pessoa.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "codPessoa", required = true, description = "Código da pessoa que se quer obter os contratos", example = "5"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta.<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>vigenciaAteAjustada:</strong> Ordena pela data de vigência ajustada</li>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código do contrato</li>" +
                                    "<li><strong>situacaoContrato:</strong> Ordena pela situação do contrato</li>" +
                                    "<li><strong>responsavelContrato:</strong> Ordena pelo responsável pelo contrato</li>" +
                                    "<li><strong>vigenciaDe:</strong> Ordena pela data de início da vigência</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação geral</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data de vigência ajustada.<br/><br/>",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Contratos encontrada com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListContratoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListContratoDTO.resposta)}
                            )
                    )}
    )
    @GetMapping("/{codPessoa}/contratos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllContratosByPessoa(@PathVariable Integer codPessoa, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(contratoService.findAllByCodPessoa(codPessoa, paginadorDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar os pagamentos de uma pessoa",
            description = "Consulta os pagamentos de uma pessoa pelo código dela",
            tags = {"Gestão de Vendas Online"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que se quer consultar os pagamentos", example = "5"),
                    @Parameter(name = "filters", description = "Filtros de busca, deve ser informado como um JSON. " +
                            "Filtros disponíveis: " +
                            "<ul>" +
                            "<li><strong>telaAluno:</strong>true ou false</li>" +
                            "<li><strong>buscarStatusConciliadora:</strong> true ou false</li>" +
                            "</ul>", example = "{\"telaAluno\":\"false\",\"buscarStatusConciliadora\":\"true\"}",
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta.<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo nome da empresa</li>" +
                                    "<li><strong>nomePessoaPagador:</strong> Ordena pelo nome do pagador</li>" +
                                    "<li><strong>reciboPagamento:</strong> Ordena pelo recibo de pagamento</li>" +
                                    "<li><strong>valor:</strong> Ordena pelo valor</li>" +
                                    "<li><strong>valorTotal:</strong> Ordena pelo valor total</li>" +
                                    "<li><strong>dataLancamento:</strong> Ordena pela data de lançamento</li>" +
                                    "<li><strong>formaPagamento:</strong> Ordena pela descrição da forma de pagamento</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data de lançamento.<br/><br/>",
                            example = "valorTotal,desc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Pagamentos encontrados com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovPagamentoDTO.class),
                            examples = @ExampleObject(
                                    name = "Resposta Status 200",
                                    value = EnvelopeRespostaListMovPagamentoDTO.resposta
                            ))
                    ),
            }
    )
    @GetMapping("/{codPessoa}/pagamentos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllPagamentosByPessoa(@PathVariable Integer codPessoa, @RequestParam(value = "filters", required = false) JSONObject filtros, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(movPagamentoService.findAllByCodPessoa(codPessoa, filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar as parcelas de uma pessoa",
            description = "Consulta as parcelas de uma pessoa.",
            tags = {"Gestão de Vendas Online"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que se quer consultar as parcelas", example = "5"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Parcelas encontradas com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaMovParcelaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaMovParcelaDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}/parcelas")
    public ResponseEntity<EnvelopeRespostaDTO> findAllParcelasByPessoa(@PathVariable Integer codPessoa, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(movParcelaService.findAllByCodPessoa(codPessoa, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar os produtos adquiridos de uma pessoa",
            description = "Consulta os produtos adquiridos de uma pessa",
            tags = {"Produto"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa", required = true, example = "5"),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca, deve ser informado como um JSON. " +
                                    "Filtros disponíveis: " +
                                    "<ul>" +
                                    "<li><strong>ignorarPlano:</strong> true ou false</li>" +
                                    "</ul>", example = "{\"ignorarPlano\":\"true\"}"
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Produtos encontrados com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovProdutoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListMovProdutoDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}/produtos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllProdutosByPessoa(@PathVariable Integer codPessoa, @RequestParam(value = "filters", required = false) String filtrosJson, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtros = (filtrosJson != null) ? new JSONObject(filtrosJson) : new JSONObject();
            FiltroMovProdutoJSON filtroMovProdutoJSON = new FiltroMovProdutoJSON(filtros);
            return ResponseEntityFactory.ok(movProdutoService.findAllByCodPessoa(codPessoa, filtroMovProdutoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar os logs relacionados a uma pessoa",
            description = "Consulta os logs relacionados a uma pessoa",
            tags = {"Logs"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa", required = true, example = "5"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Logs encontrados com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListLogDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListLogDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}/logs")
    public ResponseEntity<EnvelopeRespostaDTO> findAllLogsByPessoa(@PathVariable Integer codPessoa, @ParameterObject @Parameter(description = "Configurações de paginação da requisição") PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(logService.consultarPorPessoa(codPessoa, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar as movimentações de produtos vinculados a uma pessoa na tela do cliente",
            description = "Consulta as movimentações de produtos vinculados a uma pessoa na tela do cliente",
            tags = {"Produto"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa", required = true, example = "5"),
                    @Parameter(
                            name = "filters",
                            description = "Filtros disponíveis (informar como JSON): " +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Texto que será usado para a busca rápida</li>" +
                                    "<li><strong>searchTerm:</strong> Termo alternativo de busca (usado se o quicksearchValue não estiver preenchido)</li>" +
                                    "<li><strong>situacao:</strong> String</li>" +
                                    "<li><strong>produtosEstoque:</strong> true ou false</li>" +
                                    "<li><strong>produtosVencidos:</strong> true ou false</li>" +
                                    "<li><strong>produtoServico:</strong> true ou false</li>" +
                                    "<li><strong>todosOsProdutos:</strong> true ou false</li>" +
                                    "<li><strong>produtosComVencimento:</strong> true ou false</li>" +
                                    "<li><strong>ignorarPlano:</strong> true ou false</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"creatina\",\"situacao\":\"ativo\",\"produtosEstoque\":true,\"produtosVencidos\":false,\"produtoServico\":false,\"todosOsProdutos\":false,\"produtosComVencimento\":true,\"ignorarPlano\":false}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>contrato:</strong> Ordena pelo código do contrato</li>" +
                                    "<li><strong>descricao:</strong> Ordena pela descrição</li>" +
                                    "<li><strong>produto:</strong> Ordena pela descrição do produto</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo nome da empresa</li>" +
                                    "<li><strong>dataLancamento:</strong> Ordena pela data de lançamento</li>" +
                                    "<li><strong>precoUnitario:</strong> Ordena pelo preço unitário</li>" +
                                    "<li><strong>valorDesconto:</strong> Ordena pelo valor do desconto</li>" +
                                    "<li><strong>totalFinal:</strong> Ordena pelo valor final total</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação</li>" +
                                    "<li><strong>renovavelAutomaticamente:</strong> Ordena pelo status de renovação automática</li>" +
                                    "<li><strong>dataInicioVigencia:</strong> Ordena pela data de início da vigência</li>" +
                                    "<li><strong>dataFinalVigencia:</strong> Ordena pela data final da vigência</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "dataLancamento,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Produtos encontrados com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovProdutoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListMovProdutoDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}/produtos/tela-cliente")
    public ResponseEntity<EnvelopeRespostaDTO> consultarPorCodigoPessoaTelaCliente(@PathVariable Integer codPessoa, @RequestParam(value = "filters", required = false) JSONObject filtros, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroMovProdutoJSON filtroMovProdutoJSON = new FiltroMovProdutoJSON(filtros);
            return ResponseEntityFactory.ok(movProdutoService.consultarMovProdutosPorCodigoPessoa(codPessoa, filtroMovProdutoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Operation(
            summary = "Consulta o Usuário Móvel de uma pessoa",
            description = "Consulta o usuário móvel de uma pessoa",
            tags = {"Usuário (ADM)"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa", required = true, example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Usuário Móvel encontrados com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaUsuarioMovelDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaUsuarioMovelDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}/usuariomovel")
    public ResponseEntity<EnvelopeRespostaDTO> usuarioMovel(@PathVariable Integer codPessoa) {
        try {
            return ResponseEntityFactory.ok(pessoaService.obterUsuarioMovel(codPessoa));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("ERRO", e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar as biometrias cadastradas de uma pessoa",
            description = "Consulta as biometrias cadastradas de uma pessoa.",
            tags = {"Pessoa"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa", required = true, example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Biometrias encontradas com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaBiometriaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaBiometriaDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}/biometria")
    public ResponseEntity<EnvelopeRespostaDTO> biometria(@PathVariable Integer codPessoa) {
        try {
            return ResponseEntityFactory.ok(pessoaService.biometria(codPessoa));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("ERRO", e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar se uma pessoa foi importado",
            description = "Consulta se uma pessoa foi importada para o sistema de forma correta.",
            tags = {"Pessoa"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa", required = true, example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Biometrias encontradas com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaImportacaoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaImportacaoDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}/importacao")
    public ResponseEntity<EnvelopeRespostaDTO> importacao(@PathVariable Integer codPessoa) {
        try {
            return ResponseEntityFactory.ok(pessoaService.importacao(codPessoa));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("ERRO", e.getMessage());
        }
    }
}
