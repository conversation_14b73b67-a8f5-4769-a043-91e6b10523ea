package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.*;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaContadorReposicoesDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.*;
import com.pacto.adm.core.dto.enveloperesposta.acesso.coletor.EnvelopeRespostaListColetorDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.periodo.EnvelopeRespostaListPeriodoAcessoClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.aulas.reposicao.EnvelopeRespostaListReposicaoAulaColetivaDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.colaborador.EnvelopeRespostaListClienteColaboradoresDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.colaborador.EnvelopeRespostaListClienteColaboradoresImprimirDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.dadosauxiliares.EnvelopeRespostaClienteDadosAuxiliaresDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.dadospessoais.EnvelopeRespostaClienteDadosPessoaisDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.dadosplano.EnvelopeRespostaClienteDadosPlanoDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.gympass.EnvelopeRespostaClienteDadosTotalPassDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.gympass.EnvelopeRespostaDadosGymPassDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.linhadotempo.EnvelopeRespostaListLinhaDoTempoDiaDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.listagem.EnvelopeRespostaListClienteListagemDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios.EnvelopeRespostaListQuestionarioClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios.EnvelopeRespostaQuestionarioClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.simplificado.EnvelopeRespostaListClienteSimplificadoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.linhadotempo.EnvelopeRespostaListLinhaDoTempoContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.log.EnvelopeRespostaListLogDTO;
import com.pacto.adm.core.dto.enveloperesposta.log.totalpass.EnvelopeRespostaListLogTotalPassDTO;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaBoolan;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaDouble;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaInteger;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroLinhaDoTempoJSON;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroPessoaJSON;
import com.pacto.adm.core.dto.filtros.bi.FiltroBIDTO;
import com.pacto.adm.core.services.interfaces.*;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/clientes")
public class ClienteController {

    @Autowired
    private ClienteService clienteService;

    @Autowired
    private PeriodoAcessoClienteService periodoAcessoClienteService;

    @Autowired
    private AulaDesmarcadaService aulaDesmarcadaService;

    @Autowired
    private LogTotalPassService logTotalPassService;

    @Autowired
    private RegistroDeAcessoClienteService registroDeAcessoClienteService;

    @Autowired
    private QuestionarioClienteService questionarioClienteService;
    @Autowired
    private RequestService requestService;


    @Operation(
            summary = "Consultar dados pessoais de um cliente pela matrícula",
            description = "Retorna as informações pessoais de um cliente, como nome, cpf, telefones e emails," +
                    " utilizando o código de matrícula como chave de busca",
            tags = {"Clientes"},
            parameters = {@Parameter(name = "matricula", description = "Código da matrícula do cliente que será consultado", example = "1810", required = true)},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaClienteDadosPessoaisDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteDadosPessoaisDTO.resposta)}
                            )),

            }
    )
    @GetMapping("/{matricula}/dados-pessoais")
    public ResponseEntity<EnvelopeRespostaDTO> findByMatricula(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteService.dadosPessoais(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar parcela de um cliente que foi incluída no SPC",
            description = "Retorna a data de inclusão e a parcela de um cliente que foi incluída no SPC.",
            tags = {"Gestão de Negativações"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaClienteDadosAuxiliaresDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteDadosAuxiliaresDTO.resposta)}
                            )),

            },
            parameters = {
                    @Parameter(
                            name = "matricula", description = "Código da matrícula do cliente que será consultado",
                            example = "1810", required = true
                    )
            }
    )
    @GetMapping("/{matricula}/dados-auxiliares")
    public ResponseEntity<EnvelopeRespostaDTO> dadosAuxiliares(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteService.dadosAuxiliares(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar se um cliente possui Total Pass",
            description = "Retorna se um cliente possui Total Pass. Se possuir, o valor do content será True, se não, será False.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaBoolan.resposta)}
                            )),
            },
            parameters = {@Parameter(name = "codpessoa", description = "Código da pessoa que será consultada", required = true, example = "22")}
    )
    @GetMapping("/{codpessoa}/dados-totalpass")
    public ResponseEntity<EnvelopeRespostaDTO> findByCodigoPessoa(@PathVariable Integer codpessoa) {
        try {
            return ResponseEntityFactory.ok(logTotalPassService.consultaLogTotalPassPorPessoa(codpessoa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar dados pessoais de um cliente pelo código de pessoa",
            description = "Retorna as informações pessoais de um cliente, como nome, cpf, telefones e emails," +
                    " utilizando o código de pessoa como chave de busca.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaClienteDadosPessoaisDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteDadosPessoaisDTO.resposta)}
                            )),

            },
            parameters = {@Parameter(name = "codpessoa", description = "Código da pessoa que será consultada", required = true, example = "22")}
    )
    @GetMapping("/dados-clientes/{codpessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> dadosClientes(@PathVariable Integer codpessoa) {
        try {
            return ResponseEntityFactory.ok(clienteService.dadosPessoaisPorPessoa(codpessoa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar total de pontos de uma pessoa",
            description = "Retorna o total de pontos de uma pessoa, usando como chave de busca o código de matrícula dela.",
            tags = {"Clube de Vantagens"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaInteger.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaInteger.resposta)}
                            )),
            },
            parameters = {
                    @Parameter(
                            name = "matricula", description = "Código da matrícula do cliente que será consultado",
                            example = "1810", required = true
                    )
            }
    )
    @GetMapping("/{matricula}/total-pontos")
    public ResponseEntity<EnvelopeRespostaDTO> totalPontos(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteService.obterTotalPontos(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar saldo da conta corrente com código da matrícula",
            description = "Retorna o saldo da conta corrente usando como chave de busca o código de matrícula.",
            tags = {"Saldo Conta Corrente"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaDouble.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaDouble.resposta)}
                            )),

            },
            parameters = {
                    @Parameter(
                            name = "matricula", description = "Código da matrícula do cliente que será consultado",
                            example = "1810", required = true
                    )
            }
    )
    @GetMapping("/{matricula}/saldo-conta-corrente")
    public ResponseEntity<EnvelopeRespostaDTO> saldoContaCorrente(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteService.obterSaldoContaCorrente(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar dados do plano de um cliente",
            description = "Retorna as informações do plano de um cliente usando como chave de busca o código de matrícula do cliente.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaClienteDadosPlanoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteDadosPlanoDTO.resposta)}
                            )),
            },
            parameters = {
                    @Parameter(
                            name = "matricula", description = "Código da matrícula do cliente que será consultado",
                            example = "1810", required = true
                    )
            }
    )
    @GetMapping("/{matricula}/dados-plano")
    public ResponseEntity<EnvelopeRespostaDTO> dadosPlano(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteService.dadosPlano(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar clientes",
            description = "Lista todos os clientes da academia ordenados pelo nome. <br/> É possível filtrar a resposta pelo nome.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteListagemDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteListagemDTO.resposta)}
                            )),
            },
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca.<br/>" +
                                    "<br/><strong>Filtro disponível</strong>" +
                                    "<ul>" +
                                    "<li>" +
                                    "<strong>quicksearchValue:</strong> Termo utilizado para filtrar registro." +
                                    "</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"Roberto\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class))
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findByPessoa(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroClienteJSON filtroClienteJSON = null;
            if (filtros != null) {
                filtroClienteJSON = new FiltroClienteJSON(filtros);
            }
            return ResponseEntityFactory.ok(clienteService.listaClientes(filtroClienteJSON, paginadorDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar clientes pela tela do colaborador",
            description = "Consulta os clientes pela tela do colaborador, sendo possível fazer alguns filtros detalhados e trazer informações mais completas.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteColaboradoresDTO.class),
                                    examples = {
                                            @ExampleObject(name = "Exemplo Resposta 200 - Imprimir true", value = EnvelopeRespostaListClienteColaboradoresImprimirDTO.resposta),
                                            @ExampleObject(name = "Exemplo Resposta 200 - Imprimir false", value = EnvelopeRespostaListClienteColaboradoresDTO.resposta)
                                    }
                            )),
            },
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para filtrar os clientes quando o parâmetro empresas for false",
                            required = false,
                            example = "1",
                            in = ParameterIn.HEADER
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca. Deve ser informado como um JSON<br/>" +
                                    "<br/><strong>Filtros disponíveis:</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtro genérico.</li>" +
                                    "<li><strong>rg:</strong> Registro Geral.</li>" +
                                    "<li><strong>cpf:</strong> CPF do indivíduo.</li>" +
                                    "<li><strong>matricula:</strong> Matrícula do aluno.</li>" +
                                    "<li><strong>treino:</strong> Nome do treino.</li>" +
                                    "<li><strong>contrato:</strong> Código do contrato.</li>" +
                                    "<li><strong>pessoa:</strong> Código da pessoa.</li>" +
                                    "<li><strong>cliente:</strong> Código do cliente.</li>" +
                                    "<li><strong>colaborador:</strong> Código do colaborador.</li>" +
                                    "<li><strong>passaporte:</strong> Número do passaporte.</li>" +
                                    "<li><strong>placa:</strong> Placa do veículo.</li>" +
                                    "<li><strong>rne:</strong> Registro Nacional de Estrangeiro.</li>" +
                                    "<li><strong>telefone:</strong> Número de telefone.</li>" +
                                    "<li><strong>email:</strong> Endereço de e-mail.</li>" +
                                    "<li><strong>codigoAcesso:</strong> Código de acesso.</li>" +
                                    "<li><strong>codigoDeAcessoAlternativo:</strong> Código de acesso alternativo.</li>" +
                                    "<li><strong>responsavel:</strong> Nome do responsável.</li>" +
                                    "<li><strong>cpfresponsavel:</strong> CPF do responsável.</li>" +
                                    "<li><strong>tipoDeConsulta:</strong> Tipo da consulta realizada.</li>" +
                                    "<li><strong>grupos:</strong> Lista de códigos de grupo.</li>" +
                                    "<li><strong>profissoes:</strong> Lista de códigos de profissão.</li>" +
                                    "<li><strong>classificacoes:</strong> Lista de códigos de classificação.</li>" +
                                    "<li><strong>consultores:</strong> Lista de códigos de consultores.</li>" +
                                    "<li><strong>professores:</strong> Lista de códigos de professores.</li>" +
                                    "<li><strong>empresas:</strong> Lista de códigos de empresas.</li>" +
                                    "<li><strong>situacoes:</strong> Lista de situações (strings).</li>" +
                                    "<li><strong>tipos:</strong> Lista de códigos de tipos.</li>" +
                                    "<li><strong>categorias:</strong> Lista de códigos de categorias.</li>" +
                                    "<li><strong>niveis:</strong> Lista de códigos de níveis.</li>" +
                                    "</ul>",
                            example = "{\"cpf\": \"12345678900\",\n\"contrato\": 1010\n}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "carteiras", description = "Indica se deve visualizar as carteiras na resposta", example = "false"),
                    @Parameter(name = "empresas", description = "Inidica se deve retornar os dados completos da empresa", example = "false"),
                    @Parameter(name = "imprimir", description = "Indica se deve retornar os dados no formato de impressão (Nesse formato " +
                            "retorna os dados de forma mais simples e agrupados)", example = "true"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as resposta por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data de cadastro do aluguel<br/><br/>",
                            example = "nome,asc", schema = @Schema(implementation = String.class)
                    )
            }
    )
    @GetMapping("/alunoColaborador")
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosColaborador(
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @RequestParam(required = false) Boolean carteiras,
            @RequestParam(required = false) Boolean empresas,
            @RequestParam(required = false) Boolean imprimir,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) throws JSONException {
        try {
            FiltroPessoaJSON filtroAlunoJSON = new FiltroPessoaJSON(filtros);
            filtroAlunoJSON.setVisualizarOutrasCarteiras(carteiras != null && carteiras);
            if (empresas != null && !empresas) {
                filtroAlunoJSON.setEmpresas(new ArrayList<>());
                filtroAlunoJSON.getEmpresas().add(this.requestService.getEmpresaId());
            }
            if (imprimir != null && imprimir) {
                return ResponseEntityFactory.ok(clienteService.listaAlunosColaboradoresImprimir(filtroAlunoJSON, paginadorDTO), paginadorDTO);
            } else {
                return ResponseEntityFactory.ok(clienteService.listaAlunosColaboradores(filtroAlunoJSON, paginadorDTO), paginadorDTO);
            }
        } catch (ServiceException e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Operation(
            summary = "Cadastrar senha de acesso à academia",
            description = "Cadastra uma senha de acesso para um cliente.",
            tags = {"Clientes"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SenhaDeAcessoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaSenhaAcesso.requestBody)}
                    )
            ),

            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaSenhaAcesso.class)
                            )),

            }
    )
    @PostMapping("/senha-de-acesso")
    public ResponseEntity<EnvelopeRespostaDTO> salvarSenhaDeAcesso(@RequestBody SenhaDeAcessoDTO senhaDeAcessoDTO) {
        try {
            return ResponseEntityFactory.ok(clienteService.salvaSenhaDeAcesso(senhaDeAcessoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(
            summary = "Cadastrar acesso à academia de forma manual",
            description = "Cadastra um acesso à academia de forma manual.",
            tags = {"Registrar Acesso Manual"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para cadastrar um acesso de forma manual",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AcessoClienteDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaAcessoClienteDTO.requestBody)}
                    )
            ),

            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAcessoCadastrado.class)
                            )),
            }
    )
    @PostMapping("/registro-acesso-manual")
    public ResponseEntity<EnvelopeRespostaDTO> registroAcessoManual(@RequestBody AcessoClienteDTO registroDeAcessoManual) {
        try {
            List<AcessoClienteDTO> acessos = registroDeAcessoClienteService.listarRegistraAcessoManualDia(registroDeAcessoManual);
            if (!acessos.isEmpty()) {
                return ResponseEntityFactory.ok(acessos);
            } else {
                return ResponseEntityFactory.ok(registroDeAcessoClienteService.registroDeAcessoManual(registroDeAcessoManual));
            }
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(
            summary = "Cadastrar acesso à academia no mesmo dia de forma manual",
            description = "Cadastra um acesso à academia no mesmo dia de forma manual.",
            tags = {"Registrar Acesso Manual"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo de requisição para cadastrar um acesso à academia",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AcessoClienteDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Body da Requisição", value = EnvelopeRespostaAcessoClienteDTO.requestBody)}
                    ),
                    useParameterTypeSchema = true),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAcessoCadastrado.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaAcessoCadastrado.resposta)}
                            )),
            }
    )
    @PostMapping("/registro-acesso-manual-mesmo-dia")
    public ResponseEntity<EnvelopeRespostaDTO> registroAcessoManualMesmoDia(@RequestBody AcessoClienteDTO registroDeAcessoManual) {
        try {
            return ResponseEntityFactory.ok(registroDeAcessoClienteService.registroDeAcessoManual(registroDeAcessoManual));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(
            summary = "Editar acesso à academia de forma manual",
            description = "Editar um acesso à academia de forma manual.",
            tags = {"Registrar Acesso Manual"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo de requisição para alterar um acesso à academia",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AcessoClienteDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Body da Requisição", value = EnvelopeRespostaAcessoClienteDTO.requestBody)}
                    ),
                    useParameterTypeSchema = true),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAcessoEditado.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaAcessoEditado.resposta)}
                            )),
            }
    )
    @PostMapping("/editar-registro-acesso-manual")
    public ResponseEntity<EnvelopeRespostaDTO> editarRegistroAcessoManual(@RequestBody AcessoClienteDTO registroDeAcessoManual) {
        try {
            return ResponseEntityFactory.ok(registroDeAcessoClienteService.editarAcessoCliente(registroDeAcessoManual));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(
            summary = "Iniciar registro de acesso à academia de forma manual",
            description = "Inicia o registro de acesso à academia de forma manual, " +
                    "buscando os dados do cliente que são necessários para o registro através do código da matrícula dele.",
            tags = {"Registrar Acesso Manual"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaInicioDadosAcessoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaInicioDadosAcessoDTO.resposta)}
                            )),

            },
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para buscar os locais de acesso disponíveis",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    ),
                    @Parameter(name = "matricula", description = "Código da matrícula do cliente que será consultado", example = "1810", required = true)
            }
    )
    @GetMapping("/inicio-registro-acesso-manual/{matricula}")
    public ResponseEntity<EnvelopeRespostaDTO> IniciarDadosRegistroDeAcesso(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(registroDeAcessoClienteService.iniciaDadosRegistroDeAcessoCliente(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(
            summary = "Consultar coletores de acesso por local",
            description = "Consulta as informações dos coletores de acesso por local.",
            tags = {"Local de Acesso"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListColetorDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListColetorDTO.resposta)}
                            )),

            },
            parameters = {
                    @Parameter(name = "localacesso", description = "Código do local de acesso que se quer consultar os coletores",
                            example = "1", required = true),
                    @Parameter(name = "desativado", description = "Indica se a requisição é para encontrar coletores ativos ou desativados. Caso queira buscar coletores desativados" +
                            "Coloque como parêmetro da requisição o valor True.", example = "false", required = true)
            }
    )
    @GetMapping("/busca-coletor/{localacesso}/{desativado}")
    public ResponseEntity<EnvelopeRespostaDTO> buscarColetor(@PathVariable Integer localacesso, @PathVariable boolean desativado) {
        try {
            return ResponseEntityFactory.ok(registroDeAcessoClienteService.buscaColetor(localacesso, desativado));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(
            summary = "Consultar os registros de acesso de um cliente pelo código da matrícula",
            description = "Consulta os registros de acesso à academia feitos por um cliente através do código de matrícula dele.",
            tags = {"Lista de Acessos"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListAcessoClienteDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListAcessoClienteDTO.resposta)}
                            )),
            }
    )
    @GetMapping("/listar-registro-de-acesso/{matricula}")
    public ResponseEntity<EnvelopeRespostaDTO> buscarColetors(@RequestBody AcessoClienteDTO acessoClienteDTO) {
        try {
            return ResponseEntityFactory.ok(registroDeAcessoClienteService.listarAcessoClienteDTO(acessoClienteDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(
            summary = "Consultar os dados do GymPass do cliente",
            description = "Consulta os dados do GymPass do cliente através do código de matrícula dele.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaDadosGymPassDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaDadosGymPassDTO.resposta)}
                            )),
            },
            parameters = {@Parameter(name = "matricula", description = "Código da matrícula do cliente que será consultado", example = "1810", required = true)}
    )
    @GetMapping("/{matricula}/dados-gympass")
    public ResponseEntity<EnvelopeRespostaDTO> dadosGymPass(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteService.dadosGymPass(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Cadastrar dados do GymPass do cliente",
            description = "Cadastra os dados do GymPass do cliente através do código de matrícula dele.",
            tags = {"Clientes"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ClienteDadosGymPassDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaDadosGymPassDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            },
            parameters = {@Parameter(name = "matricula", description = "Código da matrícula do cliente", example = "1810", required = true)}
    )
    @PostMapping("/{matricula}/dados-gympass")
    public ResponseEntity<EnvelopeRespostaDTO> salvaDadosGymPass(
            @PathVariable Integer matricula, @RequestBody ClienteDadosGymPassDTO clienteDadosGymPassDTO) {
        try {
            clienteService.salvaDadosGymPass(matricula, clienteDadosGymPassDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Cadastrar dados de Churn de um cliente",
            description = "Cadastra dados de Churn de um cliente. O Churn são métricas que indicam a probabilidade do cliente deixar a academia ou parar de utilizar alguns dos serviços oferecidos.",
            tags = {"BI Administrativo"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar dados churn do cliente.<br/>",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ClienteDadosPessoaisDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteDadosPessoaisDTO.requestBody)}
                    ),
                    useParameterTypeSchema = true),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            }
    )
    @PostMapping("/dados-churn")
    public ResponseEntity<EnvelopeRespostaDTO> salvaDadosChurn(@RequestBody ClienteDadosPessoaisDTO cliente) {
        try {
            clienteService.salvaDadosChurn(cliente);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Deletar dados do GymPass de um cliente",
            description = "Deleta os dados do GymPass de um cliente através do código de matrícula dele.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = @ExampleObject(name = "Resposta 200 (Sem corpo de resposta)", value = "")
                            )
                    )
            },
            parameters = {
                    @Parameter(
                            name = "matricula", description = "Código da matrícula do cliente que terá os dados do GymPass removido",
                            example = "1810", required = true
                    )
            }
    )
    @DeleteMapping("/{matricula}/dados-gympass")
    public ResponseEntity<EnvelopeRespostaDTO> deletaDadosGymPass(@PathVariable Integer matricula) {
        try {
            clienteService.deletaDadosGymPass(matricula);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar histórico de acessos à academia via GymPass",
            description = "Consulta o histórico de acessos de uma pessoa à academia realizaodos com GymPass.",
            tags = {"Lista de Acessos"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPeriodoAcessoClienteDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListPeriodoAcessoClienteDTO.resposta)}
                            )
                    )
            },
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que será consultada", required = true, example = "22"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as resposta por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>dataInicioAcesso:</strong> Ordena pela data de início do acesso</li>" +
                                    "<li><strong>tokenGympass:</strong> Ordena pelo token GymPass do cliente</li>" +
                                    "<li><strong>valorGympass:</strong> Ordena pelo valor do GymPass do cliente</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "dataInicioAcesso,asc",
                            schema = @Schema(implementation = String.class)
                    )
            }
    )
    @GetMapping("/{codPessoa}/historico-acessos-gympass")
    public ResponseEntity<EnvelopeRespostaDTO> historicoAcessosGymPass(@PathVariable Integer codPessoa, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(periodoAcessoClienteService.consultaPeriodoAcesso(codPessoa, true, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar histórico de acessos à academia via GoGood",
            description = "Consulta o histórico de acessos de uma pessoa à academia realizados com GoGood.",
            tags = {"Lista de Acessos"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPeriodoAcessoClienteDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListPeriodoAcessoClienteDTO.resposta)}
                            )
                    ),

            },
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que será consultada", required = true, example = "22"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as resposta por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>dataInicio:</strong> Ordena pela data de início do acesso</li>" +
                                    "<li><strong>tokenGogood:</strong> Ordena pelo token GoGood do cliente</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "dataInicio,asc",
                            schema = @Schema(implementation = String.class)
                    )
            }
    )
    @GetMapping("/{codPessoa}/historico-acessos-gogood")
    public ResponseEntity<EnvelopeRespostaDTO> historicoAcessosGoGood(@PathVariable Integer codPessoa, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(periodoAcessoClienteService.consultaPeriodoAcessoGoGood(codPessoa, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar histórico de acessos à academia via Total Pass",
            description = "Consulta o histórico de acessos de uma pessoa à academia realizaodos com TotalPass.",
            tags = {"Lista de Acessos"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListLogTotalPassDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListLogTotalPassDTO.resposta)}
                            )
                    ),

            },
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que será consultada", required = true, example = "22"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as resposta por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>origem:</strong> Ordena pela origem</li>" +
                                    "<li><strong>dataregistro:</strong> Ordena pela data de registro do acesso</li>" +
                                    "<li><strong>usuario:</strong> Ordena pelo usuário</li>" +
                                    "<li><strong>tempoResposta:</strong> Ordena pelo tempo de resposta</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data de registro.<br/><br/>",
                            example = "dataInicio,asc",
                            schema = @Schema(implementation = String.class)
                    )
            }
    )
    @GetMapping("/{codPessoa}/historico-acessos-totalpass")
    public ResponseEntity<EnvelopeRespostaDTO> historicoAcessosTotalPass(@PathVariable Integer codPessoa, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(logTotalPassService.consultaLogTotalPass(codPessoa, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Autorizar acesso à academia via GymPass",
            description = "Autoriza o acesso de um cliente à academia com GymPass.",
            tags = {"Autorização de Acesso"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para autorizar acesso via GymPass",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ClienteDadosGymPassDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaDadosGymPassDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAcessoGymPass.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaAcessoGymPass.resposta)}
                            )
                    ),

            },
            parameters = {@Parameter(name = "matricula", description = "Código da matricula do cliente", required = true, example = "1810")}
    )
    @PostMapping("/{matricula}/autoriza-acesso-gympass")
    public ResponseEntity<EnvelopeRespostaDTO> autorizaAcessoGymPass(@PathVariable Integer matricula, @RequestBody ClienteDadosGymPassDTO clienteDadosGymPassDTO) {
        try {
            return ResponseEntityFactory.ok(clienteService.autorizaAcessoGymPass(matricula, clienteDadosGymPassDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Autorizar acesso à academia via Total Pass",
            description = "Autoriza o acesso de um cliente à academia com Total Pass.",
            tags = {"Autorização de Acesso"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para autorizar o acesso via Total Pass",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ClienteDadosTotalPassDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Body da Requisição", value = EnvelopeRespostaClienteDadosTotalPassDTO.requestBody)}
                    ),
                    useParameterTypeSchema = true),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAcessoTotalPass.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaAcessoTotalPass.resposta)}
                            )
                    ),

            }
    )
    @PostMapping("/autoriza-acesso-totalpass")
    public ResponseEntity<EnvelopeRespostaDTO> autorizaAcessoTotalPass(@RequestBody ClienteDadosTotalPassDTO clienteDadosTotalPassDTO) {
        try {
            return ResponseEntityFactory.ok(clienteService.autorizaAcessoTotalPass(clienteDadosTotalPassDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(
            summary = "Consultar histórico de Boletim de Visitas (BV) do cliente",
            description = "Consulta o histórico de Boletim de Visitas (BV) do cliente através do código dele.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListQuestionarioClienteDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListQuestionarioClienteDTO.resposta)}
                            )
                    ),
            },
            parameters = {
                    @Parameter(name = "codCliente", description = "Código do cliente que será consultado", required = true, example = "22"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as resposta por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo nome interno do questionário</li>" +
                                    "<li><strong>data:</strong> Ordena pela data do questionário</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data.<br/><br/>",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            }
    )
    @GetMapping("/{codCliente}/historico-bvs")
    public ResponseEntity<EnvelopeRespostaDTO> historicoBVs(@PathVariable Integer codCliente, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(questionarioClienteService.consultaHistoricoBVs(codCliente, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Alterar um Boletim de Visitas (BV)",
            description = "Alterar um Boletim de Visitas (BV).",
            tags = {"Clientes"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para alterar um BV.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = QuestionarioClienteDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaQuestionarioClienteDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaQuestionarioClienteDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaQuestionarioClienteDTO.resposta)}
                            )
                    ),

            }
    )
    @PostMapping("/historico-bvs")
    public ResponseEntity<EnvelopeRespostaDTO> editarBVs(@RequestBody QuestionarioClienteDTO questionarioClienteDTO) {
        try {

            return ResponseEntityFactory.ok(questionarioClienteService.editarBVs(questionarioClienteDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar os logs do sistema",
            description = "Consulta os logs do sistema utilizando o código do cliente, código da pessoa e código do cliente no Treino. Os filtros são cumulativos," +
                    " ou seja, os resultados incluem todos os registros que atendem a qualquer um dos códigos informados.",
            tags = {"Logs"},
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para consultar logs de APIs externas de treino",
                            required = false,
                            example = "1",
                            in = ParameterIn.HEADER
                    ),
                    @Parameter(name = "codCliente", description = "Código do cliente que será utilizado na  busca dos logs"),
                    @Parameter(name = "codigoPessoa", description = "Código da pessoa que será utilizado na busca dos logs"),
                    @Parameter(name = "codClienteTreino", description = "Código do cliente no treino"),
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca.<br/>" +
                                    "<br/><strong>Filtro disponível</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Termo utilizado para filtrar registros.</li>" +
                                    "<li><strong>tipo:</strong> Filtra pelo tipo do log. (INSERT, UPDATE ou DELETE)</li>" +
                                    "<li><strong>dataInicio: Data de início do filtro (Informar em ISO 8601)</strong></li>" +
                                    "<li><strong>dataFim: Data de fim do filtro (Informar em ISO 8601)</strong></li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"cliente123\",\"tipo\":[\"INSERT\",\"UPDATE\",\"DELETE\"],\"dataInicio\":\"2025-04-01T00:00:00Z\",\"dataFim\":\"2025-04-30T23:59:59Z\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListLogDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListLogDTO.resposta)}
                            )
                    ),

            }
    )
    @GetMapping("/logs/{codCliente}/{codigoPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> buscarLogs(@PathVariable Integer codCliente,
                                                          @PathVariable Integer codigoPessoa,
                                                          @RequestParam(required = false) Integer codClienteTreino,
                                                          @Parameter(hidden = true) PaginadorDTO paginadorDTO,
                                                          @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroLogClienteJSON filters = new FiltroLogClienteJSON(filtros);
            return ResponseEntityFactory.ok(clienteService.buscarLogs(codCliente, codigoPessoa, codClienteTreino, paginadorDTO, filters), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar período de acesso de uma pesssoa",
            description = "Consulta o período de acesso de uma pessoa através do código dela.",
            tags = {"Lista de Acessos"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPeriodoAcessoClienteDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Status 200", value = EnvelopeRespostaListPeriodoAcessoClienteDTO.resposta)}
                            )
                    ),

            },
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que será utilizado na busca", example = "1"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as resposta por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>dataInicioAcesso:</strong> Ordena pela data de início do acesso</li>" +
                                    "<li><strong>tokenGympass:</strong> Ordena pelo token GymPass do cliente</li>" +
                                    "<li><strong>valorGympass:</strong> Ordena pelo valor do GymPass do cliente</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "dataInicioAcesso,asc",
                            schema = @Schema(implementation = String.class)
                    )
            }
    )
    @GetMapping("/{codPessoa}/periodo-acesso")
    public ResponseEntity<EnvelopeRespostaDTO> historicoAcessos(@PathVariable Integer codPessoa, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {

            return ResponseEntityFactory.ok(periodoAcessoClienteService.consultaPeriodoAcesso(codPessoa, false, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar linha do tempo de um cliente",
            description = "Consulta a linha do tempo de um cliente através do código de matrícula dele.",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListLinhaDoTempoDiaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListLinhaDoTempoDiaDTO.resposta)}
                            )
                    ),
            },
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para consultar dados de treino na linha do tempo",
                            required = false,
                            example = "1",
                            in = ParameterIn.HEADER
                    ),
                    @Parameter(name = "matricula", description = "Código da pessoa que será utilizado na busca"),
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>contrato:</strong> Código do contrato associado.</li>" +
                                    "<li><strong>tipo:</strong> Tipo da operação. (999 = Todos, 1 = Adm, 2 = Financeiro, 3 = CRM, 4 = Treino)</li>" +
                                    "<li><strong>dtInicio:</strong> Data de início do filtro (timestamp em milissegundos).</li>" +
                                    "<li><strong>dtFim:</strong> Data de fim do filtro (timestamp em milissegundos).</li>" +
                                    "<li><strong>operacao:</strong> Lista de códigos das operações.</li>" +
                                    "</ul>" +
                                    "Os filtros são cumulativos, ou seja, os resultados incluem todos os registros que atendem a qualquer um dos filtros informados.",
                            example = "{\"contrato\":67890,\"tipo\":999,\"dtInicio\":1714435200000,\"dtFim\":1717027199000,\"operacao\":[1,2,3]}",
                            schema = @Schema(implementation = String.class)
                    )
            }
    )
    @GetMapping("/{matricula}/linha-tempo")
    public ResponseEntity<EnvelopeRespostaDTO> linhaDoTempo(@PathVariable Integer matricula, @RequestParam(value = "filters", required = false) String filtros) {
        try {
            FiltroLinhaDoTempoJSON filtroLinhaDoTempoJSON = new FiltroLinhaDoTempoJSON(null);
            try {
                filtroLinhaDoTempoJSON = new FiltroLinhaDoTempoJSON(new JSONObject(filtros));
                ;
            } catch (Exception ignored) {
            }
            return ResponseEntityFactory.ok(clienteService.linhaDoTempo(matricula, filtroLinhaDoTempoJSON));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar dados dos clientes simplificado",
            description = "Consulta dados dos clientes de forma simples. É possível filtrar pelo nome do cliente",
            tags = {"Lista de Clientes Simplificada"},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca.<br/>" +
                                    "<br/><strong>Filtro disponível</strong>" +
                                    "<ul><li><strong>quicksearchValue:</strong> Termo utilizado para filtrar registros pelo nome.</li></ul>",
                            example = "{\"quicksearchValue\":\"Roberto\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class))
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteListagemDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteListagemDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("simples")
    public ResponseEntity<EnvelopeRespostaDTO> findBySimples(@RequestParam(value = "filters", required = false) String filtros,
                                                             @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroClienteJSON filtroClienteJSON = null;
            if (filtros != null) {
                filtroClienteJSON = new FiltroClienteJSON(new JSONObject(filtros));
            }
            if (paginadorDTO == null || paginadorDTO.getSize() == null) {
                paginadorDTO = new PaginadorDTO();
                paginadorDTO.setSize(20L);
                paginadorDTO.setPage(0L);
            }
            return ResponseEntityFactory.ok(clienteService.listaClientes(filtroClienteJSON, paginadorDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar linha do tempo dos contratos de um cliente",
            description = "Consulta a linha do tempo dos contratos de um cliente",
            tags = {"Clientes"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListLinhaDoTempoContratoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListLinhaDoTempoContratoDTO.resposta)}
                            )
                    ),

            },
            parameters = {@Parameter(name = "matricula", description = "Cóidigo da matrícula do cliente que será consultado", example = "332")}
    )
    @GetMapping("/{matricula}/linha-tempo/contratos")
    public ResponseEntity<EnvelopeRespostaDTO> linhaDoTempoContratos(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteService.linhaDoTempoContratos(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar informação de reposição de aulas de um cliente",
            description = "Consulta as informações de reposição de aulas de um cliente",
            tags = {"Agenda de Aulas"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaContadorReposicoesDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaContadorReposicoesDTO.resposta)}
                            )
                    ),
            },
            parameters = {@Parameter(name = "matricula", description = "Cóidigo da matrícula do cliente que será consultado", example = "332")}
    )
    @GetMapping("/{matricula}/info-reposicoes-aulas")
    public ResponseEntity<EnvelopeRespostaDTO> infoReposicoes(@PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(aulaDesmarcadaService.contagemReposicoesAulaColetiva(matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar histórico de reposição de aulas coletivas de um cliente",
            description = "Consulta o histórico de  de reposição de aulas coletivas de um cliente.",
            tags = {"Agenda de Aulas"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListReposicaoAulaColetivaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListReposicaoAulaColetivaDTO.resposta)}
                            )
                    ),
            },
            parameters = {
                    @Parameter(name = "matricula", description = "Cóidigo da matrícula do cliente que será consultado", example = "332"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class))
            }
    )
    @GetMapping("/{matricula}/historico-aulas-coletivas")
    public ResponseEntity<EnvelopeRespostaDTO> reposicoes(@PathVariable Integer matricula, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(aulaDesmarcadaService.reposicoesAulaColetiva(matricula, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar cliente pelo CPF de forma simplificada",
            description = "Consulta informações simplificadad do cliente através do CPF dele.",
            tags = {"Lista de Clientes Simplificada"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteSimplificadoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteSimplificadoDTO.resposta)}
                            )
                    ),

            },
            parameters = {
                    @Parameter(name = "cpf", description = "CPF do cliente que será consultado", example = "123.456.789-10")
            }
    )
    @GetMapping("/simplificado")
    public ResponseEntity<EnvelopeRespostaDTO> consultarClienteSimplificadoPorCpf(@RequestParam(value = "cpf") String cpf) {
        try {
            return ResponseEntityFactory.ok(clienteService.consultarClienteSimplificadoPorCpf(cpf));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @GetMapping("/clientes-inativos-com-periodo-acesso")
    public ResponseEntity<EnvelopeRespostaDTO> clientesInativosComPeriodoAcesso(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(clienteService.consultarClientesInativosComPeriodoAcesso(filtroBIControleOperacoesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/clientes-com-freepass")
    public ResponseEntity<EnvelopeRespostaDTO> clientesComFreepass(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(clienteService.findClientesComFreepass(filtroBIControleOperacoesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/clientes-com-gympass")
    public ResponseEntity<EnvelopeRespostaDTO> clientesComGympass(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(clienteService.findClientesComGympass(filtroBIControleOperacoesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar se um cliente esta inadimplente em alguma empresa.",
            description = "Consultar se um cliente esta inadimplente em alguma empresa.",
            parameters = {
                    @Parameter(
                            name = "codigoMatricula",
                            description = "Código matricula do cliente que se deseja consultar",
                            example = "432123",
                            schema = @Schema(implementation = Integer.class)
                    ),
                    @Parameter(
                            name = "codigoEmpresa",
                            description = "Codigo da empresa que se deseja consultar",
                            example = "1",
                            schema = @Schema(implementation = Integer.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaBoolan.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/inadimplente/{codigoMatricula}/{codigoEmpresa}")
    public ResponseEntity<EnvelopeRespostaDTO> possuiRestricao(@PathVariable(value = "codigoMatricula") Integer codigoMatricula,
                                                               @PathVariable(value = "codigoEmpresa") Integer codigoEmpresa) {
        try {
            return ResponseEntityFactory.ok(clienteService.clienteEstaInadimplente(codigoMatricula, codigoEmpresa));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar clientes para verificar",
            description = "Consulta clientes que precisam ser verificados com filtros e paginação",
            tags = {"BI Administrativo"},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca em formato JSON",
                            example = "{\"empresa\":1,\"quickSearchValue\":\"nome\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "verificado", description = "Status de verificação", example = "true"),
                    @Parameter(name = "page", description = "Número da página", example = "0"),
                    @Parameter(name = "size", description = "Itens por página", example = "10")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Consulta realizada com sucesso")
            }
    )
    @GetMapping("/clientes-para-verificar")
    public ResponseEntity<EnvelopeRespostaDTO> clientesParaVerificar(
            @RequestParam(value = "filters", required = false) String filtros,
            @RequestParam(value = "verificado", required = false) Boolean verificado,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIDTO filtroBIDTO = new FiltroBIDTO(filtros);
            return ResponseEntityFactory.ok(clienteService.consultarClientesParaVerificar(filtroBIDTO, paginadorDTO, verificado), paginadorDTO);
        } catch (com.pacto.config.exceptions.ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
