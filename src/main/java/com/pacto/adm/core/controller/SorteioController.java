package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.SorteioDTO;
import com.pacto.adm.core.dto.enveloperesposta.sorteio.EnvelopeRespostaListSorteioDTO;
import com.pacto.adm.core.dto.enveloperesposta.sorteio.EnvelopeRespostaSorteioDTO;
import com.pacto.adm.core.dto.filtros.FiltroSorteioJSON;
import com.pacto.adm.core.services.interfaces.SorteioService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/sorteio")
@Tag(name = "Sorteio")
public class SorteioController {

    @Autowired
    SorteioService sorteioService;

    @Operation(
            summary = "Consultar todos os sorteios",
            description = "Consulta todos os sorteios.",
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros disponíveis para a consulta.<br/>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Texto utilizado para buscar o nome do cliente. Caso o valor seja numérico, também realizará a busca pelo <strong>código</strong> do cliente.</li>" +
                                    "</ul>" +
                                    "<br/><strong>Comportamento:</strong><br/>" +
                                    "Quando o valor de <strong>quicksearchValue</strong> for texto, ele será usado para filtrar clientes com nome semelhante ao informado. Se o valor for numérico, a busca será feita pelo <strong>código</strong> do cliente. O filtro aceita ambas as opções simultaneamente.",
                            example = "{" +
                                    "\"quicksearchValue\": \"João\"" +
                                    "}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Define a ordenação dos resultados com base nos campos permitidos.<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código</li>" +
                                    "<li><strong>data:</strong> Ordena pela data do sorteio</li>" +
                                    "<li><strong>observacoes:</strong> Ordena pelas observações</li>" +
                                    "<li><strong>nome:</strong> Ordena pelo nome do cliente</li>" +
                                    "<li><strong>nomeUsuario:</strong> Ordena pelo nome do usuário</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordem ascendente</li>" +
                                    "<li><strong>desc:</strong> Ordem descendente</li>" +
                                    "</ul>" +
                                    "Utilize o formato: <strong>atributo,ordem</strong>.<br/>" +
                                    "Se não informado, o padrão será <strong>codigo desc</strong>.",
                            example = "data,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListSorteioDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListSorteioDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                       @Parameter(hidden = true) PaginadorDTO paginadorDTO) {

        try {
            FiltroSorteioJSON filtroSorteioJSON = new FiltroSorteioJSON(filtros);
            return ResponseEntityFactory.ok(sorteioService.findAll(filtroSorteioJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar um sorteio",
            description = "Consulta um sorteio.",
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    ),
                    @Parameter(name = "id", description = "Codigo identificador do sorteio que será consultado", example = "10", required = true),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaSorteioDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaSorteioDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(sorteioService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Realizar um sorteio",
            description = "Realiza um sorteio através das configurações do sorteio previamente estabelecidas.",
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaSorteioDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaSorteioDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/realizar-sorteio")
    public ResponseEntity<EnvelopeRespostaDTO> realizarSorteio() {
        try {
            return ResponseEntityFactory.ok(sorteioService.realizarSorteio());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Validar resultados de um sorteio",
            description = "Valida o resultado de um sorteio.",
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    )
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Informações do sorteio que vão ser validadas.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SorteioDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaSorteioDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaSorteioDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaSorteioDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @PostMapping("/validar-resultado")
    public ResponseEntity<EnvelopeRespostaDTO> realizarSorteio(@RequestBody SorteioDTO sorteioDTO) {
        try {
            return ResponseEntityFactory.ok(sorteioService.validarResultado(sorteioDTO));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }
}

