package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.atestado.EnvelopeRespostaListAtestadoMedicoDTO;
import com.pacto.adm.core.services.interfaces.AtestadoMedicoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/atestados-medicos")
@Tag(name = "Clientes com Atestado")
public class AtestadoMedicoController {

    @Autowired
    private AtestadoMedicoService atestadoMedicoService;

    @Operation(
            summary = "Consultar os atestados médicos de uma pessoa",
            description = "Consulta todos os atestados médicos de uma pessoa através do código de matrícula dela",
            parameters = {
                    @Parameter(name = "codMatricula", description = "Código da matrícula", example = "2"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class))
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListAtestadoMedicoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListAtestadoMedicoDTO.resposta)}
                            )),
            }
    )
    @GetMapping("/{codMatricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findAllByMatricula(@PathVariable Integer codMatricula, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(atestadoMedicoService.findAllByMatricula(codMatricula, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
