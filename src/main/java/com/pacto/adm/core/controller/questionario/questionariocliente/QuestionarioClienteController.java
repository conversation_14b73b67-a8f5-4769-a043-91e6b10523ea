package com.pacto.adm.core.controller.questionario.questionariocliente;

import com.pacto.adm.core.dao.interfaces.agenda.AgendaRepository;
import com.pacto.adm.core.dto.QuestionarioClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios.EnvelopeRespostaQuestionarioClienteDTO;
import com.pacto.adm.core.services.interfaces.QuestionarioClienteService;
import com.pacto.adm.core.swagger.respostas.questionariocliente.ExemploRespostaBoolean;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.Uteis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/questionario-cliente")
@Tag(name = "Questionário Cliente", description = "Operações de gestão de questionários de clientes (BV)")
public class QuestionarioClienteController {

    private final QuestionarioClienteService questionarioClienteService;
    private final AgendaRepository agendaRepository;

    public QuestionarioClienteController(QuestionarioClienteService questionarioClienteService, AgendaRepository agendaRepository) {
        this.questionarioClienteService = questionarioClienteService;
        this.agendaRepository = agendaRepository;
    }

    @Operation(
            summary = "Cadastrar questionário de cliente",
            description = "Cadastra um novo questionário de cliente (BV) no sistema e envia notificação via webhook.",
            tags = {"Questionário Cliente"},
            parameters = {
                    @Parameter(name = "rematricula", description = "Indica se o questionário é para rematrícula", example = "false")
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do questionário de cliente a ser cadastrado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = QuestionarioClienteDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Questionário cadastrado com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaQuestionarioClienteDTO.class)
                            )
                    )
            }
    )
    @PostMapping()
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarBv(
            @RequestBody QuestionarioClienteDTO questionarioClienteDTO,
            @RequestParam(required = false, defaultValue = "false") Boolean rematricula
    ) {
        try {
            questionarioClienteDTO = questionarioClienteService.cadastrarBv(questionarioClienteDTO, rematricula);
            questionarioClienteService.notificarWebhookBV(questionarioClienteDTO);
            return ResponseEntityFactory.ok(questionarioClienteDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar último questionário do cliente",
            description = "Consulta o último questionário (BV) respondido por um cliente específico.",
            tags = {"Questionário Cliente"},
            parameters = {
                    @Parameter(name = "codCliente", description = "Código identificador do cliente", example = "123", required = true),
                    @Parameter(name = "rematricula", description = "Indica se deve buscar questionário de rematrícula", example = "false")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Último questionário encontrado com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaQuestionarioClienteDTO.class)
                            )
                    )
            }
    )
    @GetMapping("/ultimo-bv-cliente/{codCliente}")
    public ResponseEntity<EnvelopeRespostaDTO> ultimoBvCliente(
            @PathVariable Integer codCliente,
            @RequestParam(required = false, defaultValue = "false") Boolean rematricula
    ) {
        try {
            return ResponseEntityFactory.ok(questionarioClienteService.ultimoBVCliente(
                    codCliente, rematricula, false
            ));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Verificar agendamentos com consultor",
            description = "Verifica se um cliente possui agendamentos futuros com um consultor específico.",
            tags = {"Questionário Cliente"},
            parameters = {
                    @Parameter(name = "codCliente", description = "Código identificador do cliente", example = "123", required = true),
                    @Parameter(name = "codConsultor", description = "Código identificador do consultor", example = "456", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Verificação realizada com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaBoolean.class)
                            )
                    )
            }
    )
    @GetMapping("/possui-agendamentos-consultor/{codCliente}/{codConsultor}")
    public ResponseEntity<EnvelopeRespostaDTO> possuiAgendamentoConsultorAtual(
            @PathVariable Integer codCliente,
            @PathVariable Integer codConsultor
    ) {
        try {
            return ResponseEntityFactory.ok(agendaRepository.clientePossuiAgendamentosColaborador(
                    codConsultor, codCliente,
                    Uteis.getDataJDBC(Calendario.hoje())
            ));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("erro-consultar-agendamentos-consultor", e.getMessage());
        }
    }

    @Operation(
            summary = "Obter questionário para visitante",
            description = "Obtém o questionário (BV) configurado para visitantes de uma empresa específica.",
            tags = {"Questionário Cliente"},
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Questionário para visitante obtido com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaQuestionarioClienteDTO.class)
                            )
                    )
            }
    )
    @GetMapping("/obter-bv-visitante")
    public ResponseEntity<EnvelopeRespostaDTO> obterBvVisitante(@RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(questionarioClienteService.obterBVVisitante(empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
