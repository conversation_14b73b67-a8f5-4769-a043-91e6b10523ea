package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.ComprovanteVacinaDTO;
import com.pacto.adm.core.dto.enveloperesposta.vacina.EnvelopeRespostaComprovanteVacinaDTO;
import com.pacto.adm.core.dto.enveloperesposta.vacina.EnvelopeRespostaListComprovanteVacinaDTO;
import com.pacto.adm.core.dto.filtros.FiltroComprovanteVacinaJSON;
import com.pacto.adm.core.services.interfaces.ComprovanteVacinaService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/comprovante-vacina")
@Tag(name = "Cartão de Vacina")
public class ComprovanteVacinaController {

    @Autowired
    private ComprovanteVacinaService comprovanteVacinaService;

    @Operation(
            summary = "Consultar cartões de vacinação de uma pessoa pelo código de matrícula dela",
            description = "Consulta os cartões de vacinação de uma pessoa pelo código de matrícula dela.",
            parameters = {
                    @Parameter(name = "codMatricula", description = "Código da matrícula da pessoa", example = "342", required = true),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca. Deve ser informado como um JSON<br/>" +
                                    "<br/><strong>Filtros disponíveis:</strong>" +
                                    "<ul>" +
                                    "<li><strong>quickSearchValue:</strong> Filtra pelo código do comprovante de vacinação.</li>" +
                                    "</ul>",
                            example = "{\"quickSearchValue\": \"1\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "sort", description = "Ordena as respostas pelo código do comprovante de vacinação.<br/> " +
                            "<strong>Ordens disponíveis</strong>" +
                            "<ul>" +
                            "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                            "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                            "</ul>" +
                            "Por padrão, caso não seja informado nenhum valor, as respostas serão ordenadas pelo código da vacinação",
                            example = "codigo,asc", schema = @Schema(implementation = String.class))
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListComprovanteVacinaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListComprovanteVacinaDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/{codMatricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findAllByMatricula(
            @PathVariable Integer codMatricula, @RequestParam(value = "filters", required = false) JSONObject filtrosJson,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroComprovanteVacinaJSON filtros = new FiltroComprovanteVacinaJSON(filtrosJson);
            return ResponseEntityFactory.ok(comprovanteVacinaService.findAllByMatricula(codMatricula, filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Criar ou atualizar um cartão de vacinação",
            description = "Cria ou atualiza um cartão de vacinação.<br/>" +
                    "Para criar, envie o corpo da requsição sem o atributo codigo<br/>" +
                    "Para atualizar, envie o corpo da requisição com o atributo codigo com o valor do item que se deseja atualizar<br/>",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo de requisição para cadastrar ou atualizar um cartão de vacinação<br/>" +
                            "Para criar, envie o corpo da requsição sem o atributo codigo<br/>" +
                            "Para atualizar, envie o corpo da requisição com o atributo codigo com o valor do item que se deseja atualizar<br/>",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ComprovanteVacinaDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaComprovanteVacinaDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaComprovanteVacinaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaComprovanteVacinaDTO.resposta)}
                            )
                    ),
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody ComprovanteVacinaDTO comprovanteVacinaDTO) {
        try {
            return ResponseEntityFactory.ok(comprovanteVacinaService.saveOrUpdate(comprovanteVacinaDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Excluir um cartão de vacinação",
            description = "Exclui um cartão de vacinação pelo código dele",
            parameters = {
                    @Parameter(name = "id", description = "Codigo do cartão de vacinação que será excluído", example = "375", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida."
                    ),
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id) {
        try {
            comprovanteVacinaService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
