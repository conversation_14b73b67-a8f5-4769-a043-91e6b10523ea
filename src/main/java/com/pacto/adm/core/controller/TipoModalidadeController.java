package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.TipoModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.log.EnvelopeRespostaListLogDTO;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.tipo.EnvelopeRespostaListTipoModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.tipo.EnvelopeRespostaTipoModalidadeDTO;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroTipoModalidadeJSON;
import com.pacto.adm.core.services.interfaces.TipoModalidadeService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.Uteis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/tipo-modalidade")
public class TipoModalidadeController {

    @Autowired
    private TipoModalidadeService tipoModalidadeService;

    @Operation(
            summary = "Consultar todos os tipos de modalidades",
            description = "Consulta todos os tipos de modalidades podendo aplicar filtros na busca.",
            tags = {"Tipo de Modalidade"},
            parameters = {
                    @Parameter(
                            name = "quicksearchValue",
                            description = "Valor utilizado para realizar a busca rápida.<br/>" +
                                    "<ul>" +
                                    "<li><strong>Quando for texto:</strong> Busca registros cujo nome contenha o valor informado (case-insensitive).</li>" +
                                    "<li><strong>Quando for número:</strong> Também busca por código e identificador igual ao valor informado.</li>" +
                                    "</ul>" +
                                    "<br/><strong>Exemplo de uso:</strong> Se o valor informado for <strong>123</strong>, " +
                                    "a busca tentará encontrar registros com <strong>nome contendo '123'</strong>, " +
                                    "<strong>código igual a 123</strong> ou <strong>identificador igual a 123</strong>.",
                            example = "ACADEMIA",
                            schema = @Schema(type = "string")
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação dos resultados da busca.<br/>" +
                                    "<strong>Atributos disponíveis para ordenação:</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código</li>" +
                                    "<li><strong>nome:</strong> Ordena pelo nome</li>" +
                                    "<li><strong>nrVezes:</strong> Ordena pelo identificador (número de vezes)</li>" +
                                    "</ul>" +
                                    "<strong>Direções permitidas:</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Crescente</li>" +
                                    "<li><strong>desc:</strong> Decrescente</li>" +
                                    "</ul>" +
                                    "Formato: <strong>atributo,ordem</strong>.<br/>" +
                                    "Exemplo: <code>nome,asc</code><br/>" +
                                    "Padrão: <code>codigo,desc</code>.",
                            example = "nome,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListTipoModalidadeDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListTipoModalidadeDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) String filters,
                                                       @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtros = filters != null ? new JSONObject(filters) : null;
            FiltroTipoModalidadeJSON filtroTipoModalidadeJSON = new FiltroTipoModalidadeJSON(filtros);
            return ResponseEntityFactory.ok(tipoModalidadeService.findAll(filtroTipoModalidadeJSON, paginadorDTO), paginadorDTO);
        } catch (JSONException e) {
            return ResponseEntityFactory.mensagemFront("invalid.filters", "Os filtros fornecidos são inválidos.");
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar uma modalidade",
            description = "Consulta as informações de uma modalidade pelo id dela.",
            tags = {"Tipo de Modalidade"},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da modalidade", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaTipoModalidadeDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaTipoModalidadeDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            Uteis.logar("TipoModalidadeController.findById: " + id);
            return ResponseEntityFactory.ok(tipoModalidadeService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Criar ou atualizar uma modalidade",
            description = "Cria ou atualiza uma modalidade com base nas informações enviadas.",
            tags = {"Tipo de Modalidade"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TipoModalidadeDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaTipoModalidadeDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaTipoModalidadeDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaTipoModalidadeDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody TipoModalidadeDTO tipoModalidadeDTO) {
        try {
            return ResponseEntityFactory.ok(tipoModalidadeService.saveOrUpdate(tipoModalidadeDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Excluir uma modalidade",
            description = "Exclui uma modalidade pelo código identificador dela.",
            tags = {"Tipo de Modalidade"},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da modalidade que será excluída", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. Não retorna corpo de resposta.",
                                                    value = ""),
                                    }
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable Integer id) {
        try {
            tipoModalidadeService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar logs dos tipos de modalidade",
            description = "Consulta logs dos tipos de modalidade.",
            tags = {"Logs"},
            parameters = {
                    @Parameter(name = "idTipoMod", description = "Código identificador do tipo de modalidade que será consultado os logs", example = "22"),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca para logs da API.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Texto utilizado para busca parcial em dois campos: <code>operacao</code> e <code>nomeEntidadeDescricao</code>.</li>" +
                                    "<li><strong>dataInicio:</strong> Data inicial para filtrar registros com base na data de alteração. Formato esperado: <code>yyyy-MM-dd</code>.</li>" +
                                    "<li><strong>dataFim:</strong> Data final para filtrar registros com base na data de alteração. Formato esperado: <code>yyyy-MM-dd</code>.</li>" +
                                    "<li><strong>tipo:</strong> Lista de tipos de operação para filtragem (INSERT, UPDATE ou DELETE)</code>.</li>" +
                                    "</ul>" +
                                    "<br/><strong>Observações</strong>" +
                                    "<ul>" +
                                    "<li>Quando apenas <code>dataFim</code> é informada, assume <code>1900-01-01</code> como data inicial.</li>" +
                                    "<li>Quando apenas <code>dataInicio</code> é informada, assume <code>2999-12-31</code> como data final.</li>" +
                                    "<li>Os filtros de <code>tipo</code> criam múltiplas condições do tipo <code>LIKE</code> unidas por <code>OR</code>.</li>" +
                                    "</ul>",
                            example = "{" +
                                    "\"parametro\": \"Consulta de Cliente\", " +
                                    "\"dataInicio\": \"2025-01-01\", " +
                                    "\"dataFim\": \"2025-05-01\", " +
                                    "\"tipo\": [\"INCLUSÃO\", \"ALTERAÇÃO\"]" +
                                    "}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas pelos atributos disponíveis.<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>obj.operacao:</strong> Ordena pela operação realizada</li>" +
                                    "<li><strong>obj.usuario:</strong> Ordena pelo usuário responsável</li>" +
                                    "<li><strong>dataAlteracao:</strong> Ordena pela data de alteração</li>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código</li>" +
                                    "<li><strong>alteracoes:</strong> Ordena pelas alterações registradas</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para realizar a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas por <strong>dataAlteracao desc</strong> e <strong>codigo desc</strong>.<br/><br/>",
                            example = "dataAlteracao,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListLogDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListLogDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/logs")
    public ResponseEntity<EnvelopeRespostaDTO> buscarLogsTipoModalidade(@RequestParam(value = "filters", required = false) JSONObject filters,
                                                                        @RequestParam(value = "idTipoMod", required = false) Integer idTipoMod,
                                                                        @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroLogClienteJSON filtros = new FiltroLogClienteJSON(filters);
            return ResponseEntityFactory.ok(tipoModalidadeService.buscarLogs(filtros, idTipoMod, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


}
