package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaListMovPagamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaString;
import com.pacto.adm.core.services.interfaces.RedeEmpresaService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/rede-empresa")
@Tag(name = "Configurações ADM")
public class RedeEmpresaController {

    private final RedeEmpresaService redeEmpresaService;

    public RedeEmpresaController(RedeEmpresaService redeEmpresaService) {
        this.redeEmpresaService = redeEmpresaService;
    }

    @Operation(
            summary = "Limpar cache de Rede de Empresas",
            description = "Limpa o cache da Rede de Empresas do sistema.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaString.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = "{\"content\":\"ok\"}")
                                    }
                            )
                    )
            }
    )
    @GetMapping("/clear-cache")
    public ResponseEntity<EnvelopeRespostaDTO> clearCache() {
        redeEmpresaService.limparCache();
        return ResponseEntityFactory.ok("ok");
    }

}
