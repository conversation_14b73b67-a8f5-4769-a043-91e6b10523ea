package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.movpagamento.MovPagamentoTotaisDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaListMovParcelaDTO;
import com.pacto.adm.core.dto.enveloperesposta.log.EnvelopeRespostaListLogConciliadoraDTO;
import com.pacto.adm.core.services.interfaces.MovPagamentoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/pagamentos")
public class MovPagamentoController {

    private final MovPagamentoService movPagamentoService;

    public MovPagamentoController(MovPagamentoService movPagamentoService) {
        this.movPagamentoService = movPagamentoService;
    }

    @Operation(
            summary = "Consultar os pagamentos de uma pessoa",
            description = "Consulta os pagamentos de uma pessoa",
            tags = {"Clientes"},
            parameters = {
                    @Parameter(name = "codPessoa", required = true, description = "Código da pessoa", example = "5"),
                    @Parameter(name = "filters", description = "Filtros de busca, deve ser informado como um JSON. " +
                            "Filtros disponíveis: " +
                            "<ul>" +
                            "<li><strong>buscarStatusConciliadora</strong>: true ou false</li>" +
                            "<li><strong>telaAluno</strong>: true ou false</li>" +
                            "</ul>",
                            example = "{\"buscarStatusConciliadora\":\"true\",\"telaAluno\":\"false\"}"
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo nome da empresa</li>" +
                                    "<li><strong>nomePessoaPagador:</strong> Ordena pelo nome do pagador</li>" +
                                    "<li><strong>reciboPagamento:</strong> Ordena pelo recibo de pagamento</li>" +
                                    "<li><strong>valor:</strong> Ordena pelo valor</li>" +
                                    "<li><strong>valorTotal:</strong> Ordena pelo valor total</li>" +
                                    "<li><strong>dataLancamento:</strong> Ordena pela data de lançamento</li>" +
                                    "<li><strong>formaPagamento:</strong> Ordena pela descrição da forma de pagamento</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data de lançamento.<br/><br/>",
                            example = "dataLancamento,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida!",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovParcelaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListMovParcelaDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("/{codPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> findByPessoa(@PathVariable Integer codPessoa, @RequestParam(value = "filters", required = false) String filtrosJson, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtros = (filtrosJson != null) ? new JSONObject(filtrosJson) : new JSONObject();
            return ResponseEntityFactory.ok(movPagamentoService.findAllByCodPessoa(codPessoa, filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar os logs da conciliadora de pagamentos",
            description = "Consulta os logs da conciliadora de pagamentos",
            tags = {"Logs"},
            parameters = {
                    @Parameter(name = "codMovPagamento", required = true, description = "Código da movimentação do pagamento", example = "5"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class))
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Logs encontrados",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListLogConciliadoraDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaListLogConciliadoraDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("log-conciliadora/{codMovPagamento}")
    public ResponseEntity<EnvelopeRespostaDTO> logConciliadora(@PathVariable Integer codMovPagamento, @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(movPagamentoService.obterLogConciliadora(codMovPagamento), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/data-base-alterada")
    public ResponseEntity<EnvelopeRespostaDTO> pagamentosComDataBaseAlterada(
            @RequestParam(name = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {

            FiltroBIControleOperacoesJSON filtroContratoJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroContratoJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            MovPagamentoTotaisDTO movPagamentoTotaisDTO = movPagamentoService.pagamentosComDataBaseAlterada(filtroContratoJSON, paginadorDTO);
            EnvelopeRespostaDTO envelopeRespostaDTO = EnvelopeRespostaDTO.of(
                    movPagamentoTotaisDTO.getMovPagamentos(), paginadorDTO
            );
            envelopeRespostaDTO.setContent(movPagamentoTotaisDTO);
            return ResponseEntityFactory.ok(envelopeRespostaDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @GetMapping("/data-base-alterada-sem-totais")
    public ResponseEntity<EnvelopeRespostaDTO> pagamentosComDataBaseAlteradaSemTotais(
            @RequestParam(name = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {

            FiltroBIControleOperacoesJSON filtroContratoJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroContratoJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            MovPagamentoTotaisDTO movPagamentoTotaisDTO = movPagamentoService.pagamentosComDataBaseAlterada(filtroContratoJSON, paginadorDTO);
            return ResponseEntityFactory.ok(movPagamentoTotaisDTO.getMovPagamentos(), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
