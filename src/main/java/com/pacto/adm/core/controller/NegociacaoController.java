package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.CarterinhaRepresentacao;
import com.pacto.adm.core.dto.enveloperesposta.agenda.ConfigConsultaTurma.EnvelopeRespostaConfigConsultaTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.agenda.EnvelopeRespostaListAgendaTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.aulas.cheia.EnvelopeRespostaAulasCheias;
import com.pacto.adm.core.dto.enveloperesposta.cliente.negociacao.EnvelopeRespostaListClienteNegociacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.negociacao.check.EnvelopeRespostaCheckNegociacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.config.EnvelopeRespostaConfigsContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.negociacao.EnvelopeRespostaNegociacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.negociacao.EnvelopeRespostaResultadoNegociacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.negociacao.simulado.EnvelopeRespostaSimuladoDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.EnvelopeRespostaListPlanoDTO;
import com.pacto.adm.core.dto.filtros.FiltroNegociacaoPlanoJSON;
import com.pacto.adm.core.dto.negociacao.CheckNegociacaoDTO;
import com.pacto.adm.core.dto.negociacao.ConfigsContratoDTO;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import com.pacto.adm.core.enumerador.DisponibilidadeTurmaEnum;
import com.pacto.adm.core.enumerador.TurnoEnum;
import com.pacto.adm.core.services.interfaces.NegociacaoService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@RestController
@RequestMapping("/negociacao")
public class NegociacaoController {

    @Autowired
    private NegociacaoService service;

    @Operation(
            summary = "Consultar planos vigentes",
            description = "Consulta os planos vigentes.",
            tags = {"Negociação"},
            parameters = {
                    @Parameter(
                            name = "incluirBolsa",
                            description = "Indica que deve incluir as informações da bolsa. Aceita apenas valores true (incluir) ou false (Não incluir).",
                            example = "true", schema = @Schema(implementation = Boolean.class), required = true
                    ),
                    @Parameter(
                            name = "planoForcar",
                            description = "Código do plano que será forçado a busca.",
                            example = "33",
                            required = true
                    ),
                    @Parameter(name = "codigoCliente", description = "Código do cliente que será consultado os planos já feitos com a academia", example = "34"),
                    @Parameter(name = "contrato", description = "Código do contrato que será renovado que será buscado." +
                            "(Deve informar o contratro_{código} para a busca funcionar)", example = "renovar_14"),
                    @Parameter(
                            name = "filters",
                            description = "Filtros disponíveis para a consulta.<br/>" +
                                    "<ul>" +
                                    "<li><strong>dataLancamentoContrato:</strong> Data utilizada como base para filtrar planos com <strong>vigência igual ou superior</strong> " +
                                    "e <strong>data de ingresso igual ou superior</strong> à informada. A hora será automaticamente zerada para garantir precisão na comparação.</li>" +
                                    "</ul>" +
                                    "<br/><strong>Comportamento padrão:</strong><br/>" +
                                    "Caso <strong>dataLancamentoContrato</strong> não seja informada, a data atual será considerada como referência para ambos os filtros.",
                            example = "{" +
                                    "\"dataLancamentoContrato\": \"2025-05-12\"" +
                                    "}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para consultar os planos vigentes.",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListPlanoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListPlanoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/planos/{incluirBolsa}/{planoForcar}")
    public ResponseEntity<EnvelopeRespostaDTO> planos(@PathVariable String incluirBolsa,
                                                      @PathVariable Integer planoForcar,
                                                      @RequestParam(name = "codigoCliente", required = false) Integer codigoCliente,
                                                      @RequestParam(name = "contrato", required = false) String contrato,
                                                      @RequestParam(name = "filters", required = false) String filters,
                                                      @RequestHeader(value = "empresaId") Integer empresaId
    ) {
        try {
            FiltroNegociacaoPlanoJSON filtros = null;
            if (filters != null) {
                filtros = new FiltroNegociacaoPlanoJSON(new JSONObject(filters));
            }
            Integer contratoRenovar = 0;
            if (contrato != null && contrato.contains("renovar")) {
                try {
                    contratoRenovar = Integer.valueOf(contrato.replace("renovar_", ""));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return ResponseEntityFactory.ok(service.consultarPlanos(Boolean.valueOf(incluirBolsa), planoForcar,
                    filtros, codigoCliente, contratoRenovar));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Montar agenda de aulas para turmas",
            description = "Monta uma agenda de aulas para uma turma.",
            tags = {"Turmas"},
            parameters = {
                    @Parameter(name = "modalidade", description = "Código da modalidade que será usada para montar a agenda.", example = "1", required = true),
                    @Parameter(name = "nivel", description = "Código do nível da turma para montar a agenda", example = "1", required = true),
                    @Parameter(name = "professor", description = "Código do professor que será responsável pela turma", example = "1", required = true),
                    @Parameter(name = "periodo", description = "Período da turma (MANHA, TARDE ou NOITE)", example = "MANHA", required = true, schema = @Schema(implementation = TurnoEnum.class)),
                    @Parameter(name = "disponibilidade", description = "Disponibilidade da turma", example = "disponiveis", required = true, schema = @Schema(implementation = DisponibilidadeTurmaEnum.class)),
                    @Parameter(name = "cliente", description = "Código do cliente", example = "1"),
                    @Parameter(name = "inicio", description = "Data de início da turma", example = "12/05/2025"),
                    @Parameter(name = "empresaId", description = "Código da empresa que a turma pertence", example = "1", required = true, in = ParameterIn.HEADER),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListAgendaTurmaDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListAgendaTurmaDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/agenda-turmas/{modalidade}/{nivel}/{professor}/{periodo}/{disponibilidade}")
    public ResponseEntity<EnvelopeRespostaDTO> agendaTurmas(@PathVariable Integer modalidade,
                                                            @PathVariable Integer nivel,
                                                            @PathVariable Integer professor,
                                                            @PathVariable String periodo,
                                                            @PathVariable String disponibilidade,
                                                            @RequestParam(required = false) Integer cliente,
                                                            @RequestParam(required = false) String inicio,
                                                            @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.montarAgendaTurma(modalidade, nivel, professor, periodo, disponibilidade, cliente, inicio, empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar configurações das agendas das turmas filtrando pela modalidade da aula.",
            description = "Consulta as configurações das agendas de turmas buscando pela modalidade da aula. " +
                    "A resposta conterá uma lista de professores e uma lista de modalidades dispníveis para ser montado a agenda de aula das turmas.",
            tags = {"Turmas"},
            parameters = {
                    @Parameter(name = "modalidade", description = "Código da modalidade que será usada para a busca.", example = "1", required = true),
                    @Parameter(name = "empresaId", description = "Código da empresa que será usada na busca.", example = "1", required = true, in = ParameterIn.HEADER),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaConfigConsultaTurmaDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaConfigConsultaTurmaDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/agenda-turmas/configs/{modalidade}")
    public ResponseEntity<EnvelopeRespostaDTO> configsTurmas(@PathVariable Integer modalidade,
                                                             @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.configConsultaTurma(empresaId, modalidade));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar clientes que estão em negociação de planos.",
            description = "Consulta as informações dos clientes que estão negociando algum plano na academia.",
            tags = {"Negociação"},
            parameters = {
                    @Parameter(name = "nome", description = "Nome do cliente que será usado para filtrar a busca dos clientes.", example = "Ana"),
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para consultar os clientes em negociação.",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListClienteNegociacaoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListClienteNegociacaoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/clientes")
    public ResponseEntity<EnvelopeRespostaDTO> clientes(@RequestParam String nome,
                                                        @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.consultarClientesNegociacao(nome));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar informações da negociação com o cliente.",
            description = "Consulta as informações da negociação com o cliente, incluindo planos recomendados, horários de aulas, entre outros.",
            tags = {"Negociação"},
            parameters = {
                    @Parameter(name = "cliente", description = "Código identificador do cliente que será buscado as informações.", example = "123"),
                    @Parameter(name = "empresaId", description = "Código identificador da empresa vinculada a negociação.", example = "1", in = ParameterIn.HEADER),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaCheckNegociacaoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaCheckNegociacaoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/check/{cliente}")
    public ResponseEntity<EnvelopeRespostaDTO> check(@PathVariable Integer cliente,
                                                     @RequestHeader Integer empresaId) {
        try {
            CheckNegociacaoDTO checkNegociacaoDTO = service.checkNegociacao(cliente, 0, empresaId, false);
            return ResponseEntityFactory.ok(checkNegociacaoDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar informações da negociação com o cliente, gerando um novo contrato.",
            description = "Consulta as informações da negociação com o cliente, incluindo planos recomendados, horários de aulas, entre outros e gera um novo contrato.",
            tags = {"Negociação"},
            parameters = {
                    @Parameter(name = "cliente", description = "Código identificador do cliente que será buscado as informações.", example = "123"),
                    @Parameter(name = "contratoForcar", description = "Código do contrato do cliente ou se definido como \"novo\" irá criar um novo contrato", example = "novo"),
                    @Parameter(name = "empresaId", description = "Código identificador da empresa vinculada a negociação.", example = "1", in = ParameterIn.HEADER),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaCheckNegociacaoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaCheckNegociacaoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/check/{cliente}/{contratoForcar}")
    public ResponseEntity<EnvelopeRespostaDTO> checkContratoForcar(@PathVariable Integer cliente,
                                                     @PathVariable String contratoForcar,
                                                                   @RequestParam(name = "verificarEmpresaEContratoResponsavelRematricula", required = false, defaultValue = "false") Boolean verificarEmpresaEContratoResponsavelRematricula,
                                                     @RequestHeader Integer empresaId) {
        try {
            Integer contrato = null;
            if (contratoForcar != null) {
                try {
                    contrato = Integer.valueOf(contratoForcar);
                } catch (Exception e) {
                    contrato = 0;
                }
            }

            final CheckNegociacaoDTO checkNegociacaoDTO = service.checkNegociacao(cliente, contrato, empresaId, contratoForcar != null && contratoForcar.equals("novo"), verificarEmpresaEContratoResponsavelRematricula);
            return ResponseEntityFactory.ok(checkNegociacaoDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Montar escolhas de negociação.",
            description = "Monta as escolhas de negociação definindo informações sobre plano, dias de uso da academia, entre outros.",
            tags = {"Negociação"},
            parameters = {
                    @Parameter(name = "plano", description = "Código do plano que será usado para montar a negociação.", example = "1", required = true),
                    @Parameter(name = "contratoBaseado", description = "Código do contrato que será baseado a negociação", example = "234", required = true),
                    @Parameter(name = "situacaoContrato", description = "Situação do contrato", example = "MA", required = true, schema = @Schema(implementation = SituacaoContratoEnum.class)),
                    @Parameter(name = "empresaId", description = "Código identificador da empresa vinculada a negociação.", example = "1", in = ParameterIn.HEADER),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaNegociacaoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaNegociacaoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/dados-plano/{plano}/{situacaoContrato}/{contratoBaseado}")
    public ResponseEntity<EnvelopeRespostaDTO> negociacao(@PathVariable Integer plano,
                                                          @PathVariable Integer contratoBaseado,
                                                          @PathVariable SituacaoContratoEnum situacaoContrato,
                                                          @RequestHeader Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.montarEscolhasNegociacao(plano, empresaId, situacaoContrato, contratoBaseado));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Simular uma negociação.",
            description = "Faz a simulação de uma negociação.",
            tags = {"Negociação"},
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para simular a negociação.",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    )
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para simular uma negociação.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfigsContratoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfigsContratoDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaSimuladoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaSimuladoDTO.resposta)
                            )
                    )
            }
    )
    @PostMapping("/simular")
    public ResponseEntity<EnvelopeRespostaDTO> simular(@RequestBody ConfigsContratoDTO configs,
                                                       @RequestHeader(value = "empresaId") Integer empresaId) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            return ResponseEntityFactory.ok(service.simular(configs));
        } catch (ServiceException e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO SIMULAR - NEGOCIACAOCONTROLLER.JAVA ####\n" + e.getMessage());
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Fixar horários de aulas para um cliente",
            description = "Fixa os horários de aulas informados para um cliente.",
            tags = {"Negociação"},
            parameters = {
                    @Parameter(name = "cliente", description = "Código do cliente que se deseja fixar os horários das aulas.", example = "3", required = true),
                    @Parameter(name = "duracao", description = "Duração das aulas", example = "1", required = true),
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para validar e fixar as aulas do cliente.",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    )
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Código dos horários de aulas que se deseja fixar para o cliente",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = "[1,2,3,4]")}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaAulasCheias.class),
                                    examples = {
                                            @ExampleObject(summary = "Aulas registradas", description = "Exemplo de resposta com o status 200 para as aulas que foram registradas com sucesso. Na resposta não há retorno.", value = ""),
                                            @ExampleObject(summary = "Aulas cheias", description = "Exemplo de resposta com o status 200 contendo uma lista das aulas que estão cheias e que não foram possíveis registrar o aluno.", value = EnvelopeRespostaAulasCheias.resposta)
                                    }
                            )
                    )
            }
    )
    @PostMapping("/validar-aulas-fixar/{cliente}/{duracao}")
    public ResponseEntity<EnvelopeRespostaDTO> validarAulasFixar(@RequestBody List<Integer> horarios,
                                                                 @PathVariable Integer cliente,
                                                                 @PathVariable Integer duracao,
                                                                 @RequestHeader(value = "empresaId") Integer empresaId) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            List<String> aulas = service.validarFixarAulas(cliente, duracao, horarios);
            if(!aulas.isEmpty()){
                Map<String, List<String>> aulasCheias = new HashMap<>();
                aulasCheias.put("aulasCheias", aulas);
                return ResponseEntityFactory.ok(aulasCheias);
            }
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Criar uma negociação",
            description = "Cria uma negociação baseado nas configurações feitas do contrato.",
            tags = {"Negociação"},
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizada para criar a negociação.",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    )
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações criar uma negociação.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfigsContratoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfigsContratoDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaResultadoNegociacaoDTO.class),
                                    examples = {
                                            @ExampleObject(name = "Exemplo Resposta Status 200", summary = "Exemplo de resposta com o status 200.", value = EnvelopeRespostaResultadoNegociacaoDTO.resposta),
                                     }
                            )
                    )
            }
    )
    @PostMapping("/gravar")
    public ResponseEntity<EnvelopeRespostaDTO> gravar(@RequestBody ConfigsContratoDTO configs,
                                                      @RequestHeader(value = "empresaId") Integer empresaId) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            return ResponseEntityFactory.ok(service.gravar(configs));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Impressão da Carterinha do Cliente",
            description = "Impressão de da Carterinha do Cliente.",
            tags = {"Negociação"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para a carterinha.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CarterinhaRepresentacao.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value =
                                    "{" +
                                    "  \"tipoOperacao\": \"CARTERINHA\"," +
                                    "  \"codigoCliente\": \"123456\"," +
                                    "  \"codigoEmpresa\": \"001\"," +
                                    "  \"chave\": \"abc123xyz\"" +
                                    "}")}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = HashMap.class),
                                    examples = {
                                            @ExampleObject(name = "Exemplo Resposta Status 200", summary = "Exemplo de resposta com o status 200.", value = ""),
                                    }
                            )
                    )
            }
    )
    @PostMapping("/carteirinha-servlet-zw")
    public ResponseEntity<EnvelopeRespostaDTO> chamarServletCarteirinhaCliente(@RequestBody Map<String, Object> body) {
        try {
            return ResponseEntityFactory.ok(service.chamarServletCarteirinhaCliente(new JSONObject(body)));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
