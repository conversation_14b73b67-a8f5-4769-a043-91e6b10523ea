package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.filtros.FiltroSolicitacaoCompraJSON;
import com.pacto.adm.core.dto.solicitacaocompra.SolicitacaoCompraDTO;
import com.pacto.adm.core.services.interfaces.SolicitacaoCompraService;
import com.pacto.adm.core.swagger.respostas.solicitacaocompra.ExemploRespostaListSolicitacaoCompraPaginacao;
import com.pacto.adm.core.swagger.respostas.solicitacaocompra.ExemploRespostaSolicitacaoCompra;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/solicitacao-compra")
public class SolicitacaoCompraController {

    @Autowired
    private SolicitacaoCompraService solicitacaoCompraService;

    @Operation(
            summary = "Consultar solicitações de compra",
            description = "Consulta as solicitações de compra cadastradas no sistema com suporte a filtros e paginação.",
            tags = {"Solicitação de Compra"},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Campos disponíveis para ordenação**\n" +
                                    "- **titulo**: Ordena pelo título da solicitação\n" +
                                    "- **data_solicitacao**: Ordena pela data de solicitação\n" +
                                    "- **situacao**: Ordena pela situação da solicitação\n",
                            example = "titulo,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo título da solicitação de compra\n" +
                                    "- **situacao**: Filtra pela situação da solicitação (PENDENTE, APROVADO, NEGADO)\n" +
                                    "- **dataSolicitacao_start**: Data inicial para filtro por período de solicitação (formato: yyyy-MM-dd)\n" +
                                    "- **dataSolicitacao_end**: Data final para filtro por período de solicitação (formato: yyyy-MM-dd)\n",
                            example = "{\"quicksearchValue\":\"equipamentos\",\"situacao\":\"PENDENTE\",\"dataSolicitacao_start\":\"2024-01-01\",\"dataSolicitacao_end\":\"2024-12-31\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListSolicitacaoCompraPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> listarSolicitacoes(
            @RequestParam(required = false) String filters,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            JSONObject filtros = filters != null ? new JSONObject(filters) : null;

            FiltroSolicitacaoCompraJSON filtroSolicitacaoCompraJSON = new FiltroSolicitacaoCompraJSON(filtros);
            return ResponseEntityFactory.ok(solicitacaoCompraService.listarSolicitacoes(filtroSolicitacaoCompraJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Criar ou atualizar solicitação de compra",
            description = "Cria uma nova solicitação de compra ou atualiza uma existente. Se o código não for informado, uma nova solicitação será criada. Se o código for informado, a solicitação existente será atualizada.",
            tags = {"Solicitação de Compra"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da solicitação de compra a ser criada ou atualizada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SolicitacaoCompraDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaSolicitacaoCompra.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody SolicitacaoCompraDTO solicitacaoCompraDTO) {
        try {
            return ResponseEntityFactory.ok(solicitacaoCompraService.saveOrUpdate(solicitacaoCompraDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e + "/n");
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                result.append(stackTraceElement.toString() + "/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar solicitação de compra",
            description = "Consulta as informações de uma solicitação de compra específica pelo código identificador.",
            tags = {"Solicitação de Compra"},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da solicitação de compra que será consultada", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaSolicitacaoCompra.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(solicitacaoCompraService.buscarSolicitacao(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
