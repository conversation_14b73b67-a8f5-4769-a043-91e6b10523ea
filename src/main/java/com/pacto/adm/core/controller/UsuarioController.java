package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaListUsuarioDTO;
import com.pacto.adm.core.dto.filtros.FiltroUsuarioJSON;
import com.pacto.adm.core.services.interfaces.UsuarioService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/usuarios")
@Tag(name = "Usuário (ADM)")
public class UsuarioController {
    @Autowired
    UsuarioService usuarioService;

    @Operation(
            summary = "Consultar todos os usuários",
            description = "Consulta todos os usuários do sistema Pacto da Academia, podendo realizar filtros na busca.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca para a consulta de usuáros.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pelo nome do usuário.</li>" +
                                    "<li><strong>codigo:</strong> Código do usuário que será buscado.</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"João\",\"codigo\":123}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListUsuarioDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListUsuarioDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroUsuarioJSON filtroJson = new FiltroUsuarioJSON(filtros);
            return ResponseEntityFactory.ok(usuarioService.findAll(filtroJson));
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar usuário Meta",
            description = "Consulta todos os usuários meta cadastrados no sistema Pacto da Academia, podendo realizar filtros na busca.",
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa da qual os usuários meta serão consultados", required = true, example = "1", in = ParameterIn.HEADER),
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca para a consulta de usuários.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pelo nome do usuário.</li>" +
                                    "<li><strong>codigoEmpresa:</strong> Código da empresa da qual o usuário está cadastrado.</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"João\",\"codigoEmpresa\":1}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListUsuarioDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListUsuarioDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/meta")
    public ResponseEntity<EnvelopeRespostaDTO> consultarUsuarioMeta(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroUsuarioJSON filtroJson = new FiltroUsuarioJSON(filtros);
            return ResponseEntityFactory.ok(usuarioService.consultarUsuariosMeta(filtroJson));
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        }
    }
}
