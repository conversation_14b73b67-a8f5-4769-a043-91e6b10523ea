package com.pacto.adm.core.controller;

import com.pacto.adm.core.adapters.ProdutoAdapter;
import com.pacto.adm.core.adapters.planoconta.PlanoContaAdapter;
import com.pacto.adm.core.dto.filtros.FiltroPlanoContaDespesasJSON;
import com.pacto.adm.core.dto.filtros.FiltroPlanoContaProdutoJSON;
import com.pacto.adm.core.entities.PlanoConta;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.services.interfaces.planoconta.PlanoContaService;
import com.pacto.adm.core.swagger.respostas.planoconta.ExemploRespostaListPlanoContaPaginacao;
import com.pacto.adm.core.swagger.respostas.planoconta.ExemploRespostaListProdutoPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/plano-conta")
@Tag(name = "Plano de Contas", description = "Operações de gestão de planos de conta")
public class PlanoContaController {

    private final PlanoContaService planoContaService;
    private final ProdutoAdapter produtoAdapter;
    private final PlanoContaAdapter planoContaAdapter;

    public PlanoContaController(PlanoContaService planoContaService, ProdutoAdapter produtoAdapter, PlanoContaAdapter planoContaAdapter) {
        this.planoContaService = planoContaService;
        this.produtoAdapter = produtoAdapter;
        this.planoContaAdapter = planoContaAdapter;
    }

    @Operation(
            summary = "Consultar produtos do plano de conta",
            description = "Consulta produtos disponíveis no plano de conta com opções de filtro e paginação.",
            tags = {"Plano de Contas"},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição do produto\n" +
                                    "- **somenteServicos**: Filtra apenas produtos do tipo serviço (true/false)",
                            example = "{\"quicksearchValue\":\"plano\",\"somenteServicos\":false}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListProdutoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/produtos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllProdutos(
            @RequestParam(value = "filters", required = false) String filters,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            List<Produto> result = planoContaService.findAllProdutos(new FiltroPlanoContaProdutoJSON(filters), paginadorDTO);
            return ResponseEntityFactory.ok(
                    produtoAdapter.toDtos(result)
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar despesas do plano de conta",
            description = "Consulta despesas do plano de conta com filtros por período, empresa e busca textual.",
            tags = {"Plano de Contas"},
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa", required = true, example = "1", in = ParameterIn.HEADER),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome do plano de conta\n" +
                                    "- **codigoEmpresa**: Código da empresa para filtrar as despesas\n" +
                                    "- **dataInicio**: Data de início do período (timestamp em milissegundos)\n" +
                                    "- **dataFim**: Data de fim do período (timestamp em milissegundos)",
                            example = "{\"quicksearchValue\":\"operacional\",\"codigoEmpresa\":1,\"dataInicio\":1704067200000,\"dataFim\":1735689599000}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "nome,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListPlanoContaPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/despesas")
    public ResponseEntity<EnvelopeRespostaDTO> findAllDespesas(
            @RequestParam(value = "filters", required = false) String filters,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            List<PlanoConta> result = planoContaService.findAllDespesas(new FiltroPlanoContaDespesasJSON(filters), paginadorDTO);
            return ResponseEntityFactory.ok(planoContaAdapter.toDtos(result));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
