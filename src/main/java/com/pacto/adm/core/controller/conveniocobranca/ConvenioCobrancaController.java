package com.pacto.adm.core.controller.conveniocobranca;

import com.pacto.adm.core.adapters.conveniocobranca.ConvenioCobrancaAdapter;
import com.pacto.adm.core.dto.enveloperesposta.acesso.conveniocobranca.EnvelopeRespostaListConvenioCobrancaDTO;
import com.pacto.adm.core.entities.conveniocobranca.ConvenioCobranca;
import com.pacto.adm.core.services.interfaces.conveniocobranca.ConvenioCobrancaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/convenio-cobranca")
public class ConvenioCobrancaController {

    private final ConvenioCobrancaService convenioCobrancaService;
    private final ConvenioCobrancaAdapter convenioCobrancaAdapter;

    public ConvenioCobrancaController(ConvenioCobrancaService convenioCobrancaService, ConvenioCobrancaAdapter convenioCobrancaAdapter) {
        this.convenioCobrancaService = convenioCobrancaService;
        this.convenioCobrancaAdapter = convenioCobrancaAdapter;
    }

    @Operation(
            summary = "Consultar os convênios de cobrança por empresa.",
            description = "Retorna a lista dos convênios de cobrança por empresa. Caso não informado o código da empresa retorna todos os convênios.",
            tags = {"Convênio de Cobrança", "Convênio de Cobrança por empresa"},
            parameters = {
                    @Parameter(name = "empresaId", required = false, description = "Código da empresa", example = "5"),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca",
                            example = "{\"filtro\":\"valorDoFiltro\"}",
                            hidden = true,
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida!",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListConvenioCobrancaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListConvenioCobrancaDTO.resposta)}
                            ))
            }
    )
    @GetMapping({"/by-empresa", "/by-empresa/{empresaId}"})
    public ResponseEntity<EnvelopeRespostaDTO> convenioCobrancaByEmpresa(@PathVariable(value = "empresaId", required = false) Integer empresaId) {
        try {
            List<ConvenioCobranca> convenios = convenioCobrancaService.convenioCobrancaPorEmpresa(empresaId);
            return ResponseEntityFactory.ok(convenioCobrancaAdapter.toDtos(convenios));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
