package com.pacto.adm.core.controller;

import com.pacto.adm.core.dao.interfaces.ContratoOperacaoDao;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.services.interfaces.ContratoOperacaoService;
import com.pacto.adm.core.swagger.respostas.contratooperacao.ExemploRespostaListContratoOperacaoRetroativaPaginacao;
import com.pacto.adm.core.swagger.respostas.contratooperacao.ExemploRespostaListClientesCanceladosPaginacao;
import com.pacto.adm.core.swagger.respostas.contratooperacao.ExemploRespostaListClienteComBonusPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/contrato-operacao")
@Tag(name = "Contratos")
public class ContratoOperacaoController {

    private final ContratoOperacaoService contratoOperacaoService;

    public ContratoOperacaoController(ContratoOperacaoService contratoOperacaoService) {
        this.contratoOperacaoService = contratoOperacaoService;
    }

    @Operation(
            summary = "Consultar operações retroativas de contratos",
            description = "Consulta as operações retroativas realizadas em contratos dentro de um período específico, permitindo filtros por empresa, colaboradores e busca rápida.",
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa que será consultada", required = true, example = "1", in = ParameterIn.HEADER),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "cliente.pessoa.nome,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **empresa**: Código da empresa para filtrar as operações\n" +
                                    "- **inicio**: Data de início do período para consulta das operações (obrigatório)\n" +
                                    "- **fim**: Data de fim do período para consulta das operações (obrigatório)\n" +
                                    "- **colaboradores**: Lista de códigos dos colaboradores para filtrar as operações\n" +
                                    "- **quickSearchValue**: Valor para busca rápida por nome de cliente, matrícula, nome do usuário responsável, código do contrato, descrição da justificativa ou data da operação",
                            example = "{\"empresa\":1,\"inicio\":\"2024-01-01T00:00:00.000Z\",\"fim\":\"2024-01-31T23:59:59.999Z\",\"colaboradores\":[1,2,3],\"quickSearchValue\":\"João Silva\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListContratoOperacaoRetroativaPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/operacoes-retroativas")
    public ResponseEntity<EnvelopeRespostaDTO> consultarOperacoesRetroativas(
            @RequestParam(value = "filters", required = false) String filtros,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(contratoOperacaoService.consultarOperacoesContratoRetroativa(filtroBIControleOperacoesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar contratos cancelados",
            description = "Consulta os contratos que foram cancelados dentro de um período específico, permitindo filtros por empresa, colaboradores e busca rápida.",
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa que será consultada", required = true, example = "1", in = ParameterIn.HEADER),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "nomeCliente,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **empresa**: Código da empresa para filtrar as operações\n" +
                                    "- **inicio**: Data de início do período para consulta das operações (obrigatório)\n" +
                                    "- **fim**: Data de fim do período para consulta das operações (obrigatório)\n" +
                                    "- **colaboradores**: Lista de códigos dos colaboradores para filtrar as operações\n" +
                                    "- **quickSearchValue**: Valor para busca rápida por nome de cliente, matrícula, nome do usuário responsável, código do contrato, descrição da justificativa ou data da operação",
                            example = "{\"empresa\":1,\"inicio\":\"2024-01-01T00:00:00.000Z\",\"fim\":\"2024-01-31T23:59:59.999Z\",\"colaboradores\":[1,2,3],\"quickSearchValue\":\"João Silva\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListClientesCanceladosPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/contratos-cancelados")
    public ResponseEntity<EnvelopeRespostaDTO> consultarContratosCancelados(
            @RequestParam(value = "filters", required = false) String filtros,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(contratoOperacaoService.consultarClientesCancelados(filtroBIControleOperacoesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar clientes com bônus",
            description = "Consulta os clientes que receberam bônus em seus contratos dentro de um período específico, permitindo filtros por empresa, colaboradores e busca rápida.",
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa que será consultada", required = true, example = "1", in = ParameterIn.HEADER),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "cliente.pessoa.nome,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **empresa**: Código da empresa para filtrar as operações\n" +
                                    "- **fim**: Data de fim do período para consulta das operações (obrigatório)\n" +
                                    "- **colaboradores**: Lista de códigos dos colaboradores para filtrar as operações\n" +
                                    "- **quickSearchValue**: Valor para busca rápida por nome de cliente, matrícula, nome do usuário responsável, código do contrato, descrição da justificativa ou data da operação",
                            example = "{\"empresa\":1,\"fim\":\"2024-01-31T23:59:59.999Z\",\"colaboradores\":[1,2,3],\"quickSearchValue\":\"João Silva\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListClienteComBonusPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/clientes-com-bonus")
    public ResponseEntity<EnvelopeRespostaDTO> clientesComBOnus(
            @RequestParam(value = "filters", required = false) String filtros,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(contratoOperacaoService.findClientesComBonus(filtroBIControleOperacoesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
