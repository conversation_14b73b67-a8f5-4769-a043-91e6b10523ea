package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.grupo.EnvelopeRespostaListGrupo;
import com.pacto.adm.core.dto.enveloperesposta.health.EnvelopeRespostaHealth;
import com.pacto.adm.core.services.interfaces.NegociacaoService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/health")
@Tag(name = "Health")
public class HealthController {

    @Autowired
    private NegociacaoService service;

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(
            summary = "Consultar se a API está ativa e funcionando",
            description = "Consulta se a API está ativa e funcionando. Se receber o Status 200 com a mensagem: \"it's alive! :)\" a API está ativa e funcionando corretamente.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaHealth.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaHealth.resposta)
                            )
                    )}
    )
    public ResponseEntity<EnvelopeRespostaDTO> todos() {
        return ResponseEntityFactory.ok("it's alive! :)");
    }

    @Operation(
            summary = "Limpar o Map das URLs da API",
            description = "Limpa o Map das URLs da API.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaHealth.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaHealth.respostaLimparMap)
                            )
                    )}
    )
    @ResponseBody
    @RequestMapping(value = "/clear", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> clearMaps() {
        service.limparMapUrl();
        return ResponseEntityFactory.ok("mapa reiniciado");
    }

}
