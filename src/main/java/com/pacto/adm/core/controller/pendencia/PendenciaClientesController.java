package com.pacto.adm.core.controller.pendencia;

import com.pacto.adm.core.services.interfaces.pendenciacliente.PendenciaClientesService;
import com.pacto.adm.core.swagger.respostas.pendencia.ExemploRespostaListPendenciaClientesPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pendencia-clientes")
@Tag(name = "BI Administrativo")
public class PendenciaClientesController {

    private final PendenciaClientesService pendenciaClientesService;

    public PendenciaClientesController(
            PendenciaClientesService pendenciaClientesService
    ) {
        this.pendenciaClientesService = pendenciaClientesService;
    }

    @Operation(
            summary = "Consultar pendências de clientes",
            description = "Consulta pendências de clientes baseado no indicador informado. Retorna diferentes tipos de dados conforme o indicador selecionado, como parcelas em atraso, parcelas em aberto, clientes aniversariantes, entre outros.",
            tags = {"BI Administrativo"},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "nomePlano,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **empresa**: Filtra pela empresa específica\n" +
                                    "- **inicio**: Data de início do período de consulta (formato ISO 8601)\n" +
                                    "- **fim**: Data de fim do período de consulta (formato ISO 8601) - obrigatório para alguns indicadores\n" +
                                    "- **colaboradores**: Lista de códigos de colaboradores para filtrar\n" +
                                    "- **quickSearchValue**: Busca rápida por nome ou outros campos relevantes",
                            example = "{\"empresa\":1,\"inicio\":\"2024-01-01T00:00:00.000Z\",\"fim\":\"2024-12-31T23:59:59.999Z\",\"quickSearchValue\":\"João\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "indicador",
                            description = "Indicador que define o tipo de pendência a ser consultada. \n\n" +
                                    "**Valores disponíveis**\n" +
                                    "- PARCELAS_EM_ATRASO (Parcelas em Atraso)\n" +
                                    "- PARCELAS_EM_ABERTO (Parcelas em Aberto)\n" +
                                    "- PARCELAS_EM_ABERTO_COLABORADOR (Parcelas em Aberto de Colaborador)\n" +
                                    "- DEBITO_EM_CONTA_CORRENTE (Débito em Conta Corrente)\n" +
                                    "- CREDITO_EM_CONTA_CORRENTE (Crédito em Conta Corrente)\n" +
                                    "- PRODUTOS_VENCIDOS (Produtos Vencidos)\n" +
                                    "- CLIENTES_ANIVERSARIANTES (Clientes Aniversariantes)\n" +
                                    "- COLABORADOR_ANIVERSARIANTES (Colaboradores Aniversariantes)\n" +
                                    "- BV_PENDENTE (BV Pendente)\n" +
                                    "- CADASTRO_INCOMPLETO_CLIENTE (Cadastro Incompleto de Cliente)\n" +
                                    "- CADASTRO_INCOMPLETO_VISITANTE (Cadastro Incompleto de Visitante)\n" +
                                    "- SEM_ASSINATURA_CONTRATO (Sem Assinatura de Contrato)\n" +
                                    "- SEM_ASSINATURA_CANCELAMENTO_CONTRATO (Sem Assinatura de Cancelamento de Contrato)\n" +
                                    "- SEM_RECONHECIMENTO_FACIAL (Sem Reconhecimento Facial)\n" +
                                    "- SEM_FOTO (Sem Foto)\n" +
                                    "- SEM_GEOLOCALIZACAO (Sem Geolocalização)\n" +
                                    "- SEM_PRODUTOS (Sem Produtos)\n" +
                                    "- TRANCAMENTO_VENCIDO (Trancamento Vencido)\n",
                            example = "PARCELAS_EM_ATRASO",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListPendenciaClientesPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> pendenciaClientes(
            @RequestParam(value = "filters", required = false) String filtros,
            @RequestParam(value = "indicador", required = false) String indicador,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(pendenciaClientesService.consultarPorIndicador(filtros, paginadorDTO, indicador), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
