package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaListMovParcelaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pagamento.recibo.EnvelopeRespostaListReciboPagamentoDTO;
import com.pacto.adm.core.dto.filtros.FiltroMovParcelaJSON;
import com.pacto.adm.core.services.interfaces.MovParcelaService;
import com.pacto.adm.core.services.interfaces.MovProdutoService;
import com.pacto.adm.core.services.interfaces.ReciboPagamentoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/parcelas")
@Tag(name = "Gestão de Vendas Online")
public class MovParcelaController {

    @Autowired
    private MovProdutoService movProdutoService;
    @Autowired
    private ReciboPagamentoService reciboPagamentoService;
    @Autowired
    private MovParcelaService movParcelaService;

    @Operation(
            summary = "Consultar o(s) recebido(s) de uma parcela",
            description = "Consulta os recibos de uma parcela.",
            parameters = {
                    @Parameter(name = "codParcela", description = "Código da parcela que será usado para buscar os recibos", required = true, example = "120")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Recebio(s) encontrado(s) com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListReciboPagamentoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListReciboPagamentoDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codParcela}/recibos")
    public ResponseEntity<EnvelopeRespostaDTO> findRecibosByParcela(@PathVariable Integer codParcela) {
        try {
            return ResponseEntityFactory.ok(reciboPagamentoService.findAllByCodParcela(codParcela));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar todas as parcelas vinculadas à venda um produto",
            description = "Consulta todas as parcelas vinculadas à venda um produto.",
            parameters = {
                    @Parameter(name = "codMovProduto", required = true, description = "Código do produto que será usado para encontrar as parcelas", example = "100"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>contrato:</strong> Ordena pelo contrato</li>" +
                                    "<li><strong>descricao:</strong> Ordena pela descrição</li>" +
                                    "<li><strong>dataVencimento:</strong> Ordena pela data de vencimento</li>" +
                                    "<li><strong>valor:</strong> Ordena pelo valor da parcela</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo nome da empresa</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "dataVencimento,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Parcela(s) do produto encontradas com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovParcelaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListMovParcelaDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codMovProduto}/produtos")
    public ResponseEntity<EnvelopeRespostaDTO> findProdutosByParcela(@PathVariable Integer codMovProduto, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(movParcelaService.findAllByCodProduto(codMovProduto, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar parcelas de uma pessoa",
            description = "Consulta as parcelas de uma pessoa pelo código dela.",
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa usado na busca da parcela", example = "5"),
                    @Parameter(name = "filters", description = "Filtros de busca, deve ser informado como um JSON. " +
                            "Filtros disponíveis: " +
                            "<ul>" +
                            "<li><strong>ignorarEmRemessa:</strong> true ou false</li> " +
                            "<li><strong>ignorarParcelasMultaJuros:</strong> true ou false</li>" +
                            "<li><strong>situacao:</strong> PG, EA, RG, CA e atraso</li>" +
                            "</ul>",
                            example = "{\"situacao\":\"atraso\"," +
                                    "\"ignorarEmRemessa\":\"false\"," +
                                    "\"ignorarParcelasMultaJuros\":\"false\"}"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>contrato:</strong> Ordena pelo contrato</li>" +
                                    "<li><strong>descricao:</strong> Ordena pela descrição</li>" +
                                    "<li><strong>dataVencimento:</strong> Ordena pela data de vencimento</li>" +
                                    "<li><strong>valor:</strong> Ordena pelo valor da parcela</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo nome da empresa</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "dataVencimento,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Parcela(s) encontradas com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovParcelaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListMovParcelaDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> consultarPorCodigoPessoa(@PathVariable Integer codPessoa, @RequestParam(value = "filters", required = false) String filtrosJson, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtros = (filtrosJson != null) ? new JSONObject(filtrosJson) : new JSONObject();
            FiltroMovParcelaJSON filtroMovParcelaJson = new FiltroMovParcelaJSON(filtros);
            return ResponseEntityFactory.ok(movParcelaService.consultarPorCodigoPessoa(codPessoa, null, filtroMovParcelaJson, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar parcelas de uma pessoa pela matrícula",
            description = "Consulta as parcelas de uma pessoa código de matrícula dela.",
            parameters = {
                    @Parameter(name = "codMatricula", description = "Código da matrícula que será usada na busca das parcelas", example = "100"),
                    @Parameter(name = "filters", description = "Filtros de busca, deve ser informado como um JSON. " +
                            "Filtros disponíveis: " +
                            "<ul>" +
                            "<li><strong>ignorarEmRemessa:</strong> true ou false</li> " +
                            "<li><strong>ignorarParcelasMultaJuros:</strong> true ou false</li>" +
                            "<li><strong>situacao:</strong> PG, EA, RG, CA e atraso</li>" +
                            "</ul>",
                            example = "{\"situacao\":\"atraso\"," +
                                    "\"ignorarEmRemessa\":\"false\"," +
                                    "\"ignorarParcelasMultaJuros\":\"false\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>contrato:</strong> Ordena pelo contrato</li>" +
                                    "<li><strong>descricao:</strong> Ordena pela descrição</li>" +
                                    "<li><strong>dataVencimento:</strong> Ordena pela data de vencimento</li>" +
                                    "<li><strong>valor:</strong> Ordena pelo valor da parcela</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo nome da empresa</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "dataVencimento,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Parcela(s) encontradas com sucesso",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListMovParcelaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListMovParcelaDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("/by-matricula/{codMatricula}")
    public ResponseEntity<EnvelopeRespostaDTO> consultarPorMatricula(@PathVariable Integer codMatricula, @RequestParam(value = "filters", required = false) JSONObject filtros, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroMovParcelaJSON filtrosJson = new FiltroMovParcelaJSON(filtros);
            return ResponseEntityFactory.ok(movParcelaService.consultarPorCodigoPessoa(null, codMatricula, filtrosJson, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
