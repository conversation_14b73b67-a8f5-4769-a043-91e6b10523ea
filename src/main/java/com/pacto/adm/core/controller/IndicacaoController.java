package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.grupo.EnvelopeRespostaListGrupo;
import com.pacto.adm.core.dto.enveloperesposta.indicacao.EnvelopeRespostaIndicacaoDTO;
import com.pacto.adm.core.swagger.respostas.indicacao.ExemploRespostaListIndicacaoPaginacao;
import com.pacto.adm.core.services.interfaces.IndicacaoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/indicacoes")
@Tag(name = "Indicações")
public class IndicacaoController {

    @Autowired
    private IndicacaoService indicacaoService;

    @Autowired
    private RequestService requestService;

    @Operation(
            summary = "Consultar pessoas indicadas por um cliente",
            description = "Consulta as pessoas indicadas por um cliente pelo código de matrícula do cliente.",
            tags = {"Indicações"},
            parameters = {
                    @Parameter(name = "matricula", description = "Código de matrícula do cliente que realizou as indicações", example = "12345", required = true),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Atributos de ordenação disponíveis**\n" +
                                    "- **codigo**: Ordena pelo código da indicação\n" +
                                    "- **dataInicio**: Ordena pela data de início de acesso\n" +
                                    "- **tokenGympass**: Ordena pelo token do Gympass\n" +
                                    "- **valorGympass**: Ordena pelo valor do Gympass\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas por **codigo** em ordem descendente.",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                            @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida",
                                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ExemploRespostaListIndicacaoPaginacao.class),
                                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = ExemploRespostaListIndicacaoPaginacao.resposta)
                                    )
                            )
                    }
    )
    @GetMapping("/find-by-matricula/{matricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findByMatricula(@PathVariable Integer matricula,
                                                               @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                               @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(indicacaoService.findByMatricula(matricula, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
