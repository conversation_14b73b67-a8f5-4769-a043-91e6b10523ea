package com.pacto.adm.core.swagger.respostas.planoconta;

import com.pacto.adm.core.dto.planoconta.PlanoContaDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo despesas do plano de conta com paginação")
public class ExemploRespostaListPlanoContaPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<PlanoContaDTO> content;

    public List<PlanoContaDTO> getContent() {
        return content;
    }

    public void setContent(List<PlanoContaDTO> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"codigo\": 1,"
            + "    \"nome\": \"Despesas Operacionais\","
            + "    \"insideltv\": true"
            + "  }, {"
            + "    \"codigo\": 2,"
            + "    \"nome\": \"Custos de Manutenção\","
            + "    \"insideltv\": false"
            + "  }, {"
            + "    \"codigo\": 3,"
            + "    \"nome\": \"Despesas Administrativas\","
            + "    \"insideltv\": true"
            + "  }],"
            + "  \"totalElements\": 3,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
