package com.pacto.adm.core.swagger.respostas.solicitacaocompra;

import com.pacto.adm.core.dto.solicitacaocompra.SolicitacaoCompraDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas das requisições envolvendo uma solicitação de compra específica")
public class ExemploRespostaSolicitacaoCompra {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private SolicitacaoCompraDTO content;

    public SolicitacaoCompraDTO getContent() {
        return content;
    }

    public void setContent(SolicitacaoCompraDTO content) {
        this.content = content;
    }
}
