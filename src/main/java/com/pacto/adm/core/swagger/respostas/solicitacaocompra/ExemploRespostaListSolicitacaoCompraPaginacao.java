package com.pacto.adm.core.swagger.respostas.solicitacaocompra;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.solicitacaocompra.SolicitacaoCompraDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo solicitações de compra com paginação")
public class ExemploRespostaListSolicitacaoCompraPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<SolicitacaoCompraDTO> content;

    public List<SolicitacaoCompraDTO> getContent() {
        return content;
    }

    public void setContent(List<SolicitacaoCompraDTO> content) {
        this.content = content;
    }
}
