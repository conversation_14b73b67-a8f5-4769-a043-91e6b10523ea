package com.pacto.adm.core.swagger.respostas.observacaooperacao;

import com.pacto.adm.core.dto.observacaooperacao.ObservacaoOperacaoTotaisDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas das requisições envolvendo observações de operações com totais")
public class ExemploRespostaObservacaoOperacaoTotais {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ObservacaoOperacaoTotaisDTO content;

    public ObservacaoOperacaoTotaisDTO getContent() {
        return content;
    }

    public void setContent(ObservacaoOperacaoTotaisDTO content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": {"
            + "    \"observacoes\": [{"
            + "      \"codigo\": 1001,"
            + "      \"justificativa\": \"Cancelamento solicitado pelo cliente devido a mudança de cidade\","
            + "      \"dataOperacao\": \"2024-01-15T10:30:00.000Z\","
            + "      \"tipoObservacao\": \"PC\","
            + "      \"movParcela\": {"
            + "        \"codigo\": 1,"
            + "        \"valor\": 199.99"
            + "      },"
            + "      \"valorOperacao\": 199.99,"
            + "      \"usuarioResponsavel\": \"João Silva\","
            + "      \"tipoCancelamento\": \"Manual\","
            + "      \"pessoa\": {"
            + "        \"codigo\": 12345,"
            + "        \"nome\": \"Maria Santos\""
            + "      },"
            + "      \"codigoCliente\": 12345,"
            + "      \"matriculaCliente\": \"MAT001234\","
            + "      \"codigoMatriculaCliente\": 5678"
            + "    }],"
            + "    \"valorTotalParcelas\": 1599.99,"
            + "    \"paginador\": {"
            + "      \"totalElements\": 1,"
            + "      \"totalPages\": 1,"
            + "      \"first\": true,"
            + "      \"last\": true,"
            + "      \"size\": 10,"
            + "      \"number\": 0"
            + "    }"
            + "  }"
            + "}";
}
