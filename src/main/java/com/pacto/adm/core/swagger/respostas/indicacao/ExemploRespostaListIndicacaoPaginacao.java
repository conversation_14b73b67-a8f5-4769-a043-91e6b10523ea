package com.pacto.adm.core.swagger.respostas.indicacao;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.indicacao.IndicacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo indicações com paginação")
public class ExemploRespostaListIndicacaoPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<IndicacaoDTO> content;

    public List<IndicacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<IndicacaoDTO> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"codigo\": 5678,"
            + "    \"dia\": \"2023-06-15T00:00:00Z\","
            + "    \"observacao\": \"Indicação de cliente novo para planos A e B.\","
            + "    \"origemSistema\": 1,"
            + "    \"clienteQueIndicou\": {"
            + "      \"codigo\": 12345,"
            + "      \"nome\": \"João Silva\","
            + "      \"codigoMatricula\": 98765"
            + "    },"
            + "    \"colaboradorQueIndicou\": {"
            + "      \"codigo\": 456,"
            + "      \"nome\": \"Maria Santos\""
            + "    },"
            + "    \"colaboradorResponsavel\": {"
            + "      \"codigo\": 789,"
            + "      \"nome\": \"Carlos Oliveira\""
            + "    },"
            + "    \"evento\": {"
            + "      \"codigo\": 101,"
            + "      \"nome\": \"Evento de Indicações\""
            + "    },"
            + "    \"responsavelCadastro\": {"
            + "      \"codigo\": 321,"
            + "      \"nome\": \"Ana Costa\""
            + "    },"
            + "    \"indicados\": [{"
            + "      \"codigo\": 1234,"
            + "      \"cpf\": \"123.456.789-00\","
            + "      \"dataLancamento\": \"2023-06-01T00:00:00Z\","
            + "      \"email\": \"<EMAIL>\","
            + "      \"lead\": true,"
            + "      \"nomeConsulta\": \"João da Silva\","
            + "      \"nomeIndicado\": \"Maria Oliveira\","
            + "      \"origemSistema\": 1,"
            + "      \"telefone\": \"(11) 987654321\","
            + "      \"telefoneIndicado\": \"(11) 998765432\","
            + "      \"cliente\": {"
            + "        \"codigo\": 11111,"
            + "        \"nome\": \"Maria Oliveira\","
            + "        \"codigoMatricula\": 55555"
            + "      },"
            + "      \"empresa\": {"
            + "        \"codigo\": 1,"
            + "        \"nome\": \"Academia Exemplo\""
            + "      },"
            + "      \"indicacao\": {"
            + "        \"codigo\": 5678"
            + "      },"
            + "      \"objecao\": {"
            + "        \"codigo\": 22,"
            + "        \"descricao\": \"Preço muito alto\""
            + "      }"
            + "    }, {"
            + "      \"codigo\": 5679,"
            + "      \"cpf\": \"987.654.321-00\","
            + "      \"dataLancamento\": \"2023-06-20T00:00:00Z\","
            + "      \"email\": \"<EMAIL>\","
            + "      \"lead\": false,"
            + "      \"nomeConsulta\": \"Carlos Souza\","
            + "      \"nomeIndicado\": \"Ana Pereira\","
            + "      \"origemSistema\": 2,"
            + "      \"telefone\": \"(21) 987654322\","
            + "      \"telefoneIndicado\": \"(21) 998765433\","
            + "      \"cliente\": {"
            + "        \"codigo\": 22222,"
            + "        \"nome\": \"Ana Pereira\","
            + "        \"codigoMatricula\": 66666"
            + "      },"
            + "      \"empresa\": {"
            + "        \"codigo\": 1,"
            + "        \"nome\": \"Academia Exemplo\""
            + "      },"
            + "      \"indicacao\": {"
            + "        \"codigo\": 5678"
            + "      },"
            + "      \"objecao\": null"
            + "    }]"
            + "  }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
