package com.pacto.adm.core.swagger.respostas.planoconta;

import com.pacto.adm.core.dto.ProdutoDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo produtos do plano de conta com paginação")
public class ExemploRespostaListProdutoPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ProdutoDTO> content;

    public List<ProdutoDTO> getContent() {
        return content;
    }

    public void setContent(List<ProdutoDTO> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"codigo\": 1,"
            + "    \"descricao\": \"Plano Mensal Básico\","
            + "    \"valorFinal\": 99.99,"
            + "    \"desativado\": false,"
            + "    \"tipoProduto\": \"PM\","
            + "    \"tipoProdutoDescricao\": \"Mês de Referência Plano\","
            + "    \"prevalecerVigenciaContrato\": true,"
            + "    \"bloqueiaPelaVigencia\": false,"
            + "    \"nrDiasVigencia\": 30,"
            + "    \"pontos\": 100,"
            + "    \"tipoVigencia\": \"Mensal\","
            + "    \"renovavelAutomaticamente\": true,"
            + "    \"contratoTextoPadrao\": 1"
            + "  }, {"
            + "    \"codigo\": 2,"
            + "    \"descricao\": \"Kit Equipamentos\","
            + "    \"valorFinal\": 149.90,"
            + "    \"desativado\": false,"
            + "    \"tipoProduto\": \"EQ\","
            + "    \"tipoProdutoDescricao\": \"Equipamento\","
            + "    \"prevalecerVigenciaContrato\": false,"
            + "    \"bloqueiaPelaVigencia\": false,"
            + "    \"nrDiasVigencia\": 365,"
            + "    \"pontos\": 50,"
            + "    \"tipoVigencia\": \"Anual\","
            + "    \"renovavelAutomaticamente\": false,"
            + "    \"contratoTextoPadrao\": 2"
            + "  }],"
            + "  \"totalElements\": 2,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
