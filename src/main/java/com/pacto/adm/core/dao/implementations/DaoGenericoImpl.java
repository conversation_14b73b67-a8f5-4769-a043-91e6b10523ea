package com.pacto.adm.core.dao.implementations;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.UteisValidacao;
import com.pacto.config.utils.reflexao.UtilReflection;
import com.pacto.adm.core.dao.interfaces.DaoGenerico;
import org.hibernate.Session;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;

import javax.persistence.EntityExistsException;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceException;
import javax.persistence.PersistenceUnit;
import javax.persistence.Query;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DaoGenericoImpl<T, ID extends Serializable> implements DaoGenerico<T, ID> {

    private final Class<T> clazz;
    @PersistenceUnit(unitName = "zwClientPU")
    private EntityManagerFactory entityManagerFactory;

    @Autowired
    private MessageSource messageSource;
    @Autowired
    private RequestService requestService;

    @SuppressWarnings("unchecked")
    public DaoGenericoImpl() {
        this.clazz = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
    }

    private EntityManager getEntityManager() {
        return entityManagerFactory.createEntityManager();
    }

    @Override
    public Session getCurrentSession() {
        try {
            return (Session) getEntityManager().getDelegate();
        } catch (Exception ex) {
            Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    @Override
    public T findById(ID id) throws Exception {
        return getEntityManager().find(clazz, id);
    }

    @Override
    public T findByName(String nome) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("nome", nome);
        List<T> records = findByParam(new StringBuilder("WHERE unaccent(trim(obj.nome)) = unaccent(trim(:nome))"), params);
        if(records != null && records.size() > 0){
            return records.get(0);
        }else{
            return null;
        }
    }

    @Override
    public T findByDescription(String descricao) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("descricao", descricao);
        List<T> records = findByParam(new StringBuilder("WHERE unaccent(trim(obj.descricao)) = unaccent(trim(:descricao))"), params);
        if(records != null && records.size() > 0){
            return records.get(0);
        }else{
            return null;
        }
    }

    @Override
    public T findByType(String type) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("tipo", type);
        List<T> records = findByParam(new StringBuilder("WHERE unaccent(trim(obj.tipo)) = unaccent(trim(:type))"), params);
        if(records != null && records.size() > 0){
            return records.get(0);
        }else{
            return null;
        }
    }

    public Optional<T> findByParam(String[] atributos, Object[] valores) {
        EntityManager em = getEntityManager();
        String hql = "select obj from " + clazz.getSimpleName() + " obj " + getWhereClause(atributos);
        Query query = em.createQuery(hql);
        setWhereValues(atributos, valores, query);
        return query.getResultStream().findFirst();
    }

    @Override
    public List<T> findAll() throws Exception {
        return getEntityManager().createQuery("from " + clazz.getName())
                .getResultList();
    }

    @Override
    public List<T> findByParam(final StringBuilder whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder("select obj  from ").append(clazz.getSimpleName()).append(" obj ");
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        return query.getResultList();
    }

    @Override
    public List<T> findByParam(StringBuilder whereClause, Map<String, Object> params, int max, int index) throws Exception {
        StringBuilder s = new StringBuilder("select obj from ").append(clazz.getSimpleName()).append(" obj ");
        s.append(whereClause != null ? whereClause.toString() : "");
        Query q = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                q.setParameter(p, params.get(p));
            }
        }

        if (max != 0)
            q.setMaxResults(max);
        if (index != 0)
            q.setFirstResult(index);

        return q.getResultList();
    }


    public List<T> findAll(Long limit, Long offset) throws Exception {
        Query q = getCurrentSession().createQuery("from " + clazz.getName());

        if (limit != 0)
            q.setMaxResults(limit.intValue());

        if (offset != 0)
            q.setFirstResult(offset.intValue());

        return q.getResultList();
    }

    public T save(T obj) throws Exception {
        EntityManager em = getEntityManager();
        if (!em.getTransaction().isActive())
            em.getTransaction().begin();
        try {
            em.persist(obj);
            em.getTransaction().commit();
        } catch (EntityExistsException e) {
            em.getTransaction().rollback();
            throw e;
        }
        return obj;
    }

    public T update(T obj) throws Exception {
        EntityManager em = getEntityManager();
        try {
            em.getTransaction().begin();
            obj = em.merge(obj);
            em.getTransaction().commit();
        } catch (EntityExistsException e) {
            em.getTransaction().rollback();
            throw e;
        } catch (Exception e) {
            em.getTransaction().rollback();
            throw e;
        }
        return obj;
    }

    public void delete(T obj) throws Exception {
        EntityManager em = getEntityManager();
        try {
            em.getTransaction().begin();
            em.remove(em.getReference(clazz, UtilReflection.getValor(obj, "codigo")));
            em.getTransaction().commit();
        } catch (PersistenceException pe) {
            em.getTransaction().rollback();
            pe.printStackTrace();
            if (pe.getCause() != null && pe.getCause().getCause() != null && pe.getCause().getCause().getClass() == ConstraintViolationException.class) {
                String mensagem = pe.getCause().getCause().getCause().getMessage();
                throw new Exception(messageSource.getMessage("registro.com.relacionamento", new Object[]{mensagem.substring(mensagem.lastIndexOf(" "))}, new Locale(requestService.getLocale())));
            }
        } catch (Exception e1) {
            em.getTransaction().rollback();
            e1.printStackTrace();
            throw e1;
        }
    }

    public void delete(ID id) throws Exception {
        deleteComParam(new String[]{"id"}, new Object[]{id});
    }

    public void deleteComParam(String[] atributos, Object[] valores) throws Exception {
        EntityManager em = getEntityManager();
        try {
            em.getTransaction().begin();
            String hql = "delete from " + clazz.getSimpleName() + getWhereClause(atributos);
            Query query = em.createQuery(hql);
            setWhereValues(atributos, valores, query);
            query.executeUpdate();
            em.getTransaction().commit();
        } catch (PersistenceException pe) {
            em.getTransaction().rollback();
            pe.printStackTrace();
            if (pe.getCause() != null && pe.getCause().getCause() != null && pe.getCause().getClass() == ConstraintViolationException.class) {
                String mensagem = pe.getCause().getCause().getMessage();
                throw new Exception(messageSource.getMessage("registro.com.relacionamento", new Object[]{mensagem.substring(mensagem.lastIndexOf(" "))}, new Locale(requestService.getLocale())));
            }
        }
    }

    private StringBuilder getWhereClause(final String[] atributos) {
        StringBuilder query = new StringBuilder();
        if (atributos == null) {
            return query;
        }
        if (atributos.length > 0) {
            query.append(" where ");
        }

        for (int i = 0; i < atributos.length; i++) {
            String atributo = atributos[i];
            if (atributo.contains("like")) {
                atributo = atributo.replace(".like", "");
                query.append("LOWER(obj.").append(atributo).append(") LIKE :").append(atributo);
            } else if (atributo.contains("between")) {
                final String attr = atributo.replaceAll("\\.", "").replaceAll("between", "").replaceAll(" ", "");
                final String attr1 = attr + "1";
                final String attr2 = attr + "2";
                query.append(atributo).append(" :").append(attr1).append(" and :").append(attr2);
            } else if (atributo.contains("<>")) {
                query.append(atributo).append(" :").append(atributo.replaceAll("<>", "").replaceAll("\\.", "").trim());
            } else if (atributo.contains(">")) {
                query.append(atributo).append(" :").append(atributo.replaceAll(">", "").replaceAll("\\.", "").trim());
            } else if (atributo.contains("<")) {
                query.append(atributo).append(" :").append(atributo.replaceAll("<", "").replaceAll("\\.", "").trim());
            } else {
                query.append(atributo).append(" = :").append(atributo.replaceAll("\\.", ""));
            }
            if (i + 1 < atributos.length) {
                query.append(" and ");
            }
        }
        return query;
    }

    private void setWhereValues(String[] atributos, Object[] valores, Query q) {
        if (atributos == null || valores == null) {
            return;
        }
        List<String> l = new ArrayList<>(Arrays.asList(atributos));
        List<Object> lv = new ArrayList<>(Arrays.asList(valores));
        for (int i = 0; i < atributos.length; i++) {
            final String atributo = atributos[i];
            final String attr = atributo.replaceAll(".like", "").replaceAll("\\.", "").replaceAll("between", "").replaceAll(" ", "");
            if (atributo.contains("between")) {
                lv.add(i, null);
                final String attr1 = attr + "1";
                final String attr2 = attr + "2";

                l.add(attr1);
                l.add(attr2);
            }
        }
        Object[] attrs = l.toArray();
        Object[] val = lv.toArray();
        for (int i = 0; i < attrs.length; i++) {
            String atributo = (String) attrs[i];
            if (atributo.contains("between")) {
                continue;
            }
            atributo = atributo.replace(".like", "").replaceAll("\\.", "").replaceAll("<", "").replaceAll(">", "").trim();
            Object valor = val[i];


            if (valor != null) {
                if (valor.getClass() == Date.class) {
                    q.setParameter(atributo, (Date) valor, TemporalType.DATE);
                } else if (attrs[i].toString().contains(".like")) {
                    q.setParameter(atributo, valor + "%");
                } else {
                    q.setParameter(atributo, valor);
                }
            } else {
                q.setParameter(atributo, null);
            }
        }
    }

    @Override
    public Number countWithParam(final String atributoCount, final StringBuilder whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder("select COUNT(").append(atributoCount).append(") from ").append(clazz.getSimpleName()).append(" obj ");
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        return (Number) query.getSingleResult();
    }

    /**
     * Cria uma nova sessão independente para a Thread Atual, útil para o Paralelismo de Recursos
     * ATENÇÃO: utilizar obrigatoriamente 'session.close()' ou 'connection.close()' no bloco 'finally' para liberar
     * os recursos e evitar vazamentos de conexão e memória
     * @return SessionImplementor
     * @throws Exception
     */
    @Override
    public SessionImplementor createSessionCurrentWork() throws Exception {
        return (SessionImplementor) newSession();
    }

    private Session newSession() {
        try {
            return (Session) getEntityManager().getDelegate();
        } catch (Exception ex) {
            Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    @Override
    public ResultSet createStatement(final Connection connection, final String sql) throws Exception {
        Statement stm = connection.createStatement();
        return stm.executeQuery(sql);
    }

    public void processarPaginador(StringBuilder sql, String orderByDefault, PaginadorDTO paginadorDTO) throws ServiceException {
        if (paginadorDTO != null) {

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            paginadorDTO.setQuantidadeTotalElementos(contar(sql.toString()).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            //adicionar ordenação
            if (!UteisValidacao.emptyString(orderByDefault)
                    && paginadorDTO.getSQLOrderByUse() != null
                    && UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse().trim())) {
                sql.append(" ORDER BY ").append(orderByDefault).append(" \n");
            } else {
                sql.append(paginadorDTO.getSQLOrderByUse());
            }

            //adicionar limit
            sql.append(paginadorDTO.getSQLLimitByUse());

            //adicionar offset
            sql.append(paginadorDTO.getSQLOffsetByUse());
        }
    }

    public Integer contar(final String sql) throws ServiceException {
        try {
            String sqlBusca = "SELECT COUNT(*) FROM (" + sql + ") as a";
            AtomicReference<Integer> qtd = new AtomicReference<>(0);
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try (ResultSet rs = createStatement(connection, sqlBusca)) {
                        if (rs.next()) {
                            qtd.set(rs.getInt(1));
                        } else {
                            qtd.set(0);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            }
            return qtd.get();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
