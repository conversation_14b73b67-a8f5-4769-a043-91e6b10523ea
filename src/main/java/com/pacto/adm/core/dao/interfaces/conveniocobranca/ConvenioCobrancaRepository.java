package com.pacto.adm.core.dao.interfaces.conveniocobranca;

import com.pacto.adm.core.entities.conveniocobranca.ConvenioCobranca;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConvenioCobrancaRepository extends JpaRepository<ConvenioCobranca, Integer> {

    @Query(
            "SELECT cc FROM ConvenioCobranca cc INNER JOIN cc.empresas emp WHERE emp.empresa.codigo = :codigoEmpresa"
    )
    List<ConvenioCobranca> findByEmpresaId(@Param("codigoEmpresa") Integer codigoEmpresa);

}
