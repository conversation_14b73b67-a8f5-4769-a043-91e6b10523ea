package com.pacto.adm.core.dao.implementations;


import com.pacto.adm.core.dao.interfaces.IntegracaoKobanaDao;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.kobana.IntegracaoKobana;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class IntegracaoKobanaDaoImpl extends DaoGenericoImpl<Empresa, Integer> implements IntegracaoKobanaDao {
    @Override
    public IntegracaoKobana findByEmpresa(Integer codEmpresa) throws Exception {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append("SELECT obj FROM IntegracaoKobana obj ");
        hql.append(" WHERE obj.empresa = :codEmpresa");

        params.put("codEmpresa", codEmpresa);

        Query query = getCurrentSession().createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        if (query.getResultList().size() > 0) {
            return (IntegracaoKobana) query.getResultList().get(0);
        } else {
            return null;
        }
    }
}
