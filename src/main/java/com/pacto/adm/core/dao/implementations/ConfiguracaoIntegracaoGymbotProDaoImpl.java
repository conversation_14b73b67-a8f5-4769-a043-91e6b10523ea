package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoGymbotProDao;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoGymbotPro;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ConfiguracaoIntegracaoGymbotProDaoImpl extends  DaoGenericoImpl<ConfiguracaoIntegracaoGymbotPro, Integer> implements ConfiguracaoIntegracaoGymbotProDao {

    @Override
    public List<ConfiguracaoIntegracaoGymbotPro> findByEmpresaId (Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoIntegracaoGymbotPro obj ");
        s.append(" WHERE obj.empresa = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        List<ConfiguracaoIntegracaoGymbotPro>  configEntity = new ArrayList<>();
        try {
            configEntity = (List<ConfiguracaoIntegracaoGymbotPro> ) query.getResultList();
        } catch (Exception e) {
        }
        return configEntity;
    }

    @Override
    public String findbyFluxoTelaAluno(String empresaId) {

        // Criação da query usando JPQL
        String jpql = "SELECT c.token " +
                "FROM ConfiguracaoIntegracaoGymbotPro c " +
                "WHERE c.empresa = :empresaId " +
                "AND c.ativo = true " +
                "AND c.tipoFluxo = '"+2+"'";


        Query query = getCurrentSession().createQuery(jpql);
        query.setParameter("empresaId",Integer.valueOf(empresaId));

        getCurrentSession().clear();
        List<String> urls = query.getResultList();

        if (UteisValidacao.emptyList(urls)) {
            return "";
        }

        return urls.get(0);
    }
}
