package com.pacto.adm.core.dao.implementations.nativerepositories.observacaooperacao;

import com.pacto.adm.core.dao.interfaces.nativerepositories.observacaooperacao.ObservacaoOperacaoNativeRepository;
import com.pacto.adm.core.dto.MovParcelaDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.observacaooperacao.ObservacaoOperacaoDTO;
import com.pacto.adm.core.dto.observacaooperacao.ObservacaoOperacaoTotaisDTO;
import com.pacto.adm.core.enumerador.TipoObservacaoOperacaoEnum;
import com.pacto.adm.core.util.QueryUtils;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Repository criado para queries nativas que não dá para usar o repository padrão do JPA. Como queries que dependem
 * de muitas verificações para serem montadas.
 */
@Repository
public class ObservacaoOperacaoNativeRepositoryImpl implements ObservacaoOperacaoNativeRepository {

    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public ObservacaoOperacaoTotaisDTO consultarPorNomeEntidadePorDataAlteracaoPorOperacao(FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO, boolean buscarComAdministrador, TipoObservacaoOperacaoEnum tipoObservacaoOperacaoEnum) throws Exception {

        if (filtros.getInicio() == null) {
            throw new Exception("Deve ser informado o filtro data início para consulta!");
        }

        if (filtros.getFim() == null) {
            throw new Exception("Deve ser informado o filtro data fim para consulta!");
        }
        List<ObservacaoOperacaoDTO> lista;

        String sql = sqlConsultarPorNomeEntidadePorDataAlteracao(filtros, paginadorDTO, buscarComAdministrador, tipoObservacaoOperacaoEnum).toString();

        Query query = entityManager.createNativeQuery(sql, Tuple.class);

        int size = 3;
        int page = 1;

        if (paginadorDTO != null) {
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);

        setParamConsultarPorNomeEntidadePorDataAlteracao(query, filtros, tipoObservacaoOperacaoEnum);

        List<Tuple> resultado = query.getResultList();

        lista = resultado.stream().map(tuple -> {
            String justificativa = tuple.get("justificativa", String.class);
            return new ObservacaoOperacaoDTO(
                    tuple.get("codigo", Integer.class),
                    StringUtils.hasText(justificativa) ? justificativa : tuple.get("obCancel", String.class),
                    tuple.get("dataoperacao", Date.class),
                    tuple.get("tipooperacao", String.class),
                    new MovParcelaDTO(
                            tuple.get("codigoParcela", Integer.class),
                            BigDecimal.valueOf(tuple.get("valorParcela", Float.class))
                    ),
                    tuple.get("valor", Double.class),
                    tuple.get("usuarioresponsavel", String.class),
                    StringUtils.hasText(tuple.get("obCancel", String.class)) ? "Cancelamento Contrato" : "Manual",
                    new PessoaDTO(
                            tuple.get("codigoPessoaCliente", Integer.class),
                            tuple.get("nomeCliente", String.class)
                    ),
                    tuple.get("codigoCliente", Integer.class),
                    tuple.get("clienteMatricula", String.class),
                    tuple.get("clienteCodigoMatricula", Integer.class)
            );
        }).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParamConsultarPorNomeEntidadePorDataAlteracao(countQuery, filtros, tipoObservacaoOperacaoEnum);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        ObservacaoOperacaoTotaisDTO observacaoOperacaoTotaisDTO = new ObservacaoOperacaoTotaisDTO();
        observacaoOperacaoTotaisDTO.setObservacoes(lista);

        if (tipoObservacaoOperacaoEnum.equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            Query valorTotalQuery = entityManager.createNativeQuery("select sum(mp.valorparcela) " + sql.substring(sql.indexOf("FROM")).replaceAll("(?i)ORDER BY.*$", ""));
            setParamConsultarPorNomeEntidadePorDataAlteracao(valorTotalQuery, filtros, tipoObservacaoOperacaoEnum);
            Float totalParcelas = (Float) valorTotalQuery.getSingleResult();
            if (totalParcelas != null) {
                observacaoOperacaoTotaisDTO.setValorTotalParcelas(BigDecimal.valueOf(totalParcelas));
            }
        }

        return observacaoOperacaoTotaisDTO;
    }

    private StringBuilder sqlConsultarPorNomeEntidadePorDataAlteracao(
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO,
            boolean buscarComAdministrador, TipoObservacaoOperacaoEnum tipoObservacaoOperacaoEnum
    ) {
        StringBuilder sqlStr = new StringBuilder("SELECT oo.*,\n" +
                " co.observacao as obCancel\n");

        if (tipoObservacaoOperacaoEnum.equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            sqlStr.append(", mp.codigo as codigoParcela,\n" +
                    "mp.valorparcela as valorParcela,\n" +
                    "c.codigo as codigoCliente,\n" +
                    "c.matricula as clienteMatricula,\n" +
                    "c.codigoMatricula as clienteCodigoMatricula,\n" +
                    "p.codigo as codigoPessoaCliente,\n" +
                    "p.nome as nomeCliente\n");
        }

        sqlStr.append("FROM observacaooperacao oo\n");

        if (tipoObservacaoOperacaoEnum.equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            sqlStr.append("INNER JOIN movparcela mp ON mp.codigo = oo.movparcela\n");
            sqlStr.append("LEFT JOIN contratooperacao co ON co.contrato = mp.contrato and co.tipooperacao = 'CA'\n");
            sqlStr.append("left join pessoa p on p.codigo = mp.pessoa\n");
            sqlStr.append("left join cliente c on c.pessoa = p.codigo\n");
        }

        StringBuilder sqlWhere = new StringBuilder("WHERE oo.dataoperacao between :dataInicio AND :dataFim\n" +
                " and " + ((!buscarComAdministrador) ? "not" : "") + " (oo.usuarioresponsavel ilike 'ADMINISTRADOR' )\n");

        sqlWhere.append("AND oo.tipoOperacao = :tipoOperacao\n");

        if (tipoObservacaoOperacaoEnum.equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
                LinkedHashMap<String, Class<?>> columTypes = new LinkedHashMap<String, Class<?>>();
                if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
                    columTypes.put("p.nome", null);
                    columTypes.put("mp.codigo", Integer.class);
                }
                columTypes.put("oo.valor", Float.class);
                columTypes.put("oo.dataOperacao", Date.class);
                columTypes.put("oo.usuarioResponsavel", null);
                columTypes.put("oo.justificativa", null);
                sqlWhere.append("AND ");
                QueryUtils.buildSqlQuickSearchByType(sqlWhere, columTypes);
            }
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            sqlWhere.append(" AND oo.usuarioresponsavel IN :colaboradores\n");
        }

        if (filtroBIControleOperacoesJSON.getEmpresa() != 0) {
            sqlWhere.append("AND mp.empresa = :codigoEmpresa\n");
        }

        sqlStr.append(sqlWhere);

        String sortField = "p.nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];

                if (tipoObservacaoOperacaoEnum.equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
                    if (sortField.equals("pessoa")) {
                        sortField = "p.nome";
                    }
                    if (sortField.equals("parcela")) {
                        sortField = "mp.codigo";
                    }
                }
                if (sortField.equals("valor")) {
                    sortField = "oo.valor";
                }
                if (sortField.equals("dataEstorno")) {
                    sortField = "oo.dataEstorno";
                }
                if (sortField.equals("usuarioResponsavel")) {
                    sortField = "oo.usuarioResponsavel";
                }
                if (sortField.equals("justificativa")) {
                    sortField = "oo.justificativa";
                }
                sortOrder = paginadorDTO.getSort().split(",")[1];
            }

        }
        sqlStr.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sqlStr;
    }

    private void setParamConsultarPorNomeEntidadePorDataAlteracao(Query query, FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, TipoObservacaoOperacaoEnum tipoObservacaoOperacaoEnum) throws Exception {
        query.setParameter("dataInicio", Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(filtroBIControleOperacoesJSON.getInicio())));
        query.setParameter("dataFim", Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(filtroBIControleOperacoesJSON.getFim())));
        query.setParameter("tipoOperacao", tipoObservacaoOperacaoEnum.getTipo());
        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIControleOperacoesJSON.getQuickSearchValue(), query);
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            query.setParameter("colaboradores", filtroBIControleOperacoesJSON.getColaboradores());
        }

        if (filtroBIControleOperacoesJSON.getEmpresa() != 0) {
            query.setParameter("codigoEmpresa", filtroBIControleOperacoesJSON.getEmpresa());
        }
    }
}
