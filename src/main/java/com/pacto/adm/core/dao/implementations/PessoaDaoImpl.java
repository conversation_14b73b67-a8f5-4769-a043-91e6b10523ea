package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.PessoaDao;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.enumerador.TipoColaboradorEnum;
import com.pacto.adm.core.enumerador.UsoCreditoPersonalEnum;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicReference;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class PessoaDaoImpl extends DaoGenericoImpl<Pessoa, Integer> implements PessoaDao {

    @Value("${url.fotos.nuvem}")
    private String urlFotosNuvem;

    @Override
    public boolean salvaSenhaDeAcesso(Pessoa pessoa, String senhaAcesso, Boolean habilitaSenhaAcesso) {
        AtomicReference<Boolean> retorno = new AtomicReference<>(false);
        String sql = "update pessoa set senhaAcesso = ?, liberasenhaacesso = ? where codigo = ?;";
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    PreparedStatement sqlAlterar = con.prepareStatement(sql);
                    int i = 0;
                    sqlAlterar.setString(++i, senhaAcesso);
                    sqlAlterar.setBoolean(++i, habilitaSenhaAcesso);
                    sqlAlterar.setInt(++i, pessoa.getCodigo());

                    int x = sqlAlterar.executeUpdate();
                    retorno.set(x > 0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return retorno.get();
    }

    private static final int MAXIMO_RESULTADOS = 50;

    @Override
    public List<PessoaDTO> consultaColaboradorOuCliente(String nome, Integer matricula, boolean ultimosAcessos) throws Exception {
        List<PessoaDTO> pessoas = new ArrayList<>();
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(
                    con -> {
                        StringBuilder sql = new StringBuilder();
                        sql.append(" select p.codigo, p.nome, p.fotokey, p.cfp, \n");
                        sql.append(" c.codigo as colaborador, c.usocreditospersonal, cli.codigo as cliente, cat.nome as categoria, \n");
                        sql.append(" cli.situacao, s.situacaocontrato, c.situacao as situacaocolaborador, \n");
                        sql.append(" c.codigo as colaborador, cli.codigo as cliente from pessoa p\n");
                        sql.append(" left join colaborador c on c.pessoa = p.codigo AND c.situacao = 'AT'\n");
                        sql.append(" left join cliente cli on cli.pessoa = p.codigo\n");
                        sql.append(" left join categoria cat on cat.codigo = cli.categoria\n");
                        sql.append(" left join situacaoclientesinteticodw s on s.codigopessoa = p.codigo\n");
                        sql.append(" WHERE (cli.codigo is not null or c.codigo is not null) ");
                        if(nome != null){
                            sql.append(" AND LOWER(p.nome) LIKE '").append(nome.toLowerCase()).append("%'");
                        }
                        if(matricula != null){
                            sql.append(" AND cli.codigoMatricula = ").append(matricula);
                        }
                        if(ultimosAcessos){
                            sql.append(" and s.dataultimoacesso is not null\n");
                            sql.append(" order by s.dataultimoacesso desc limit 5");
                        } else {
                            sql.append(" order by p.nome limit ").append(MAXIMO_RESULTADOS);
                        }


                        try (ResultSet rs = createStatement(con, sql.toString())) {
                            while (rs.next()){
                                if (!UteisValidacao.emptyNumber(rs.getInt("cliente"))) {
                                    PessoaDTO pessoa = new PessoaDTO();
                                    pessoa.setCodigo(rs.getInt("codigo"));
                                    pessoa.setNome(rs.getString("nome").toLowerCase());
                                    pessoa.setFotoKey(rs.getString("fotokey"));

                                    pessoa.setUrlFoto(Uteis.obterUrlFotoDaNuvem(pessoa.getFotoKey()));

                                    pessoa.setCpf(rs.getString("cfp"));
                                    pessoa.setTipo("cliente");
                                    pessoa.setCategoria(UteisValidacao.emptyString(rs.getString("categoria")) ? "-" : rs.getString("categoria"));
                                    pessoa.setSituacao(rs.getString("situacao"));
                                    pessoa.setSituacaoContrato(rs.getString("situacaocontrato"));
                                    pessoa.setCodCliente(rs.getInt("cliente"));

                                    pessoas.add(pessoa);
                                }
                                if (!UteisValidacao.emptyNumber(rs.getInt("colaborador"))) {
                                    PessoaDTO pessoa = new PessoaDTO();
                                    pessoa.setCodigo(rs.getInt("codigo"));
                                    pessoa.setNome(rs.getString("nome").toLowerCase());
                                    pessoa.setFotoKey(rs.getString("fotokey"));
                                    pessoa.setUrlFoto(Uteis.obterUrlFotoDaNuvem(pessoa.getFotoKey()));
                                    pessoa.setCpf(rs.getString("cfp"));
                                    pessoa.setTipo("colaborador");

                                    StringBuilder categoria = new StringBuilder();
                                    pessoa.setTipoColaborador("");
                                    try (PreparedStatement stm = con.prepareStatement("select descricao from tipocolaborador where colaborador = " + rs.getInt("colaborador"));
                                         ResultSet rsTipos = stm.executeQuery()) {
                                        while (rsTipos.next()) {
                                            TipoColaboradorEnum tipo = TipoColaboradorEnum.getTipo(rsTipos.getString("descricao"));
                                            if (tipo != null) {
                                                pessoa.setTipoColaborador(pessoa.getTipoColaborador() + tipo.getSigla() + ",");
                                                categoria.append(",").append(tipo.getDescricao());
                                            }
                                        }
                                    }

                                    pessoa.setCategoria(categoria.toString().replaceFirst(",", ""));
                                    pessoa.setSituacao(rs.getString("situacaocolaborador"));
                                    if (pessoa.getSituacao().equals("NA")) {
                                        pessoa.setSituacao("IN");
                                    }
                                    pessoa.setSituacaoContrato("");
                                    UsoCreditoPersonalEnum usoCreditosPersonal = UsoCreditoPersonalEnum.getEnum(rs.getInt("usocreditospersonal"));
                                    if (usoCreditosPersonal == null || usoCreditosPersonal.equals(UsoCreditoPersonalEnum.SOMENTE_PRE_PAGO)) {
                                        pessoa.setTipoCompraCredito("pre");
                                    } else {
                                        pessoa.setTipoCompraCredito("pos");
                                    }

                                    pessoas.add(pessoa);
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            con.close();
                        }
                    }
            );
        }
        return pessoas;
    }

    @Override
    public List<Pessoa> findByNome(String nome, Integer matricula, PaginadorDTO paginadorDTO) throws Exception {
        if (getCurrentSession() != null) {
            getCurrentSession().clear();
        }
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" LEFT JOIN Cliente cli ON cli.pessoa.codigo = obj.codigo ");
        where.append(" LEFT JOIN Colaborador col ON col.pessoa.codigo = obj.codigo AND col.situacao = 'AT' ");
        where.append(" WHERE (cli.codigo is not null or col.codigo is not null) ");
        if(nome != null){
            where.append(" AND LOWER(obj.nome) LIKE '").append(nome.toLowerCase()).append("%'");
        }
        if(matricula != null){
            where.append(" AND cli.codigoMatricula = ").append(matricula);
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }
        where.append(" order by obj.nome ");

        return findByParam(where, params, maxResults, indiceInicial);
    }

    @Override
    public Pessoa findByCliente(Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT cli.pessoa FROM Cliente cli WHERE cli.codigo = :codigoCliente");
        Query query = getCurrentSession().createQuery(sql.toString());
        query.setParameter("codigoCliente", codigoCliente);

        try {
            return (Pessoa) query.getSingleResult();
        } catch (NoResultException ex) {
            return null;
        }
    }
}
