package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.ProdutoSugerido;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface ProdutoSugeridoDao extends DaoGenerico<ProdutoSugerido, Integer> {

    public List<ProdutoSugerido> findAllByCodModalidade(Integer codModalidade) throws Exception;

    public List<ProdutoSugerido> findAllByCodModalidadePaginado(Integer codModalidade, PaginadorDTO paginadorDTO) throws Exception;


    }
