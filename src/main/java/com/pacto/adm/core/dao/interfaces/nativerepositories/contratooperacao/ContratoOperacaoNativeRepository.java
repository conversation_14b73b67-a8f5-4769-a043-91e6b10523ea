package com.pacto.adm.core.dao.interfaces.nativerepositories.contratooperacao;

import com.pacto.adm.core.dto.ClienteComBonusDTO;
import com.pacto.adm.core.dto.ClientesCanceladosDTO;
import com.pacto.adm.core.dto.ContratoOperacaoRetroativaDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface ContratoOperacaoNativeRepository {
    List<ContratoOperacaoRetroativaDTO> consultarOperacoesContratoRetroativa(FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    List<ClientesCanceladosDTO> consultarClientesCancelados(FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    List<ClienteComBonusDTO> findClientesComBonus(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception;
}
