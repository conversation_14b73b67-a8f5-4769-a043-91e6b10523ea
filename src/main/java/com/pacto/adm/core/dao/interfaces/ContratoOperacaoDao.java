package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.ContratoOperacaoRetroativaDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroContratoOperacaoJSON;
import com.pacto.adm.core.entities.contrato.ContratoOperacao;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ContratoOperacaoDao extends DaoGenerico<ContratoOperacao, Integer> {

    List<ContratoOperacao> findAll(FiltroContratoOperacaoJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    List<ContratoOperacao> findAllByContrato(Integer codigoContrato) throws Exception;
    List<ContratoOperacao> findAllByContrato(Integer codigoContrato, PaginadorDTO paginadorDTO) throws Exception;


    boolean consultarCodigoContratoETipoOperacao(Integer codContrato, String tipoOperacao) throws Exception;

}
