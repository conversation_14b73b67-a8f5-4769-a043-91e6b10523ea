package com.pacto.adm.core.dao.implementations.nativerepositories.justificativaoperacao;

import com.pacto.adm.core.dao.interfaces.nativerepositories.justificativaoperacao.JustificativaOperacaoNativeRepository;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.entities.contrato.JustificativaOperacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class JustificativaOperacaoNativeRepositoryImpl implements JustificativaOperacaoNativeRepository {

    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public List<JustificativaOperacao> contratosCanceladosTransferidosOutroAluno(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception {

        StringBuilder sb = new StringBuilder("select descricao from justificativaoperacao where tipooperacao = 'CA' and descricao like 'CT.%.CA.%'\n");

        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            sb.append("and UPPER(descricao) = UPPER(:quickSearchValue)\n");
        }

        if (!UteisValidacao.emptyNumber(filtroBIControleOperacoesJSON.getEmpresa())) {
            sb.append("and empresa = :codigoEmpresa\n");
        }

        String sortField = "descricao";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];

                if (sortField.equals("descricao")) {
                    sortField = "descricao";
                }
                sortOrder = paginadorDTO.getSort().split(",")[1];
            }

        }
        sb.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        Query query = entityManager.createNativeQuery(sb.toString(), Tuple.class);
        if (!UteisValidacao.emptyNumber(filtroBIControleOperacoesJSON.getEmpresa())) {
            query.setParameter("codigoEmpresa", filtroBIControleOperacoesJSON.getEmpresa());
        }
        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            query.setParameter("quickSearchValue", "%" + filtroBIControleOperacoesJSON.getQuickSearchValue() + "%");
        }
        int size = 3;
        int page = 1;

        if (paginadorDTO != null) {
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);
        List<Tuple> resultado = query.getResultList();
        List<JustificativaOperacao> lista = resultado.stream().map(
                tuple -> new JustificativaOperacao(tuple.get("descricao", String.class))
        ).collect(Collectors.toList());


        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sb + ") as sqlCount");
            if (!UteisValidacao.emptyNumber(filtroBIControleOperacoesJSON.getEmpresa())) {
                countQuery.setParameter("codigoEmpresa", filtroBIControleOperacoesJSON.getEmpresa());
            }
            if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
                countQuery.setParameter("quickSearchValue", "%" + filtroBIControleOperacoesJSON.getQuickSearchValue() + "%");
            }
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return lista;
    }
}
