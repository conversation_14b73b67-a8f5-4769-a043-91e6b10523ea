package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.AcessoCliente;
import com.pacto.config.dto.PaginadorDTO;

import java.time.LocalDate;
import java.util.List;

public interface AcessoClienteDao extends DaoGenerico<AcessoCliente, Integer> {

    List<AcessoCliente> listarRegistrDeAcessoCliente(Integer codigoLocalAcesso) throws Exception;

    List<AcessoCliente>listarRegistrDeAcessoClienteDia(Integer matricula, LocalDate data) throws Exception;

    List<AcessoCliente> findAllByPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception;
}
