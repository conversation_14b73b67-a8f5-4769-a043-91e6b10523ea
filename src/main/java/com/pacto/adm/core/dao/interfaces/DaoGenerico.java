package com.pacto.adm.core.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.hibernate.Session;
import org.hibernate.engine.spi.SessionImplementor;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface DaoGenerico<T, ID extends Serializable> {

    Session getCurrentSession();

    T findById(ID id) throws Exception;

    T findByName(String nome) throws Exception;

    T findByDescription(String nome) throws Exception;

    T findByType(String tipo) throws Exception;

    Optional<T> findByParam(String[] atributos, Object[] valores);

    List<T> findAll() throws Exception;

    List<T> findAll(Long limit, Long offset) throws Exception;

    List<T> findByParam(final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    List<T> findByParam(final StringBuilder whereClause, Map<String, Object> params, int max, int index) throws Exception;

    T save(T obj) throws Exception;

    T update(T obj) throws Exception;

    void delete(T obj) throws Exception;

    void delete(ID id) throws Exception;

    void deleteComParam(String[] atributos, Object[] valores) throws Exception;

    Number countWithParam(final String atributoCount, final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    SessionImplementor createSessionCurrentWork() throws Exception;

    ResultSet createStatement(final Connection connection, final String sql) throws Exception;

    void processarPaginador(StringBuilder sql, String orderByDefault, PaginadorDTO paginadorDTO) throws ServiceException;
}
