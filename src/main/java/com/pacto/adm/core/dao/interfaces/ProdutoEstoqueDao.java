package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.financeiro.ProdutoEstoque;

import java.util.Date;

public interface ProdutoEstoqueDao extends DaoGenerico<ProdutoEstoque, Integer> {

    Date pesquisarBalancoComDataMaior(Date dataComparar, Integer codigoProduto, Integer codigoEmpresa)throws Exception;
    Date pesquisarProdutoEstoqueComDataMaior(Date dataComparar, Integer codigoProduto, Integer codigoEmpresa)throws Exception;
}
