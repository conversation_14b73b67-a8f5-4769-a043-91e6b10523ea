package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.AfastamentoContratoDependenteDao;
import com.pacto.adm.core.entities.contrato.AfastamentoContratoDependente;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AfastamentoContratoDependenteDaoImpl extends DaoGenericoImpl<AfastamentoContratoDependente, Integer> implements AfastamentoContratoDependenteDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<AfastamentoContratoDependente> findAllByContratoDependente(Integer codContratoDependente, PaginadorDTO paginadorDTO) throws Exception {
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.contratoDependente.codigo = :codContratoDependente \n");
        params.put("codContratoDependente", codContratoDependente);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if(sortField.equalsIgnoreCase("inicioAfastamento")) {
                    where.append(" order by obj.inicioAfastamento " + sortOrder);
                } else if(sortField.equalsIgnoreCase("finalAfastamento")) {
                    where.append(" order by obj.finalAfastamento " + sortOrder);
                } else if(sortField.equalsIgnoreCase("nrDiasSomar")) {
                    where.append(" order by obj.nrDiasSomar " + sortOrder);
                } else if(sortField.equalsIgnoreCase("dataRegistro")) {
                    where.append(" order by obj.dataRegistro " + sortOrder);
                } else if(sortField.equalsIgnoreCase("titular")) {
                    where.append(" order by obj.titular " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc");
            }
        } else {
            where.append(" order by obj.codigo desc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
