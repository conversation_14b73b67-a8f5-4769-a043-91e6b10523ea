package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.filtros.FiltroTipoModalidadeJSON;

import com.pacto.adm.core.entities.contrato.TipoModalidade;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface TipoModalidadeDao extends DaoGenerico<TipoModalidade, Integer>{

    List<TipoModalidade> findAll(FiltroTipoModalidadeJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    TipoModalidade findByIdentificador(Integer identificador) throws Exception;
}
