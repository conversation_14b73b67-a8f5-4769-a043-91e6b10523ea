package com.pacto.adm.core.dao.interfaces.nativerepositories.planoconta;

import com.pacto.adm.core.dto.filtros.FiltroPlanoContaDespesasJSON;
import com.pacto.adm.core.entities.PlanoConta;
import com.pacto.config.dto.PaginadorDTO;

import java.util.Date;
import java.util.List;

public interface PlanoContaNativeRepository {
    List<PlanoConta> consultarTodosPlanoContaComValoresPorPeriodoEEmpresa(Integer codigoEmpresa, Date dataInicio, Date dataFim, PaginadorDTO paginadorDTO) throws Exception;

    List<PlanoConta> consultarTodosPlanoContaComValoresPorFiltro(FiltroPlanoContaDespesasJSON filtroPlanoContaDespesasJSON, PaginadorDTO paginadorDTO) throws Exception;
}
