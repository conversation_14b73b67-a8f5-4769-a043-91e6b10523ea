package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ClassificacaoDao;
import com.pacto.adm.core.entities.Classificacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ClassificacaoDaoImpl extends DaoGenericoImpl<Classificacao, Integer> implements ClassificacaoDao {


    @Override
    public Classificacao findById(Integer codigoClassificacao, Integer empresa) throws Exception{
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" SELECT col FROM classificacao col WHERE col.codigo = :codigoClassificacao ");
        params.put("codigoClassificacao", codigoClassificacao);
        Query query = getCurrentSession().createQuery(where.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().size() > 0) {
            return (Classificacao) query.getResultList().get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<Classificacao> findClassificaoAll() throws Exception {
        List<Classificacao> classificacoes = new ArrayList<>();
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                PreparedStatement pst;
                StringBuilder sql = new StringBuilder("SELECT * FROM classificacao");

                sql.append(" ORDER BY nome");
                try {
                    ResultSet rs = createStatement(connection, sql.toString());
                    while (rs.next()) {
                        Classificacao classificacao = new Classificacao();
                        classificacao.setCodigo(rs.getInt("codigo"));
                        classificacao.setNome(rs.getString("nome"));

                        classificacoes.add(classificacao);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return classificacoes;
    }
}
