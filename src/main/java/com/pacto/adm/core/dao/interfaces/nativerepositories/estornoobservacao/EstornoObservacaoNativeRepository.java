package com.pacto.adm.core.dao.interfaces.nativerepositories.estornoobservacao;

import com.pacto.adm.core.dto.estornoobservacao.EstornoObservacaoDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface EstornoObservacaoNativeRepository {
    List<EstornoObservacaoDTO> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO, boolean buscarComAdministrador, boolean buscarComRecorrencia) throws Exception;
}
