package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.solicitacaocompra.SolicitacaoCompraDao;
import com.pacto.adm.core.dto.filtros.FiltroSolicitacaoCompraJSON;
import com.pacto.adm.core.entities.solicitacaocompra.SolicitacaoCompra;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Uteis;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@AllArgsConstructor
public class SolicitacaoCompraDaoImpl extends DaoGenericoImpl<SolicitacaoCompra, Integer> implements SolicitacaoCompraDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<SolicitacaoCompra> findAll(FiltroSolicitacaoCompraJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" WHERE 1 = 1\n");

        if (Uteis.notNullAndNotEmpty(filtros.getParametro())) {
            where.append(" AND upper(obj.titulo) like CONCAT('%',:titulo,'%')\n");
            params.put("titulo", filtros.getParametro());
        }

        if (filtros.getSituacao() != null) {
            where.append(" AND upper(obj.situacao) = :situacao\n");
            params.put("situacao", filtros.getSituacao());
        }

        if (filtros.getDataSolicitacaoStart() != null && filtros.getDataSolicitacaoEnd() != null) {
            where.append(" AND obj.dataSolicitacao BETWEEN :dataSolicitacaoStart AND :dataSolicitacaoEnd\n");
            params.put("dataSolicitacaoStart", filtros.getDataSolicitacaoStart());
            params.put("dataSolicitacaoEnd", filtros.getDataSolicitacaoEnd());
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("distinct obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("titulo")) {
                    where.append(" order by obj.titulo " + sortOrder);
                } else if (sortField.equalsIgnoreCase("data_solicitacao")) {
                    where.append(" order by obj.data_solicitacao " + sortOrder);
                } else if (sortField.equalsIgnoreCase("situacao")) {
                    where.append(" order by obj.situacao " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc");
            }
        } else {
            where.append(" order by obj.codigo desc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
