package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.SorteioDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroSorteioJSON;
import com.pacto.adm.core.entities.sorteio.Sorteio;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class SorteioDaoImpl extends DaoGenericoImpl<Sorteio, Integer> implements SorteioDao {
    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<Sorteio> findAll(FiltroSorteioJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        if (Uteis.notNullAndNotEmpty(filtros.getParametro())) {
            where.append("where upper(obj.cliente.pessoa.nome) like concat('%',:nome,'%')\n");
            params.put("nome", filtros.getParametro());
            if (filtros.getParametro().matches("\\d+")) {
                where.append("or obj.codigo = :codigo\n");
                params.put("codigo", new Integer(filtros.getParametro()));
            }
        }
        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sort = paginadorDTO.getSort().split(",")[0].trim();
                String orderDirection = paginadorDTO.getSort().split(",")[1];

                if (sort.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + orderDirection);
                } else if (sort.equalsIgnoreCase("data")) {
                    where.append(" order by obj.dataSorteio " + orderDirection);
                } else if (sort.equalsIgnoreCase("observacoes")) {
                    where.append(" order by obj.observacoes " + orderDirection);
                } else if (sort.equalsIgnoreCase("nome")) {
                    where.append(" order by obj.cliente.pessoa.nome " + orderDirection);
                } else if (sort.equalsIgnoreCase("nomeUsuario")) {
                    where.append(" order by obj.usuario.nome " + orderDirection);
                }
            } else {
                where.append(" order by obj.codigo desc ");
            }
        } else {
            where.append(" order by obj.codigo desc ");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
