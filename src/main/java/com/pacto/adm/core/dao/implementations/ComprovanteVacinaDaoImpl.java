package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ComprovanteVacinaDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroComprovanteVacinaJSON;
import com.pacto.adm.core.entities.contrato.ComprovanteVacina;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ComprovanteVacinaDaoImpl extends DaoGenericoImpl<ComprovanteVacina, Integer> implements ComprovanteVacinaDao {

    private final Integer MAXIMO_RESULTADOS = 10;

    @Override
    public List<ComprovanteVacina> findByMatricula(Integer codMatricula, FiltroComprovanteVacinaJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" INNER JOIN Cliente cli ON cli.pessoa.codigo = obj.pessoa.codigo \n");
        where.append(" WHERE cli.codigoMatricula = :codMatricula\n");
        params.put("codMatricula", codMatricula);

        if (filtros.getParametro() != null && filtros.getParametro().matches("\\d+")) {
            where.append(" AND obj.codigo = :codigo\n");
            params.put("codigo", new Integer(filtros.getParametro()));
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                }
            }
        } else {
            where.append(" order by obj.codigo desc ");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }

}
