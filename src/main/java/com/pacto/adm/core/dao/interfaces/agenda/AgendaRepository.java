package com.pacto.adm.core.dao.interfaces.agenda;

import com.pacto.adm.core.entities.agenda.Agenda;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Repository
public interface AgendaRepository extends JpaRepository<Agenda, Integer> {

    @Modifying
    @Transactional(transactionManager = "zwClientTransactionManager")
    @Query("UPDATE Agenda a SET a.colaboradorResponsavel.codigo = :codigoColaboradorResponsavelNovo\n" +
            "WHERE a.colaboradorResponsavel.codigo = :codigoColaboradorResponsavelAntigo\n" +
            "AND CAST(CONCAT(CAST(a.dataAgendamento AS date), ' ', a.hora, ':', a.minuto) AS timestamp) >= CAST(:data AS date)\n" +
            "AND a.cliente.codigo = :codigoCliente")
    void alterarConsultorResponsavelAgenda(
            @Param("codigoColaboradorResponsavelAntigo") Integer codigoColaboradorResponsavelAntigo,
            @Param("codigoColaboradorResponsavelNovo") Integer codigoColaboradorResponsavelNovo,
            @Param("codigoCliente") Integer codigoCliente,
            @Param("data") Date data
    );

    @Query("SELECT CASE WHEN count(a.codigo) > 0 THEN true ELSE false END\n" +
            "FROM Agenda a\n" +
            "INNER JOIN a.colaboradorResponsavel u\n" +
            "WHERE u.colaborador.codigo = :codigoColaborador\n" +
            "AND CAST(CONCAT(CAST(a.dataAgendamento AS date), ' ', a.hora, ':', a.minuto) AS timestamp) >= CAST(:dataAgendamento AS date)\n" +
            "AND a.cliente.codigo = :codigoCliente")
    boolean clientePossuiAgendamentosColaborador(
            @Param("codigoColaborador") Integer codigoColaborador,
            @Param("codigoCliente") Integer codigoCliente,
            @Param("dataAgendamento") Date dataAgendamento
    );
}
