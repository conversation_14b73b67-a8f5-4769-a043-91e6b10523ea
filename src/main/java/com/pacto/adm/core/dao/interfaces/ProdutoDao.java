package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.Produto;

import java.util.List;

public interface ProdutoDao extends DaoGenerico<Produto, Integer> {

    List<Produto> findVendaByDescricao(String descricao, List<Integer> produtosSugeridos, Integer empresaId) throws Exception;

    List<Produto> findAllByTipoProduto(String tipoProduto) throws Exception;

    List<Produto> findAllByTipoProdutoAtivo(String tipoProduto, Boolean ativo) throws Exception;

    List<Produto> ultimosVendidos(Integer empresa, String jaAdicionados, Integer limit) throws Exception;

    List<Produto> findProdutosByListTipo(List<String> listaTipoProduto, Integer empresa, Boolean somenteComEstoque) throws Exception;
}
