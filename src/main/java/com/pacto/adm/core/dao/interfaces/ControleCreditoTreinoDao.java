package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.filtros.FiltroControleCreditoTreinoJSON;
import com.pacto.adm.core.entities.contrato.ControleCreditoTreino;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface ControleCreditoTreinoDao extends DaoGenerico<ControleCreditoTreino, Integer> {
    List<ControleCreditoTreino> findByCodigoContrato(Integer codigoContrato, PaginadorDTO paginadorDTO, FiltroControleCreditoTreinoJSON filtros) throws Exception;
}
