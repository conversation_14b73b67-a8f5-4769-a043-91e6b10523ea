package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.AluguelArmarioDao;
import com.pacto.adm.core.entities.AluguelArmario;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AluguelArmarioDaoImpl extends DaoGenericoImpl<AluguelArmario, Integer> implements AluguelArmarioDao {
    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<AluguelArmario> findAllByPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.cliente.pessoa.codigo = :codPessoa\n");
        params.put("codPessoa", codPessoa);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if(sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if(sortField.equalsIgnoreCase("dataCadastro")) {
                    where.append(" order by obj.dataCadastro " + sortOrder);
                }
            } else {
                where.append(" order by obj.dataCadastro desc");
            }
        } else {
            where.append(" order by obj.datacadastro desc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }

    @Override
    public Integer totalByPessoa(Integer codPessoa) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.cliente.pessoa.codigo = :codPessoa\n");
        params.put("codPessoa", codPessoa);

        return countWithParam("obj.codigo", where, params).intValue();
    }
}
