package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoJoinDao;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoJoin;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ConfiguracaoIntegracaoJoinDaoImpl extends DaoGenericoImpl<ConfiguracaoIntegracaoJoin, Integer> implements ConfiguracaoIntegracaoJoinDao {
    @Override
    public ConfiguracaoIntegracaoJoin findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoIntegracaoJoin obj ");
        s.append(" WHERE obj.empresa.codigo = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ConfiguracaoIntegracaoJoin config = new ConfiguracaoIntegracaoJoin();
        try {
            config = (ConfiguracaoIntegracaoJoin) query.getResultList().get(0);
        } catch (Exception e) {
        }
        return config;
    }
}
