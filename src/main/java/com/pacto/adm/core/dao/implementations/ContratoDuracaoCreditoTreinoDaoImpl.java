package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ContratoDuracaoCreditoTreinoDao;
import com.pacto.adm.core.entities.contrato.ContratoDuracaoCreditoTreino;
import com.pacto.config.utils.Uteis;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import java.util.Date;


@Repository
public class ContratoDuracaoCreditoTreinoDaoImpl extends DaoGenericoImpl<ContratoDuracaoCreditoTreino, Integer> implements ContratoDuracaoCreditoTreinoDao {
    @Override
    public void alterarDataUltimoCreditoMensal(ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreinoVO, Date data) throws Exception {
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                try {
                    String sql = "update ContratoDuracaoCreditoTreino set dataUltimoCreditoMensal = '" + Uteis.getDataJDBCTimestamp(data) + "' where codigo = " + contratoDuracaoCreditoTreinoVO.getCodigo();
                    connection.createStatement().executeUpdate(sql);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
        }
    }
}
