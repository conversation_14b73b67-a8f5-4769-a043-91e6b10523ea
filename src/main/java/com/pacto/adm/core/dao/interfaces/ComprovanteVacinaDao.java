package com.pacto.adm.core.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroComprovanteVacinaJSON;
import com.pacto.adm.core.entities.contrato.ComprovanteVacina;

import java.util.List;

public interface ComprovanteVacinaDao extends DaoGenerico<ComprovanteVacina, Integer> {

    List<ComprovanteVacina> findByMatricula(Integer codMatricula, FiltroComprovanteVacinaJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

}
