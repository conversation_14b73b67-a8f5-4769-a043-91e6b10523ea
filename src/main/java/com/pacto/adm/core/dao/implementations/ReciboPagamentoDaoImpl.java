package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ReciboPagamentoDao;
import com.pacto.adm.core.entities.ReciboPagamento;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ReciboPagamentoDaoImpl extends DaoGenericoImpl<ReciboPagamento, Integer> implements ReciboPagamentoDao {

    @Override
    public List<ReciboPagamento> findAllByPessoa(Integer codPessoa) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.pessoaPagador.codigo = :codPessoa\n");
        params.put("codPessoa", codPessoa);

        return findByParam(where, params);
    }

}
