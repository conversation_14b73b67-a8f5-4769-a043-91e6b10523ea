package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.MovProdutoParcela;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;
import java.util.Set;

public interface MovProdutoParcelaDao extends DaoGenerico<MovProdutoParcela, Integer> {

    List<MovProdutoParcela> findByMovProduto(Integer codigoMovProduto);
    List<MovProdutoParcela> findAllByRecibo(Integer codRecibo) throws Exception;

    List<MovProdutoParcela> findAllByParcela(Integer codParcela) throws Exception;

    void alterarSomenteReciboPagamento(MovProdutoParcela movProdutoParcela) throws Exception;

    List<MovProdutoParcela> consultarPorCodigoMovProdutos(Integer codProduto) throws Exception;

    List<MovProdutoParcela> findByMovProduto(Integer codMovProduto, PaginadorDTO paginadorDTO) throws Exception;
}
