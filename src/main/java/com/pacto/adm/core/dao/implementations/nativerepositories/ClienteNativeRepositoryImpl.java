package com.pacto.adm.core.dao.implementations.nativerepositories;

import com.pacto.adm.core.dao.interfaces.nativerepositories.cliente.ClienteNativeRepository;
import com.pacto.adm.core.dto.ClienteComFreepassDTO;
import com.pacto.adm.core.dto.ClienteComGympassDTO;
import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.ClienteInativoPeriodoAcessoDTO;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.cliente.ClienteAniversarianteDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.filtros.bi.FiltroClienteAniversarianteJSON;
import com.pacto.adm.core.util.QueryUtils;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Repository criado para queries nativas que não dá para usar o repository padrão do JPA. Como queries que dependem
 * de muitas verificações para serem montadas.
 */
@Repository
public class ClienteNativeRepositoryImpl implements ClienteNativeRepository {

    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public List<ClienteInativoPeriodoAcessoDTO> consultarClientesInativosComPeriodoAcesso(
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO
    ) throws Exception {

        if (filtroBIControleOperacoesJSON.getFim() == null) {
            throw new Exception("Deve ser informado o filtro data fim para consulta!");
        }

        List<ClienteInativoPeriodoAcessoDTO> lista = new ArrayList<>();

        String sql = sqlConsultarClientesInativosComPeriodoAcesso(filtroBIControleOperacoesJSON, paginadorDTO).toString();

        Query query = entityManager.createNativeQuery(sql, Tuple.class);

        int size = 3;
        int page = 1;

        if (paginadorDTO != null) {
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);

        setParametersCliInComPeriodoAcesso(query, filtroBIControleOperacoesJSON);

        List<Tuple> resultado = query.getResultList();

        lista = resultado.stream().map(tuple -> new ClienteInativoPeriodoAcessoDTO(
                tuple.get("codigoCliente", Integer.class),
                tuple.get("situacaoCliente", String.class),
                tuple.get("codigoMatriculaCliente", Integer.class),
                tuple.get("matriculaCliente", String.class),
                new PessoaDTO(
                        tuple.get("codigoPessoa", Integer.class),
                        tuple.get("nomePessoa", String.class)
                ),
                tuple.get("situacaoContrato", String.class)
        )).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParametersCliInComPeriodoAcesso(countQuery, filtroBIControleOperacoesJSON);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return lista;
    }

    private StringBuilder sqlConsultarClientesInativosComPeriodoAcesso(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select distinct(cliente.codigo),\n")
                .append(" cliente.codigo as codigoCliente,\n")
                .append(" cliente.matricula as matriculaCliente,\n")
                .append(" cliente.codigoMatricula as codigoMatriculaCliente,\n")
                .append(" cliente.situacao as situacaoCliente,\n")
                .append(" pessoa.codigo as codigoPessoa,\n")
                .append(" pessoa.nome as nomePessoa,\n")
                .append(" contrato.codigo as contratoCodigo,\n")
                .append(" sw.situacaocontrato as situacaoContrato\n")
                .append(" from historicocontrato as ht\n")
                .append(" inner join contrato on contrato.codigo = ht.contrato\n")
                .append(" inner join pessoa on pessoa.codigo = contrato.pessoa\n")
                .append(" inner join cliente on cliente.pessoa = pessoa.codigo\n")
                .append(" inner join periodoacessocliente as per on per.contrato = ht.contrato\n")
                .append(" left join situacaoclientesinteticodw sw on sw.codigopessoa = pessoa.codigo\n");

        StringBuilder sbWhere = new StringBuilder(" where (ht.tipohistorico = 'VE' or ht.tipohistorico = 'DE' or ht.tipohistorico = 'CA')\n")
                .append(" and ht.datainiciosituacao <= :datafim\n")
                .append(" and (per.datainicioacesso <= :datafim\n")
                .append(" and per.datafinalacesso >= :datafim)\n")
                .append(" and contrato.empresa = :codigoEmpresa\n");

        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            sbWhere.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sbWhere, new LinkedHashMap<String, Class<?>>() {{
                put("pessoa.nome", null);
                put("cliente.matricula", null);
                put("cliente.situacao", null);
                put("sw.situacaocontrato", null);
            }});
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            sqlStr.append(" INNER JOIN vinculo ON vinculo.cliente = cliente.codigo\n");
            sbWhere.append(" AND vinculo.colaborador IN :colaboradores\n");
        }

        sqlStr.append(sbWhere);

        String sortField = "pessoa.nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("pessoa")) {
                    sortField = "pessoa.nome";
                }
                if (sortField.equals("situacao")) {
                    sortField = "cliente.situacao " + sortOrder + ", sw.situacaoContrato";
                }
                if (sortField.equals("matricula")) {
                    sortField = "cliente.matricula";
                }
            }

        }
        sqlStr.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sqlStr;
    }

    private void setParametersCliInComPeriodoAcesso(Query query, FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON) {
        query.setParameter("datafim", Uteis.getDataJDBC(filtroBIControleOperacoesJSON.getFim()));
        query.setParameter("codigoEmpresa", filtroBIControleOperacoesJSON.getEmpresa());
        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIControleOperacoesJSON.getQuickSearchValue(), query);
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            query.setParameter("colaboradores", filtroBIControleOperacoesJSON.getColaboradores());
        }
    }

    @Override
    public List<ClienteComFreepassDTO> findClientesComFreepass(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception {

        if (filtroBIControleOperacoesJSON.getFim() == null) {
            throw new Exception("Deve ser informado o filtro data fim para consulta!");
        }

        StringBuilder sb = sqlFindClientesComFreepass(filtroBIControleOperacoesJSON, paginadorDTO);

        Query query = entityManager.createNativeQuery(sb.toString(), Tuple.class);
        setParametersClientesComFreepass(query, filtroBIControleOperacoesJSON);

        int size = 3;
        int page = 1;

        if (paginadorDTO != null) {
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);

        List<Tuple> resultado = query.getResultList();

        List<ClienteComFreepassDTO> lista = resultado.stream().map(
                tuple -> new ClienteComFreepassDTO(
                        tuple.get("dataLancamento", Date.class),
                        tuple.get("qtdDiasFreepass", Integer.class),
                        new ClienteDTO(
                                tuple.get("matriculaCliente", String.class),
                                tuple.get("codigoMatriculaCliente", Integer.class),
                                tuple.get("situacaoCliente", String.class),
                                new PessoaDTO(
                                        tuple.get("nomeCliente", String.class)
                                )
                        ),
                        new UsuarioDTO(
                                tuple.get("responsavel", String.class)
                        ),
                        new ContratoDTO(
                                tuple.get("situacaoContrato", String.class)
                        )
                )
        ).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sb + ") as sqlCount");
            setParametersClientesComFreepass(countQuery, filtroBIControleOperacoesJSON);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return lista;
    }

    private StringBuilder sqlFindClientesComFreepass(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) {
        StringBuilder sb = new StringBuilder(
                "SELECT cli.codigomatricula as codigoMatriculaCliente,\n" +
                        "cli.matricula as matriculaCliente,\n" +
                        "cli.situacao as situacaoCliente,\n" +
                        "sw.situacaocontrato as situacaoContrato,\n" +
                        "coalesce(pa.dataLancamento, pa.datainicioacesso) as dataLancamento,\n" +
                        "pes.nome as nomeCliente,\n" +
                        "us.nome as responsavel,\n" +
                        "(extract(day\n" +
                        "   from pa.datafinalacesso - pa.datainicioacesso) + 1)\\:\\:integer as qtdDiasFreepass\n" +
                        "FROM cliente cli\n" +
                        "INNER JOIN periodoacessocliente pa ON pa.pessoa = cli.pessoa\n" +
                        "INNER JOIN pessoa pes ON pes.codigo = cli.pessoa\n" +
                        "LEFT JOIN usuario us ON pa.responsavel = us.codigo\n" +
                        "LEFT JOIN situacaoclientesinteticodw sw ON sw.codigopessoa = pes.codigo\n" +
                        "WHERE coalesce(pa.tokengympass, '') = ''\n" +
                        "AND pa.tipoacesso = 'PL'\n" +
                        "AND :dataFim BETWEEN pa.datainicioacesso AND pa.datafinalacesso\n"
        );

        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            sb.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sb, new LinkedHashMap<String, Class<?>>() {{
                put("pes.nome", null);
                put("cli.matricula", null);
                put("cli.situacao", null);
                put("sw.situacaocontrato", null);
                put("us.nome", null);
                put("(extract(day from pa.datafinalacesso - pa.datainicioacesso) + 1)", Integer.class);
                put("coalesce(pa.dataLancamento, pa.datainicioacesso)", Date.class);
            }});
        }

        if (!UteisValidacao.emptyNumber(filtroBIControleOperacoesJSON.getEmpresa())) {
            sb.append("AND cli.empresa = :codigoEmpresa\n");
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            sb.append("AND us.colaborador IN :colaboradores\n");
        }

        String sortField = "pes.nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("pessoa")) {
                    sortField = "pes.nome";
                }
                if (sortField.equals("matricula")) {
                    sortField = "cli.matricula";
                }
                if (sortField.equals("responsavel")) {
                    sortField = "us.nome";
                }
                if (sortField.equals("dataLancamento")) {
                    sortField = "dataLancamento";
                }
                if (sortField.equals("qtdDiasFreepass")) {
                    sortField = "qtdDiasFreepass";
                }
                if (sortField.equals("situacao")) {
                    sortField = "cli.situacao " + sortOrder + ", sw.situacaocontrato";
                }
            }

        }
        sb.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sb;
    }

    private void setParametersClientesComFreepass(Query query, FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON) throws ServiceException {
        query.setParameter("dataFim", Uteis.getDataJDBC(filtroBIControleOperacoesJSON.getFim()));
        if (!UteisValidacao.emptyNumber(filtroBIControleOperacoesJSON.getEmpresa())) {
            query.setParameter("codigoEmpresa", filtroBIControleOperacoesJSON.getEmpresa());
        }
        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIControleOperacoesJSON.getQuickSearchValue(), query);
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            query.setParameter("colaboradores", filtroBIControleOperacoesJSON.getColaboradores());
        }
    }

    @Override
    public List<ClienteComGympassDTO> findClientesComGympass(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception {

        if (filtroBIControleOperacoesJSON.getFim() == null) {
            throw new Exception("Deve ser informado o filtro data fim para consulta!");
        }

        StringBuilder sb = sqlFindClientesComGympass(filtroBIControleOperacoesJSON, paginadorDTO);

        Query query = entityManager.createNativeQuery(sb.toString(), Tuple.class);
        setParametersClientesComGympass(query, filtroBIControleOperacoesJSON);

        int size = 3;
        int page = 1;

        if (paginadorDTO != null) {
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);

        List<Tuple> resultado = query.getResultList();

        List<ClienteComGympassDTO> lista = resultado.stream().map(
                tuple -> new ClienteComGympassDTO(
                        tuple.get("dataInicio", Date.class),
                        tuple.get("qtdDiasGympass", Integer.class),
                        tuple.get("tokenGympass", String.class),
                        new ClienteDTO(
                                tuple.get("matriculaCliente", String.class),
                                tuple.get("codigoMatriculaCliente", Integer.class),
                                tuple.get("situacaoCliente", String.class),
                                new PessoaDTO(
                                        tuple.get("nomeCliente", String.class)
                                )
                        ),
                        new UsuarioDTO(
                                tuple.get("responsavel", String.class)
                        ),
                        new ContratoDTO(
                                tuple.get("situacaoContrato", String.class)
                        )
                )
        ).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sb + ") as sqlCount");
            setParametersClientesComGympass(countQuery, filtroBIControleOperacoesJSON);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return lista;
    }

    private StringBuilder sqlFindClientesComGympass(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) {
        StringBuilder sb = new StringBuilder(
                "SELECT cli.codigomatricula as codigoMatriculaCliente,\n" +
                        "cli.matricula as matriculaCliente,\n" +
                        "cli.situacao as situacaoCliente,\n" +
                        "sw.situacaocontrato as situacaoContrato,\n" +
                        "coalesce(pa.dataLancamento, pa.datainicioacesso) as dataInicio,\n" +
                        "pes.nome as nomeCliente,\n" +
                        "us.nome as responsavel,\n" +
                        "pa.tokengympass as tokenGympass,\n" +
                        "(extract(day\n" +
                        "   from pa.datafinalacesso - pa.datainicioacesso) + 1)\\:\\:integer as qtdDiasGympass\n" +
                        "FROM cliente cli\n" +
                        "INNER JOIN periodoacessocliente pa ON pa.pessoa = cli.pessoa\n" +
                        "INNER JOIN pessoa pes ON pes.codigo = cli.pessoa\n" +
                        "LEFT JOIN usuario us ON pa.responsavel = us.codigo\n" +
                        "LEFT JOIN situacaoclientesinteticodw sw ON sw.codigopessoa = pes.codigo\n" +
                        "WHERE coalesce(pa.tokengympass, '') <> ''\n" +
                        "AND pa.tipoacesso = 'PL'\n" +
                        "AND :dataFim BETWEEN pa.datainicioacesso AND pa.datafinalacesso\n"
        );

        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            sb.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sb, new LinkedHashMap<String, Class<?>>() {{
                put("pes.nome", null);
                put("cli.matricula", null);
                put("cli.situacao", null);
                put("sw.situacaocontrato", null);
                put("us.nome", null);
                put("pa.tokengympass", null);
                put("coalesce(pa.dataLancamento, pa.datainicioacesso)", Date.class);
            }});
        }

        if (!UteisValidacao.emptyNumber(filtroBIControleOperacoesJSON.getEmpresa())) {
            sb.append("AND cli.empresa = :codigoEmpresa\n");
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            sb.append("AND us.colaborador IN :colaboradores\n");
        }

        String sortField = "pes.nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("pessoa")) {
                    sortField = "pes.nome";
                }
                if (sortField.equals("matricula")) {
                    sortField = "cli.matricula";
                }
                if (sortField.equals("responsavel")) {
                    sortField = "us.nome";
                }
                if (sortField.equals("dataInicio")) {
                    sortField = "dataInicio";
                }
                if (sortField.equals("qtdDiasGympass")) {
                    sortField = "qtdDiasGympass";
                }
                if (sortField.equals("tokenGympass")) {
                    sortField = "tokenGympass";
                }
                if (sortField.equals("situacao")) {
                    sortField = "cli.situacao " + sortOrder + ", sw.situacaocontrato";
                }
            }

        }
        sb.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sb;
    }

    private void setParametersClientesComGympass(Query query, FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON) throws ServiceException {
        query.setParameter("dataFim", Uteis.getDataJDBC(filtroBIControleOperacoesJSON.getFim()));
        if (!UteisValidacao.emptyNumber(filtroBIControleOperacoesJSON.getEmpresa())) {
            query.setParameter("codigoEmpresa", filtroBIControleOperacoesJSON.getEmpresa());
        }
        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIControleOperacoesJSON.getQuickSearchValue(), query);
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            query.setParameter("colaboradores", filtroBIControleOperacoesJSON.getColaboradores());
        }
    }

    @Override
    public List<ClienteAniversarianteDTO> consultaAniversariantes(FiltroClienteAniversarianteJSON filtroClienteAniversarianteJSON, PaginadorDTO paginadorDTO) throws Exception {

        if (filtroClienteAniversarianteJSON.getInicio() == null) {
            throw new Exception("Deve ser informado o filtro da data inicial!");
        }

        if (filtroClienteAniversarianteJSON.getFim() == null) {
            throw new Exception("Deve ser informado o filtro da data final!");
        }

        String sql = sqlAniversariantes(filtroClienteAniversarianteJSON, paginadorDTO);

        Query query = entityManager.createNativeQuery(sql, Tuple.class);
        setParametersAniversariantes(query, filtroClienteAniversarianteJSON);
        QueryUtils.setQueryPagination(query, paginadorDTO);

        List<Tuple> resultado = query.getResultList();

        List<ClienteAniversarianteDTO> aniversariantes = resultado.stream().map(
                tuple -> new ClienteAniversarianteDTO(
                        new ClienteDTO(
                                tuple.get("matriculaCli", Integer.class),
                                tuple.get("situacaoCliente", String.class),
                                new PessoaDTO(
                                        tuple.get("nome", String.class),
                                        tuple.get("cpf", String.class),
                                        tuple.get("datanascimento", Date.class)
                                ),
                                tuple.get("telefonescliente", String.class)
                        ),
                        tuple.get("nomePlano", String.class),
                        tuple.get("duracaoContrato", Integer.class)
                )
        ).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParametersAniversariantes(countQuery, filtroClienteAniversarianteJSON);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return aniversariantes;
    }

    private String sqlAniversariantes(FiltroClienteAniversarianteJSON filtroClienteAniversarianteJSON, PaginadorDTO paginadorDTO) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT dw.codigocliente as cli , dw.codigocontrato as codContrato,\n");
        sql.append("dw.nomecliente as nome ,dw.nomePlano ,dw.codigopessoa as codPessoa, dw.matricula as matriculacli,dw.situacao as situacaoCliente,\n");
        sql.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf,dw.datanascimento\n");
        sql.append("FROM cliente cli\n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cli.pessoa\n");
        sql.append("INNER JOIN situacaoclientesinteticodw dw ON dw.codigocliente = cli.codigo\n");
        if (!CollectionUtils.isEmpty(filtroClienteAniversarianteJSON.getColaboradores())) {
            sql.append("INNER JOIN vinculo v on v.cliente = dw.codigocliente\n");
        }
        sql.append("LEFT JOIN contrato ct ON  ct.codigo = dw.codigocontrato\n");
        sql.append("LEFT JOIN contratoduracao cd ON cd.contrato = ct.codigo\n");
        sql.append("WHERE DATE_PART('MONTH', p.datanasc) = :mes\n");
        sql.append("AND (DATE_PART('DAY', p.datanasc) = :dia OR DATE_PART('DAY',dw.datanascimento) = :dia)\n");

        if (!UteisValidacao.emptyNumber(filtroClienteAniversarianteJSON.getEmpresa())) {
            sql.append("AND dw.empresacliente = :codigoEmpresa\n");
        } else {
            sql.append("AND dw.empresacliente IS NOT NULL\n");
        }

        if (!CollectionUtils.isEmpty(filtroClienteAniversarianteJSON.getColaboradores())) {
            sql.append("AND v.colaborador IN :colaboradores\n");
        }

        if (StringUtils.hasText(filtroClienteAniversarianteJSON.getQuickSearchValue())) {
            sql.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sql, new LinkedHashMap<String, Class<?>>() {{
                put("dw.nomecliente", null);
                put("dw.matricula", Integer.class);
                put("dw.situacao", null);
                put("dw.cpf", null);
                put("dw.datanascimento", Date.class);
                put("dw.nomePlano", null);
                put("cd.numeroMeses", Integer.class);
                put("dw.telefonescliente", null);
            }});
        }
        sql.append("GROUP BY cli, matriculaCli, dw.telefonescliente, codPessoa, dw.nomePlano, codContrato, situacaoCliente, nome, dataInicio ,dataFim, duracaoContrato, ct.nomemodalidades, dw.cpf, dw.nomecliente, dw.datanascimento\n");

        String sortField = "p.nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("pessoa")) {
                    sortField = "dw.nomecliente";
                }

                if (sortField.equals("matricula")) {
                    sortField = "dw.matricula";
                }

                if (sortField.equals("dataNascimento")) {
                    sortField = "dw.datanascimento";
                }

                if (sortField.equals("situacaoCliente")) {
                    sortField = "dw.situacao";
                }

                if (sortField.equals("cpf")) {
                    sortField = "dw.cpf";
                }

                if (sortField.equals("nomePlano")) {
                    sortField = "dw.nomePlano";
                }

                if (sortField.equals("duracaoContrato")) {
                    sortField = "cd.numeroMeses";
                }

                if (sortField.equals("telefonesCliente")) {
                    sortField = "dw.telefonescliente";
                }
            }

        }
        sql.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sql.toString();
    }

    private void setParametersAniversariantes(Query query, FiltroClienteAniversarianteJSON filtroClienteAniversarianteJSON) {
        int dia = Uteis.getDiaMesData(filtroClienteAniversarianteJSON.getFim());
        int mes = Uteis.getMesData(filtroClienteAniversarianteJSON.getFim());

        query.setParameter("dia", dia);
        query.setParameter("mes", mes);
        if (!UteisValidacao.emptyNumber(filtroClienteAniversarianteJSON.getEmpresa())) {
            query.setParameter("codigoEmpresa", filtroClienteAniversarianteJSON.getEmpresa());
        }

        if (StringUtils.hasText(filtroClienteAniversarianteJSON.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroClienteAniversarianteJSON.getQuickSearchValue(), query);
        }
    }

}
