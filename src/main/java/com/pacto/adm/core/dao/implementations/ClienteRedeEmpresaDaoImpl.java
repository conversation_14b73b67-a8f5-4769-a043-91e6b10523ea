package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ClienteRedeEmpresaDao;
import com.pacto.adm.core.entities.ClienteRedeEmpresa;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ClienteRedeEmpresaDaoImpl extends DaoGenericoImpl<ClienteRedeEmpresa, Integer> implements ClienteRedeEmpresaDao {

    @Override
    public List<ClienteRedeEmpresa> findByCpf(String cpf) throws Exception {
        if(UteisValidacao.emptyString(cpf)){
            return new ArrayList<>();
        }
        Map<String, Object> params = new HashMap<>();
        StringBuilder where = new StringBuilder();
        where.append(" WHERE obj.cpf = :cpf ");
        params.put("cpf", Uteis.removerMascara(cpf));
        return findByParam(where, params);
    }

    @Override
    public ClienteRedeEmpresa findByFilters(String cpf, String chaveEmpresa, Integer codigoEmpresa, Integer codigoMatricula) throws Exception {
        Map<String, Object> params = new HashMap<>();
        StringBuilder hql = new StringBuilder();

        hql.append("SELECT obj FROM ClienteRedeEmpresa obj ");
        hql.append(" WHERE obj.cpf = :cpf ");
        params.put("cpf", Uteis.removerMascara(cpf));
        if (!UteisValidacao.emptyString(chaveEmpresa)) {
            hql.append(" AND obj.chaveEmpresa = :chaveEmpresa ");
            params.put("chaveEmpresa", chaveEmpresa);
        }
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            hql.append(" AND obj.codigoEmpresa = :codigoEmpresa ");
            params.put("codigoEmpresa", codigoEmpresa);
        }
        if (!UteisValidacao.emptyNumber(codigoMatricula)) {
            hql.append(" AND obj.codigoMatricula = :codigoMatricula ");
            params.put("codigoMatricula", codigoMatricula);
        }

        Query query =  getCurrentSession().createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().isEmpty()) {
            return null;
        } else {
            return (ClienteRedeEmpresa) query.getResultList().get(0);
        }
    }
}
