package com.pacto.adm.core.dao.interfaces.nativerepositories.contrato;

import com.pacto.adm.core.dto.ContratoBolsaDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroContratoAutorizacaoJSON;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface ContratoNativeRepository {

    List<Contrato> contratosComDataBaseAlterada(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception;

    List<ContratoBolsaDTO> findContratosBolsa(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception;

    List<Contrato> findAllContratosAutorizacao(FiltroContratoAutorizacaoJSON filtroContratoAutorizacaoJSON, PaginadorDTO paginadorDTO) throws Exception;
}
