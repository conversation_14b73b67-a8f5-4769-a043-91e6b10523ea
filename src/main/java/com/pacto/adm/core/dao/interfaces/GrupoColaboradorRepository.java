package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.GrupoColaborador;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GrupoColaboradorRepository extends JpaRepository<GrupoColaborador, Integer> {

    @Query("SELECT g FROM GrupoColaborador g" +
            " WHERE (:empresa = 0 OR :empresa IS NULL OR g.empresa.codigo = :empresa)")
    List<GrupoColaborador> findByEmpresaCodigo(@Param("empresa") Integer empresa);

    @Query("SELECT gc FROM GrupoColaborador gc\n" +
            "INNER JOIN gc.grupoColaboradorParticipantes gcp\n" +
            "INNER JOIN gcp.colaboradorParticipante cp\n" +
            "WHERE gcp.tipoVisao = UPPER(:tipoVisao)\n" +
            "AND cp.codigo = :colaborador\n" +
            "AND (:empresa IS NULL OR :empresa = 0 OR gc.empresa.codigo = :empresa)")
    List<GrupoColaborador> findByColaboradorAndTipoVisao(
            @Param("colaborador") Integer colaborador,
            @Param("tipoVisao") String tipoVisao,
            @Param("empresa") Integer empresa);

    @Query("SELECT gc FROM GrupoColaborador gc\n" +
            "INNER JOIN gc.gerente g\n" +
            "WHERE g.nome LIKE CONCAT(:nomeGerente, '%')\n" +
            "AND (:empresa IS NULL OR :empresa = 0 OR gc.empresa.codigo = :empresa)\n" +
            "ORDER BY g.codigo")
    List<GrupoColaborador> findByGerenteNome(@Param("nomeGerente") String nomeGerente,
                                             @Param("empresa") Integer empresa);

}
