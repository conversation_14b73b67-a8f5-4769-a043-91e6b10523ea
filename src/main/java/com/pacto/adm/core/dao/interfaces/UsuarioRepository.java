package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.Usuario;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.List;

@Repository
public interface UsuarioRepository extends JpaRepository<Usuario, Integer> {

    @Query("SELECT u FROM Usuario u WHERE u.codigo = :codigo AND (u.senha = :senha OR u.pin = :senha)")
    Optional<Usuario> verificarLoginUsuario(
            @Param("codigo") Integer codigo,
            @Param("senha") String senha
    );

    @Query("SELECT u FROM Usuario u\n" +
            "INNER JOIN UsuarioPerfilAcesso upa ON upa.usuario.codigo = u.codigo\n" +
            "WHERE upa.empresa = :empresa\n" +
            "AND u.colaborador IN (SELECT c.codigo FROM Colaborador c WHERE c.pessoa.codigo IN (SELECT c2.pessoa FROM Colaborador c2 WHERE c2.codigo in (:codigosColaboradores)))")
    List<Usuario> consultarPorColaboresEmpresa(@Param("codigosColaboradores") List<Integer> codigosColaboradores, @Param("empresa") Integer empresa);

    @Transactional(transactionManager = "zwClientTransactionManager")
    Usuario findByColaboradorCodigo(Integer codigo);
}
