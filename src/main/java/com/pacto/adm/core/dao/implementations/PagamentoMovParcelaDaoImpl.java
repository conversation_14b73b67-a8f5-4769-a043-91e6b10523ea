package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.PagamentoMovParcelaDao;
import com.pacto.adm.core.entities.financeiro.PagamentoMovParcela;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class PagamentoMovParcelaDaoImpl extends DaoGenericoImpl<PagamentoMovParcela, Integer> implements PagamentoMovParcelaDao {


    @Override
    public List<PagamentoMovParcela> findByMovProduto(Integer codigoMovProduto) {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append("SELECT pmp FROM PagamentoMovParcela pmp ");
        hql.append(" WHERE pmp.parcela.movProdutosParcelas.movProduto.codigo = :codigoMovProduto");
        params.put("codigoMovProduto", codigoMovProduto);
        hql.append(" ORDER BY pmp.recibo.data DESC");

        Query query = getCurrentSession().createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        return query.getResultList();
    }
}
