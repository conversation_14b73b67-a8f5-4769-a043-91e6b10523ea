package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.IndicacaoDao;
import com.pacto.adm.core.entities.indicacao.Indicacao;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class IndicacaoDaoImpl extends DaoGenericoImpl<Indicacao, Integer> implements IndicacaoDao {

    private static final int MAXIMO_RESULTADOS = 50;

    @Override
    public List<Indicacao> findByMatricula(Integer matricula, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" WHERE obj.clienteQueIndicou.codigoMatricula = :matricula");
        params.put("matricula", matricula);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if (sortField.equalsIgnoreCase("dataInicio")) {
                    where.append(" order by obj.dataInicioAcesso " + sortOrder);
                } else if (sortField.equalsIgnoreCase("tokenGympass")) {
                    where.append(" order by obj.tokenGympass " + sortOrder);
                } else if (sortField.equalsIgnoreCase("valorGympass")) {
                    where.append(" order by obj.valorGympass " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc ");
            }
        } else {
            where.append(" order by obj.codigo desc ");
        }

        return findByParam(where, params);
    }

}
