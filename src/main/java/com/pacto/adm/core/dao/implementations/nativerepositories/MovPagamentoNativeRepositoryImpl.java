package com.pacto.adm.core.dao.implementations.nativerepositories;

import com.pacto.adm.core.dao.interfaces.nativerepositories.movpagamento.MovPagamentoNativeRepository;
import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.FormaPagamentoDTO;
import com.pacto.adm.core.dto.MovPagamentoDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.movpagamento.MovPagamentoTotaisDTO;
import com.pacto.adm.core.util.QueryUtils;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class MovPagamentoNativeRepositoryImpl implements MovPagamentoNativeRepository {

    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public MovPagamentoTotaisDTO pagamentosComDataBaseAlterada(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception {
        if (filtroBIControleOperacoesJSON.getInicio() == null) {
            throw new Exception("Deve ser informado o filtro data início para consulta!");
        }

        if (filtroBIControleOperacoesJSON.getFim() == null) {
            throw new Exception("Deve ser informado o filtro data fim para consulta!");
        }
        List<MovPagamentoDTO> lista;
        String sql = sqlConsultarPagamentosComDataBaseAlterada(filtroBIControleOperacoesJSON, paginadorDTO).toString();

        Query query = entityManager.createNativeQuery(sql, Tuple.class);

        int size = 3;
        int page = 1;

        if (paginadorDTO != null) {
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);

        setParametersPagamentosComDataBaseAlterada(query, filtroBIControleOperacoesJSON);

        List<Tuple> resultado = query.getResultList();

        lista = resultado.stream().map(tuple -> new MovPagamentoDTO(
                new ClienteDTO(
                        tuple.get("matriculaCliente", String.class),
                        tuple.get("codigoMatriculaCliente", Integer.class),
                        new PessoaDTO(tuple.get("nomeCliente", String.class))
                ),
                BigDecimal.valueOf(tuple.get("valor", Float.class)),
                new FormaPagamentoDTO(
                        tuple.get("descricaoFormaPagamento", String.class)
                ),
                new UsuarioDTO(
                        tuple.get("responsavelPagamento", String.class)
                ),
                tuple.get("dataQuitacao", Date.class),
                tuple.get("dataAlteracaoManual", Date.class),
                tuple.get("dataPagamento", Date.class),
                tuple.get("dataLancamento", Date.class)
                )).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParametersPagamentosComDataBaseAlterada(countQuery, filtroBIControleOperacoesJSON);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        MovPagamentoTotaisDTO movPagamentoTotaisDTO = new MovPagamentoTotaisDTO();
        movPagamentoTotaisDTO.setMovPagamentos(lista);

        Query valorTotalQuery = entityManager.createNativeQuery("select sum(mp.valor) " + sql.substring(sql.indexOf("FROM")).replaceAll("(?i)ORDER BY.*$", ""));
        setParametersPagamentosComDataBaseAlterada(valorTotalQuery, filtroBIControleOperacoesJSON);
        Float totalPagamentos = (Float) valorTotalQuery.getSingleResult();
        if (totalPagamentos != null) {
            movPagamentoTotaisDTO.setValorTotalPagamentos(BigDecimal.valueOf(totalPagamentos));
        }

        return movPagamentoTotaisDTO;
    }


    private StringBuilder sqlConsultarPagamentosComDataBaseAlterada(
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO
    ) {
        StringBuilder sqlStr = new StringBuilder("SELECT\n" +
                "p.nome as nomeCliente,\n" +
                "c.codigomatricula as codigoMatriculaCliente,\n" +
                "c.matricula as matriculaCliente,\n" +
                "mp.datalancamento as dataLancamento,\n" +
                "mp.datapagamento as dataPagamento,\n" +
                "mp.dataalteracaomanual as dataAlteracaoManual,\n" +
                "mp.valor as valor,\n" +
                "f.descricao as descricaoFormaPagamento,\n" +
                "rpPg.nome as responsavelPagamento,\n" +
                "mp.dataquitacao as dataQuitacao\n" +
                "FROM\n" +
                "movpagamento mp\n" +
                "LEFT JOIN pessoa p on p.codigo = mp.pessoa\n" +
                "LEFT JOIN cliente c on c.pessoa = p.codigo\n" +
                "LEFT JOIN usuario rpPg on rpPg.codigo = mp.responsavelpagamento \n" +
                "LEFT JOIN formapagamento f on f.codigo = mp.formapagamento\n");

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            sqlStr.append("INNER JOIN recibopagamento rp on rp.codigo = mp.recibopagamento\n" +
                    "LEFT JOIN usuario usu ON rp.responsavellancamento = usu.codigo\n");
        }

        sqlStr.append("WHERE mp.dataalteracaomanual BETWEEN :dataInicio and :dataFim\n");

        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            sqlStr.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sqlStr, new LinkedHashMap<String, Class<?>>() {{
                put("p.nome", null);
                put("mp.valor", Double.class);
                put("f.descricao", Integer.class);
                put("rpPg.nome", Integer.class);
                put("mp.datalancamento", Date.class);
                put("mp.dataPagamento", Date.class);
                put("mp.dataquitacao", Date.class);
                put("mp.dataAlteracaoManual", Date.class);
            }});
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            sqlStr.append(" AND usu.colaborador IN :colaboradores\n");
        }

        if (filtroBIControleOperacoesJSON.getEmpresa() != 0) {
            sqlStr.append("AND mp.empresa = :codigoEmpresa\n");
        }

        String sortField = "p.nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];

                if (sortField.equalsIgnoreCase("pessoa")) {
                    sortField = "p.nome";
                }
                if (sortField.equals("dataLancamento")) {
                    sortField = "mp.dataLancamento";
                }
                if (sortField.equals("dataPagamento")) {
                    sortField = "mp.dataPagamento";
                }
                if (sortField.equals("dataAlteracaoManual")) {
                    sortField = "mp.dataAlteracaoManual";
                }
                if (sortField.equals("valor")) {
                    sortField = "mp.valor";
                }
                if (sortField.equals("formaPagamento")) {
                    sortField = "f.descricao";
                }
                if (sortField.equals("responsavelPagamento")) {
                    sortField = "rpPg.nome";
                }
                if (sortField.equals("dataQuitacao")) {
                    sortField = "mp.dataQuitacao";
                }
                sortOrder = paginadorDTO.getSort().split(",")[1];
            }

        }
        sqlStr.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sqlStr;
    }

    private void setParametersPagamentosComDataBaseAlterada(Query query, FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON) throws Exception {
        query.setParameter("dataInicio", Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(filtroBIControleOperacoesJSON.getInicio())));
        query.setParameter("dataFim", Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(filtroBIControleOperacoesJSON.getFim())));

        if (StringUtils.hasText(filtroBIControleOperacoesJSON.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIControleOperacoesJSON.getQuickSearchValue(), query);
        }

        if (!CollectionUtils.isEmpty(filtroBIControleOperacoesJSON.getColaboradores())) {
            query.setParameter("colaboradores", filtroBIControleOperacoesJSON.getColaboradores());
        }

        if (filtroBIControleOperacoesJSON.getEmpresa() != 0) {
            query.setParameter("codigoEmpresa", filtroBIControleOperacoesJSON.getEmpresa());
        }
    }
}
