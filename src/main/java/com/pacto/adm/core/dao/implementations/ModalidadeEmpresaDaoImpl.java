package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ModalidadeEmpresaDao;
import com.pacto.adm.core.entities.contrato.ModalidadeEmpresa;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ModalidadeEmpresaDaoImpl extends DaoGenericoImpl<ModalidadeEmpresa, Integer> implements ModalidadeEmpresaDao {


    @Override
    public List<ModalidadeEmpresa> findAllByCodModalidade(Integer codModalidade) throws Exception {

        getCurrentSession().clear();
        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append("SELECT me FROM ModalidadeEmpresa me ");
        hql.append(" WHERE me.modalidade.codigo = :codModalidade");
        params.put("codModalidade", codModalidade);

        Query query = getCurrentSession().createQuery(hql.toString());
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            query.setParameter(entry.getKey(), entry.getValue());
        }
        return query.getResultList();
    }

}
