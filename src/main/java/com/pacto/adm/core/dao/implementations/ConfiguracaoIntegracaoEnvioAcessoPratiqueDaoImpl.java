package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoEnvioAcessoPratiqueDao;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoAcessoPratique;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ConfiguracaoIntegracaoEnvioAcessoPratiqueDaoImpl extends DaoGenericoImpl<ConfiguracaoIntegracaoAcessoPratique, Integer> implements ConfiguracaoIntegracaoEnvioAcessoPratiqueDao {
    @Override
    public ConfiguracaoIntegracaoAcessoPratique findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoIntegracaoAcessoPratique obj ");
        s.append(" WHERE obj.empresa.codigo = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ConfiguracaoIntegracaoAcessoPratique config = new ConfiguracaoIntegracaoAcessoPratique();
        try {
            config = (ConfiguracaoIntegracaoAcessoPratique) query.getResultList().get(0);
        } catch (Exception e) {}

        return config;
    }
}
