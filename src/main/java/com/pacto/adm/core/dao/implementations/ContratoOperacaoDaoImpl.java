package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ContratoOperacaoDao;
import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.ClienteInativoPeriodoAcessoDTO;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.ContratoOperacaoDTO;
import com.pacto.adm.core.dto.ContratoOperacaoRetroativaDTO;
import com.pacto.adm.core.dto.JustificativaOperacaoDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroContratoOperacaoJSON;
import com.pacto.adm.core.entities.contrato.ContratoOperacao;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class ContratoOperacaoDaoImpl extends DaoGenericoImpl<ContratoOperacao, Integer> implements ContratoOperacaoDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<ContratoOperacao> findAll(FiltroContratoOperacaoJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" INNER JOIN Cliente cli ON cli.pessoa.codigo = obj.contrato.pessoa.codigo ");
        where.append(" WHERE 1 = 1");
        if (filtros.getMatricula() > 0) {
            where.append(" AND cli.codigoMatricula = :matricula");
            params.put("matricula", filtros.getMatricula());
        }
        if (!filtros.getTipoOperacao().isEmpty()) {
            where.append(" AND obj.tipoOperacao = :tipoOp");
            params.put("tipoOp", filtros.getTipoOperacao());
        }
        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }
        where.append(" order by obj.codigo desc");

        return findByParam(where, params, maxResults, indiceInicial);
    }

    @Override
    public List<ContratoOperacao> findAllByContrato(Integer codigoContrato, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.contrato.codigo = :codigoContrato");
        params.put("codigoContrato", codigoContrato);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("tipoJustificativa")) {
                    where.append(" order by obj.tipoJustificativa.tipoOperacao" + sortOrder);
                } else if(sortField.equalsIgnoreCase("dataInicioEfetivacaoOperacao")) {
                    where.append(" order by obj.dataInicioEfetivacaoOperacao " + sortOrder);
                } else if(sortField.equalsIgnoreCase("nrDiasOperacao")) {
                    where.append(" order by obj.nrDiasOperacao " + sortOrder);
                }
            } else {
                where.append(" order by obj.dataFimEfetivacaoOperacao desc");
            }
        } else {
            where.append(" order by obj.dataFimEfetivacaoOperacao desc");
        }

        return findByParam(where, params);
    }

    @Override
    public boolean consultarCodigoContratoETipoOperacao(Integer codContrato, String tipoOperacao) throws Exception {
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.contrato.codigo = :codContrato AND obj.tipoOperacao = :tipoOperacao");
        params.put("codContrato", codContrato);
        params.put("tipoOperacao", tipoOperacao.toUpperCase());

        return !UteisValidacao.emptyNumber(countWithParam("obj.codigo", where, params).intValue());
    }

    @Override
    public List<ContratoOperacao> findAllByContrato(Integer codigoContrato) throws Exception {
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.contrato.codigo = :codigoContrato");
        params.put("codigoContrato", codigoContrato);

        return findByParam(where, params);
    }

}
