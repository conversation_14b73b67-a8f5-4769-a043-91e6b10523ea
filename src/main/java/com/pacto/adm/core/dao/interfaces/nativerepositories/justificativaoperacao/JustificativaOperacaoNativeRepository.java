package com.pacto.adm.core.dao.interfaces.nativerepositories.justificativaoperacao;

import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.entities.contrato.JustificativaOperacao;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface JustificativaOperacaoNativeRepository {
    List<JustificativaOperacao> contratosCanceladosTransferidosOutroAluno(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception;
}
