package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.filtros.FiltroNotaFiscalJSON;
import com.pacto.adm.core.entities.NotaFiscal;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface NotaFiscalDao extends DaoGenerico<NotaFiscal, Integer> {

    List<NotaFiscal> findByPessoa(Integer codPessoa, FiltroNotaFiscalJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

}
