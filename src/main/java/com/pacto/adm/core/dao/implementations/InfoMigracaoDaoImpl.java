package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.InfoMigracaoDao;
import com.pacto.adm.core.entities.InfoMigracao;
import com.pacto.adm.core.enumerador.TipoInfoMigracaoEnum;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class InfoMigracaoDaoImpl extends DaoGenericoImpl<InfoMigracao, Integer> implements InfoMigracaoDao {

    @Override
    public List<InfoMigracao> findByTipoInfoAndUsuario(Integer tipoInfo, Integer usuario, String username,
                                                       String token, String chave) throws Exception {
        getCurrentSession().clear();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append("WHERE obj.tipoInfo = :tipoInfo \n");
        if (UteisValidacao.emptyNumber(usuario) &&
                !UteisValidacao.emptyString(username)) {
            Uteis.logar("InfoMigracaoDaoImpl | infomigracao | Buscou recurso com o username | Chave: " + (chave == null ? "null" : chave) +
                    " | Username: " + username +
                    " | Usuario: " + (usuario == null ? "null" : usuario) +
                    " | Token: " + (token == null ? "null" : token));
            where.append("AND obj.usuario.username = :username \n");
            params.put("username", username);
        } else {
            where.append("AND obj.usuario.codigo = :usuario \n");
            params.put("usuario", usuario);
        }

        where.append("ORDER BY obj.codigo DESC \n");
        params.put("tipoInfo", tipoInfo);
        return findByParam(where, params);
    }

    @Override
    public List<InfoMigracao> findAllHabilitadosByUsuario(Integer usuario, String username, String token, String chave) throws Exception {
        getCurrentSession().clear();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append("WHERE info = 'true'\n");
        if (UteisValidacao.emptyNumber(usuario) &&
                !UteisValidacao.emptyString(username)) {
            Uteis.logar("InfoMigracaoDaoImpl | infomigracao | Buscou recurso com o username | Chave: " + (chave == null ? "null" : chave) +
                    " | Username: " + username +
                    " | Usuario: " + (usuario == null ? "null" : usuario) +
                    " | Token: " + (token == null ? "null" : token));
            where.append("AND obj.usuario.username = :username \n");
            params.put("username", username);
        } else {
            where.append("AND obj.usuario.codigo = :usuario \n");
            params.put("usuario", usuario);
        }

        where.append("ORDER BY obj.codigo DESC \n");
        List<InfoMigracao> lista = findByParam(where, params);
        if (UteisValidacao.emptyList(lista) &&
                chave != null &&
                (chave.equalsIgnoreCase("1a2fd82ae16bb8801fb26de95aa9505e") ||
                        chave.equalsIgnoreCase("6d67e74f46d3ebcf2b77cfc7994ba6b9") ||
                        chave.equalsIgnoreCase("3c67485ada5af06f8293840221f8480"))) {
            Uteis.logar("InfoMigracaoDaoImpl | infomigracao | Buscou e não encontrou info com o username | Chave: " + chave +
                    " | Username: " + username +
                    " | Usuario: " + (usuario == null ? "null" : usuario) +
                    " | Token: " + (token == null ? "null" : token));
        }
        return lista;
    }

    public Boolean podeUsarNegociacao(Integer empresa) throws Exception {
        final Boolean[] temPlanoPacoteCredito = {false};
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement("select count(p.codigo) as cont from plano p  \n" +
                                "left join planocomposicao pc on pc.plano = p.codigo\n" +
                                "left join composicao c on c.codigo = pc.composicao\n" +
                                "where p.empresa = "+ empresa +" and p.vigenciaate > current_date\n" +
                                "and (c.codigo is not null or p.creditosessao)");
                        ResultSet rs = pst.executeQuery();
                        if (rs.next()) {
                            Integer cont = rs.getInt("cont");
                            temPlanoPacoteCredito[0] = cont > 0;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return !temPlanoPacoteCredito[0];
    }

    public Boolean recursoPadraoEmpresa(TipoInfoMigracaoEnum recurso, Integer empresa) throws Exception {
        final Boolean[] padrao = {false};
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        StringBuilder sql = new StringBuilder();
                        sql.append("select exists( \n");
                        sql.append("select \n");
                        sql.append("e.codigo \n");
                        sql.append("from empresa e \n");
                        sql.append("where e.tiposInfoMigracaoPadrao ilike '%").append(recurso.name()).append("%' \n");
                        if (!UteisValidacao.emptyNumber(empresa)) {
                            sql.append("and e.codigo = ").append(empresa).append(" \n");
                        }
                        sql.append(") as existe ");
                        PreparedStatement pst = con.prepareStatement(sql.toString());
                        ResultSet rs = pst.executeQuery();
                        if (rs.next()) {
                            padrao[0] = rs.getBoolean("existe");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return padrao[0];
    }
}
