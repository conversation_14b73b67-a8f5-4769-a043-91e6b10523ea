package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoWordPressDao;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoWordPress;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ConfiguracaoIntegracaoWordPressDaoImpl extends DaoGenericoImpl<ConfiguracaoIntegracaoWordPress, Integer> implements ConfiguracaoIntegracaoWordPressDao {
    @Override
    public ConfiguracaoIntegracaoWordPress findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoIntegracaoWordPress obj ");
        s.append(" WHERE obj.empresa.codigo = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ConfiguracaoIntegracaoWordPress configuracaoIntegracaoWordPress = new ConfiguracaoIntegracaoWordPress();
        try {
            configuracaoIntegracaoWordPress = (ConfiguracaoIntegracaoWordPress) query.getResultList().get(0);
        } catch (Exception e) {
        }
        return configuracaoIntegracaoWordPress;
    }
}
