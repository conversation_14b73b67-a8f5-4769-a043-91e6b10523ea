package com.pacto.adm.core.dao.interfaces.permissao;

import com.pacto.adm.core.entities.Permissao;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PermissaoRepository extends JpaRepository<Permissao, Integer> {

    @Query(
            value = "select case when count(p.nomeentidade) > 0 then true else false end from permissao p\n" +
                    "inner join usuarioperfilacesso u on u.perfilacesso = p.codperfilacesso\n" +
                    "where upper(p.nomeentidade) = UPPER(:nomeEntidade) and p.tituloapresentacao = :tituloApresentacao\n" +
                    "and u.usuario = :codUsuario",
            nativeQuery = true
    )
    boolean existsByCodUsuarioAndNomeEntidadeAndTituloApresentacao(
            @Param("codUsuario") Integer codUsuario,
            @Param("nomeEntidade") String nomeEntidade,
            @Param("tituloApresentacao") String tituloApresentacao
    );

}
