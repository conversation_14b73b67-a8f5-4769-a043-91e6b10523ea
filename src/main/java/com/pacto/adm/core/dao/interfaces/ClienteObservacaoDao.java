package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.ClienteObservacao;
import com.pacto.config.dto.PaginadorDTO;
import org.json.JSONObject;

import java.util.List;

public interface ClienteObservacaoDao extends DaoGenerico<ClienteObservacao, Integer> {

    List<ClienteObservacao> findAllByPessoa(Integer codPessoa, JSONObject filtros, PaginadorDTO paginadorDTO) throws Exception;

    Integer totalByPessoa(Integer codPessoa) throws Exception;

    List<ClienteObservacao> findAllByMatricula(Integer codMatricula) throws Exception;
}
