package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.HistoricoPontosDao;
import com.pacto.adm.core.entities.clubeDeVantagens.HistoricoPontos;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;

@Repository
public class HistoricoPontosDaoImpl extends DaoGenericoImpl<HistoricoPontos, Integer> implements HistoricoPontosDao {

    @Override
    public Integer consultarPontosTotalPorCliente(Integer cliente) throws Exception {
        try {
            final Integer[] valor = new Integer[1];
            valor[0] = 0;
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder query = new StringBuilder();
                        query.append("SELECT pontostotal from historicopontos where cliente =").append(cliente);
                        query.append("order by codigo desc limit 1 ");
                        PreparedStatement pst = connection.prepareStatement(query.toString());
                        ResultSet rs = pst.executeQuery();
                        if (rs.next()){
                            valor[0] = rs.getInt(1);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
            return valor[0];
        }catch (Exception e){
            throw new ServiceException(e);
        }


    }
}
