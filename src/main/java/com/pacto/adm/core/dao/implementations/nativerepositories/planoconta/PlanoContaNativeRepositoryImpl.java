package com.pacto.adm.core.dao.implementations.nativerepositories.planoconta;

import com.pacto.adm.core.dao.interfaces.nativerepositories.planoconta.PlanoContaNativeRepository;
import com.pacto.adm.core.dto.filtros.FiltroPlanoContaDespesasJSON;
import com.pacto.adm.core.entities.PlanoConta;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Repository
public class PlanoContaNativeRepositoryImpl implements PlanoContaNativeRepository {
    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public List<PlanoConta> consultarTodosPlanoContaComValoresPorPeriodoEEmpresa(Integer codigoEmpresa, Date dataInicio, Date dataFim, PaginadorDTO paginadorDTO) throws Exception {
        return consultarTodosPlanoContaComValoresPorPeriodoEEmpresa(null, codigoEmpresa, dataInicio, dataFim, paginadorDTO);
    }

    public List<PlanoConta> consultarTodosPlanoContaComValoresPorPeriodoEEmpresa(String quickSearchValue, Integer codigoEmpresa, Date dataInicio, Date dataFim, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT p.codigo, p.nome, p.insideltv, \n");
        sql.append("(SELECT sum(m.valor) valor FROM movcontarateio mr \n");
        sql.append("INNER JOIN movconta m ON m.codigo = mr.movconta \n");
        sql.append("WHERE mr.planoconta = p.codigo AND m.datavencimento BETWEEN :dataInicio AND :dataFim \n");
        sql.append("AND m.empresa = :empresa ) valor \n");
        sql.append("FROM planoconta p\n");
        sql.append("WHERE tipoes = 2\n");

        if (quickSearchValue != null) {
            sql.append("AND UPPER(p.nome) LIKE UPPER(:quickSearchValue) \n");
        }

        sql.append("ORDER BY p.insideltv desc, p.nome asc\n");

        Query query = entityManager.createNativeQuery(sql.toString(), "PlanoContaSimple");
        query.setParameter("empresa", codigoEmpresa);
        query.setParameter("dataInicio", Uteis.getDataJDBCTimestamp(dataInicio));
        query.setParameter("dataFim", Uteis.getDataJDBCTimestamp(dataFim));

        if (quickSearchValue != null) {
            query.setParameter("quickSearchValue", "%" + quickSearchValue + "%");
        }

        int page = 1;
        int size = 10;

        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage((long) page);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize((long) size);
            }
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);

        return query.getResultList();
    }

    @Override
    public List<PlanoConta> consultarTodosPlanoContaComValoresPorFiltro(FiltroPlanoContaDespesasJSON filtroPlanoContaDespesasJSON, PaginadorDTO paginadorDTO) throws Exception {
        return consultarTodosPlanoContaComValoresPorPeriodoEEmpresa(filtroPlanoContaDespesasJSON.getParametro(), filtroPlanoContaDespesasJSON.getCodigoEmpresa(), filtroPlanoContaDespesasJSON.getDataInicio(), filtroPlanoContaDespesasJSON.getDataFim(), paginadorDTO);
    }
}
