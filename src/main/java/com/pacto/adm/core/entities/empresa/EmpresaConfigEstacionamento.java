package com.pacto.adm.core.entities.empresa;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;

@Entity
public class EmpresaConfigEstacionamento {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String ftpHost;
    private String ftpUser;
    private String ftpPass;
    private Integer ftpPort;
    private boolean enviaValor;
    private boolean enviaHorario;
    private boolean enviaTelefoneEmail;
    private String nomeArquivo;
    private String produtosAdicionar;

    public EmpresaConfigEstacionamento() {
        this.enviaValor = true;
        this.enviaHorario = true;
        this.enviaTelefoneEmail = false;
        this.nomeArquivo = "alunoEstacionamento.txt";
        this.produtosAdicionar = "";
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getFtpHost() {
        return ftpHost;
    }

    public void setFtpHost(String host) {
        this.ftpHost = host;
    }

    public String getFtpUser() {
        return ftpUser;
    }

    public void setFtpUser(String user) {
        this.ftpUser = user;
    }

    public String getFtpPass() {
        return ftpPass;
    }

    public void setFtpPass(String pass) {
        this.ftpPass = pass;
    }

    public Integer getFtpPort() {
        return ftpPort;
    }

    public void setFtpPort(Integer port) {
        this.ftpPort = port;
    }

    public boolean isEnviaValor() {
        return enviaValor;
    }

    public void setEnviaValor(boolean enviaValor) {
        this.enviaValor = enviaValor;
    }

    public boolean isEnviaHorario() {
        return enviaHorario;
    }

    public void setEnviaHorario(boolean enviaHorario) {
        this.enviaHorario = enviaHorario;
    }

    public boolean isEnviaTelefoneEmail() {
        return enviaTelefoneEmail;
    }

    public void setEnviaTelefoneEmail(boolean enviaTelefoneEmail) {
        this.enviaTelefoneEmail = enviaTelefoneEmail;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getProdutosAdicionar() {
        return produtosAdicionar;
    }

    public void setProdutosAdicionar(String produtosAdicionar) {
        this.produtosAdicionar = produtosAdicionar;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
