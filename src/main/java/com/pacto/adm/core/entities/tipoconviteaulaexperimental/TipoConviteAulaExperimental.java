package com.pacto.adm.core.entities.tipoconviteaulaexperimental;

import com.pacto.adm.core.entities.empresa.Empresa;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "tipoconviteaulaexperimental")
public class TipoConviteAulaExperimental {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private Boolean alunoPodeEnviarConvite;
    private Boolean aulasAgendadasSemDiasSeguido;
    private Boolean colaboradorPodeEnviarConvite;
    private Date dataLancamento;
    private String descricao;
    private Integer quantidadeAulaExperimental;
    private Integer quantidadeConviteAlunoPodeEnviar;
    private Date vigenciaInicial;
    private Date vigenciaFinal;

    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "tpconviteaulaexp_empresa_fk"))
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAlunoPodeEnviarConvite() {
        return alunoPodeEnviarConvite;
    }

    public void setAlunoPodeEnviarConvite(Boolean alunoPodeEnviarConvite) {
        this.alunoPodeEnviarConvite = alunoPodeEnviarConvite;
    }

    public Boolean getAulasAgendadasSemDiasSeguido() {
        return aulasAgendadasSemDiasSeguido;
    }

    public void setAulasAgendadasSemDiasSeguido(Boolean aulasAgendadasSemDiasSeguido) {
        this.aulasAgendadasSemDiasSeguido = aulasAgendadasSemDiasSeguido;
    }

    public Boolean getColaboradorPodeEnviarConvite() {
        return colaboradorPodeEnviarConvite;
    }

    public void setColaboradorPodeEnviarConvite(Boolean colaboradorPodeEnviarConvite) {
        this.colaboradorPodeEnviarConvite = colaboradorPodeEnviarConvite;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getQuantidadeAulaExperimental() {
        return quantidadeAulaExperimental;
    }

    public void setQuantidadeAulaExperimental(Integer quantidadeAulaExperimental) {
        this.quantidadeAulaExperimental = quantidadeAulaExperimental;
    }

    public Integer getQuantidadeConviteAlunoPodeEnviar() {
        return quantidadeConviteAlunoPodeEnviar;
    }

    public void setQuantidadeConviteAlunoPodeEnviar(Integer quantidadeConviteAlunoPodeEnviar) {
        this.quantidadeConviteAlunoPodeEnviar = quantidadeConviteAlunoPodeEnviar;
    }

    public Date getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(Date vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
