package com.pacto.adm.core.entities.contrato;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
@Schema(description = "Informações da justificativa de operação")
public class JustificativaOperacao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código único identificador da justificativa de operação", example = "1")
    private Integer codigo;

    @Schema(description = "Tipo da operação realizada", example = "CA")
    private String tipoOperacao;

    @Schema(description = "Descrição detalhada da justificativa da operação", example = "CT.12345.CA.Transferência para outro aluno")
    private String descricao;

    @Schema(description = "Indica se deve isentar a multa de cancelamento", example = "false")
    private Boolean isentarMultaCancelamento;

    @Schema(description = "Indica se não deve cobrar parcelas atrasadas no cancelamento", example = "false")
    private Boolean naoCobrarParcelasAtrasadasCancelamento;

    @Schema(description = "Indica se é necessário anexar comprovante", example = "true")
    private Boolean necessarioAnexarComprovante;

    @Schema(description = "Indica se a justificativa está ativa", example = "true")
    private Boolean ativa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    @Schema(description = "Informações da empresa associada à justificativa")
    private Empresa empresa;

    public JustificativaOperacao() {
    }

    public JustificativaOperacao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Boolean getIsentarMultaCancelamento() {
        return isentarMultaCancelamento;
    }

    public void setIsentarMultaCancelamento(Boolean isentarMultaCancelamento) {
        this.isentarMultaCancelamento = isentarMultaCancelamento;
    }

    public Boolean getNaoCobrarParcelasAtrasadasCancelamento() {
        return naoCobrarParcelasAtrasadasCancelamento;
    }

    public void setNaoCobrarParcelasAtrasadasCancelamento(Boolean naoCobrarParcelasAtrasadasCancelamento) {
        this.naoCobrarParcelasAtrasadasCancelamento = naoCobrarParcelasAtrasadasCancelamento;
    }

    public Boolean getNecessarioAnexarComprovante() {
        return necessarioAnexarComprovante;
    }

    public void setNecessarioAnexarComprovante(Boolean necessarioAnexarComprovante) {
        this.necessarioAnexarComprovante = necessarioAnexarComprovante;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }
}
