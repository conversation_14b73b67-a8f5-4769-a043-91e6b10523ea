package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.AcessoCliente;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.reposicao.Reposicao;
import com.pacto.adm.core.enumerador.TipoOperacaoCreditoTreinoEnum;
import com.pacto.adm.core.enumerador.tipooperacaocontrato.TipoOperacaoCreditoTreinoEnumConverter;
import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.ConstraintMode;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;
import java.util.Date;

@Entity
public class ControleCreditoTreino {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer quantidade;
    private Date dataLancamento;
    private Date dataOperacao;
    private String observacao;

    @Convert(converter = TipoOperacaoCreditoTreinoEnumConverter.class)
    private TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreino;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "auladesmarcada", foreignKey = @ForeignKey(name = "controlecreditotreino_auladesmarc_fkey"))
    private AulaDesmarcada aulaDesmarcada;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "horarioturmafalta", foreignKey = @ForeignKey(name = "fk_controlecredito_horarioturma"))
    private HorarioTurma horarioTurmaFalta;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "contratoorigem", foreignKey = @ForeignKey(name = "fk_controlecreditotreino_contrato"))
    private Contrato contratoOrigem;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "modalidade", foreignKey = @ForeignKey(name = "fk_controlecreditotreino_modalidade"))
    private Modalidade modalidade;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "reposicao", foreignKey = @ForeignKey(name = "controlecreditotreino_reposicao_fkey"))
    private Reposicao reposicao;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "acessocliente", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private AcessoCliente acessoCliente;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Usuario usuario;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Contrato contrato;

    @Transient
    private Integer codigoTipoAjusteManualCreditoTreino;
    @Transient
    private Integer saldo;

    private String descricaoAulaMarcada;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public AulaDesmarcada getAulaDesmarcada() {
        return aulaDesmarcada;
    }

    public void setAulaDesmarcada(AulaDesmarcada aulaDesmarcada) {
        this.aulaDesmarcada = aulaDesmarcada;
    }

    public HorarioTurma getHorarioTurmaFalta() {
        return horarioTurmaFalta;
    }

    public void setHorarioTurmaFalta(HorarioTurma horarioTurmaFalta) {
        this.horarioTurmaFalta = horarioTurmaFalta;
    }

    public Contrato getContratoOrigem() {
        return contratoOrigem;
    }

    public void setContratoOrigem(Contrato contratoOrigem) {
        this.contratoOrigem = contratoOrigem;
    }

    public Modalidade getModalidade() {
        return modalidade;
    }

    public void setModalidade(Modalidade modalidade) {
        this.modalidade = modalidade;
    }

    public Reposicao getReposicao() {
        return reposicao;
    }

    public void setReposicao(Reposicao reposicao) {
        this.reposicao = reposicao;
    }

    public AcessoCliente getAcessoCliente() {
        return acessoCliente;
    }

    public void setAcessoCliente(AcessoCliente acessoCliente) {
        this.acessoCliente = acessoCliente;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public TipoOperacaoCreditoTreinoEnum getTipoOperacaoCreditoTreino() {
        return tipoOperacaoCreditoTreino;
    }

    public void setTipoOperacaoCreditoTreino(TipoOperacaoCreditoTreinoEnum tipoOperacaoContratoEnum) {
        this.tipoOperacaoCreditoTreino = tipoOperacaoContratoEnum;
    }

    public Integer getCodigoTipoAjusteManualCreditoTreino() {
        return codigoTipoAjusteManualCreditoTreino;
    }

    public void setCodigoTipoAjusteManualCreditoTreino(Integer codigoTipoAjusteManualCreditoTreino) {
        this.codigoTipoAjusteManualCreditoTreino = codigoTipoAjusteManualCreditoTreino;
    }

    public Integer getSaldo() {
        return saldo;
    }

    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }

    public String getDescricaoAulaMarcada() {
        return descricaoAulaMarcada;
    }

    public void setDescricaoAulaMarcada(String descricaoAulaMarcada) {
        this.descricaoAulaMarcada = descricaoAulaMarcada;
    }
}
