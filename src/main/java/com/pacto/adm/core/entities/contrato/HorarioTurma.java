package com.pacto.adm.core.entities.contrato;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.Ambiente;
import com.pacto.adm.core.entities.Colaborador;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class HorarioTurma {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name ="diasemana", length = 2, nullable = false)
    private String diaSemana;
    private Integer diaSemanaNumero;

    @Transient
    private Boolean horarioTurmaEscolhida;

    @ManyToOne
    @JoinColumn(name = "turma", foreignKey = @ForeignKey(name = "fk_horarioturma_turma"))
    private Turma turma;

    @ManyToOne
    @JoinColumn(name = "professor", foreignKey = @ForeignKey(name = "fk_horarioturma_professor"))
    private Colaborador professor;

    @ManyToOne
    @JoinColumn(name = "ambiente", foreignKey = @ForeignKey(name = "fk_horarioturma_ambiente"))
    private Ambiente ambiente;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "nivelturma", foreignKey = @ForeignKey(name = "fk_horarioturma_nivelturma"))
    @NotFound(action = NotFoundAction.IGNORE)
    private NivelTurma nivelTurma;

    private String horaInicial;
    private String horaFinal;

    public Colaborador getProfessor() {
        return professor;
    }

    public void setProfessor(Colaborador professor) {
        this.professor = professor;
    }

    public Ambiente getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Ambiente ambiente) {
        this.ambiente = ambiente;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public NivelTurma getNivelTurma() {
        return nivelTurma;
    }

    public void setNivelTurma(NivelTurma nivelTurma) {
        this.nivelTurma = nivelTurma;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getDiaSemanaNumero() {
        return diaSemanaNumero;
    }

    public void setDiaSemanaNumero(Integer diaSemanaNumero) {
        this.diaSemanaNumero = diaSemanaNumero;
    }

    public Boolean getHorarioTurmaEscolhida() {
        return horarioTurmaEscolhida;
    }

    public void setHorarioTurmaEscolhida(Boolean horarioTurmaEscolhida) {
        this.horarioTurmaEscolhida = horarioTurmaEscolhida;
    }

    public Turma getTurma() {
        return turma;
    }

    public void setTurma(Turma turma) {
        this.turma = turma;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }
}
