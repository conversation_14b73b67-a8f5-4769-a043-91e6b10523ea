package com.pacto.adm.core.entities.financeiro;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.MovPagamento;
import com.pacto.adm.core.entities.MovParcela;
import com.pacto.adm.core.entities.ReciboPagamento;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class PagamentoMovParcela {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Double valorPago;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "movpagamento", foreignKey = @ForeignKey(name = "fk_pagamentomovparcela_movpagamento"))
    @NotFound(action = NotFoundAction.IGNORE)
    private MovPagamento movPagamento;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "movparcela")
    @NotFound(action = NotFoundAction.IGNORE)
    private MovParcela parcela;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "recibopagamento")
    @NotFound(action = NotFoundAction.IGNORE)
    private ReciboPagamento recibo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorPago() {
        return valorPago;
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = valorPago;
    }


    public MovPagamento getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(MovPagamento movPagamento) {
        this.movPagamento = movPagamento;
    }

    public MovParcela getParcela() {
        return parcela;
    }

    public void setParcela(MovParcela parcela) {
        this.parcela = parcela;
    }

    public ReciboPagamento getRecibo() {
        return recibo;
    }

    public void setRecibo(ReciboPagamento recibo) {
        this.recibo = recibo;
    }
}
