package com.pacto.adm.core.entities;

import com.pacto.config.annotations.NotLogged;
import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.financeiro.FormaPagamento;
import com.pacto.adm.core.entities.financeiro.PagamentoMovParcela;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Entity
public class MovPagamento {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer reciboPagamento;
    private Double valor;
    private boolean credito;
    private boolean movPagamentoEscolhida;
    private Integer nrParcelaCartaoCredito;

    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Empresa empresa;

    private String nomePagador;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "datalancamento")
    private Date dataLancamento;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dataPagamento")
    private Date dataPagamento;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dataquitacao")
    private Date dataQuitacao;
    private BigDecimal valorTotal;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_movpagamento_pessoa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "formapagamento", foreignKey = @ForeignKey(name = "fk_movpagamento_formapagamento"))
    @NotFound(action = NotFoundAction.IGNORE)
    private FormaPagamento formaPagamento;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavelpagamento", foreignKey = @ForeignKey(name = "fk_movpagamento_responsavelpagamento"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelPagamento;

    @OneToMany(mappedBy = "movPagamento", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<PagamentoMovParcela> pagamentosMovParcelas;

    @NotLogged
    @Transient
    private Double valorPP = 0.0; // usado nos calculo dos produtos pagos
    @NotLogged
    @Transient
    private Double valorPPCancelado = 0.0; // usado nos calculo dos produtos pagos
    @NotLogged
    @Transient
    private Integer statusConciliadora;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(Integer reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getNomePagador() {
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public FormaPagamento getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamento formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }


    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Usuario getResponsavelPagamento() {
        return responsavelPagamento;
    }

    public void setResponsavelPagamento(Usuario responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }

    public boolean isCredito() {
        return credito;
    }

    public void setCredito(boolean credito) {
        this.credito = credito;
    }

    public boolean isMovPagamentoEscolhida() {
        return movPagamentoEscolhida;
    }

    public void setMovPagamentoEscolhida(boolean movPagamentoEscolhida) {
        this.movPagamentoEscolhida = movPagamentoEscolhida;
    }

    public Integer getNrParcelaCartaoCredito() {
        return nrParcelaCartaoCredito;
    }

    public void setNrParcelaCartaoCredito(Integer nrParcelaCartaoCredito) {
        this.nrParcelaCartaoCredito = nrParcelaCartaoCredito;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Date getDataQuitacao() {
        return dataQuitacao;
    }

    public void setDataQuitacao(Date dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public Set<PagamentoMovParcela> getPagamentosMovParcelas() {
        if (pagamentosMovParcelas == null) {
            pagamentosMovParcelas = new HashSet<>();
        }
        return pagamentosMovParcelas;
    }

    public void setPagamentosMovParcelas(Set<PagamentoMovParcela> pagamentosMovParcelas) {
        this.pagamentosMovParcelas = pagamentosMovParcelas;
    }

    public Double getValorPP() {
        return valorPP;
    }

    public void setValorPP(Double valorPP) {
        this.valorPP = valorPP;
    }

    public Double getValorPPCancelado() {
        return valorPPCancelado;
    }

    public void setValorPPCancelado(Double valorPPCancelado) {
        this.valorPPCancelado = valorPPCancelado;
    }

    public Integer getStatusConciliadora() {
        if (statusConciliadora == null) {
            statusConciliadora = 0;
        }
        return statusConciliadora;
    }

    public void setStatusConciliadora(Integer statusConciliadora) {
        this.statusConciliadora = statusConciliadora;
    }
}
