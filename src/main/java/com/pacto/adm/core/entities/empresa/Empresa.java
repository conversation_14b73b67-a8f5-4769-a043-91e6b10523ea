package com.pacto.adm.core.entities.empresa;

import com.pacto.adm.core.entities.Questionario;
import com.pacto.config.annotations.NomeEntidadeLog;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.*;


@Entity
@NomeEntidadeLog("Empresa")
@Schema(description = "Informações da empresa")
public class Empresa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código único identificador da empresa", example = "1")
    private Integer codigo;

    @Schema(description = "Nome da empresa", example = "Academia Exemplo")
    private String nome;
    private Integer toleranciaOcupacaoTurma;
    private Double juroParcela;
    private Double multa;
    private Boolean habilitarSomaDeAulaNaoVigente = false;
    private Boolean habilitarCobrancaAutomaticaNaVenda = false;
    private Boolean adicionarAulasDesmarcadasContratoAnterior = false;
    private String codigoGymPass;
    private String tokenApiGymPass;
    private String tokenAcademyGogood;
    private Integer limiteDeAcessosPorDiaGympass;
    private Integer limiteDeAulasPorDiaGympass;
    private Boolean senhaAcessoOnzeDigitos = false;
    private Boolean ativa = true;
    private Boolean integracaoMyWellneHabilitada = false;
    private Boolean integracaoMyWellnessEnviarVinculos = true;
    private Boolean integracaoMyWellnessEnviarGrupos = true;
    private String integracaoMyWellnessFacilityUrl;
    private String integracaMyWellneApiKey;
    private String integracaoMyWellnessUser;
    private String integracaoMyWellnessPassword;
    private Integer nrDiasVigenciaMyWellnessGymPass;
    private Integer nrDiasVigenteQuestionarioRematricula;
    private Integer nrDiasVigenteQuestionarioRetorno;
    private Integer tipoVigenciaMyWellnessGympass;
    private Boolean integracaoMentorWebHabilitada = false;
    private String integracaoMentorWebUrl = "";
    private String integracaoMentorWebServico = "";
    private String integracaoMentorWebUser = "";
    private String integracaoMentorWebPassword = "";
    private Boolean utilizaSistemaEstacionamento = false;
    @OneToOne(mappedBy = "empresa", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private EmpresaConfigEstacionamento empresaConfigEstacionamento;
    private Boolean usarParceiroFidelidade = false;
    private Boolean notificarWebhook = false;
    private String urlWebhookNotificar;
    private Boolean integracaoAmigoFitHabilitada = false;
    private String nomeUsuarioAmigoFit;
    private String senhaUsuarioAmigoFit;
    private String tokenBuzzLead = "";
    private String tokenSMS;
    private String tokenSMSShortCode;
    private Boolean integracaoWeHelpHabilitada = false;
    private Boolean cpfCodigoInternoWeHelp = false;
    private Boolean integracaoF360RelFatHabilitada = false;
    private String integracaoF360FtpServer;
    private String email;
    private Integer integracaoF360FtpPort;
    private String integracaoF360User;
    private String integracaoF360Password;
    private String integracaoF360Dir;
    private Boolean integracaoF360Quinzenal = false;
    private Boolean usarConciliadora = false;
    private String empresaConciliadora;
    private String senhaConciliadora;
    @Column(name = "usarsescdf", columnDefinition = "DEFAULT FALSE")
    private Boolean usarSescDf;
    @Column(name = "tokensescdf")
    private String tokenSescDf;
    @Column(name = "utilizaconfigcancelamentosesc", columnDefinition = "DEFAULT FALSE")
    private Boolean utilizaConfigCancelamentoSesc;
    // Spivi
    private Boolean integracaoSpiviHabilitada = false;
    private String integracaoSpiviSourceName;
    private Integer integracaoSpiviSiteID;
    private String integracaoSpiviPassword;
    private Boolean usaIntegracoesCrm = false;
    private Integer tipoGestaoNfse;

    private Integer arredondamento;
    private String cnpj;
    private String razaoSocial;

    @Column(name = "trabalharcompontuacao", columnDefinition = "DEFAULT FALSE")
    private Boolean trabalharComPontuacao;
    @Column(name = "usarnfse", columnDefinition = "DEFAULT FALSE")
    private Boolean usarNfse;
    @Column(name = "usarnfce", columnDefinition = "DEFAULT FALSE")
    private Boolean usarNfce;

    @Column(name = "usargestaocreditospersonal", columnDefinition = "DEFAULT FALSE")
    private Boolean usarGestaoCreditosPersonal;

    private Boolean consultarNovoCadastroSPC = false;
    private Boolean pesquisaAutomaticaSPC = false;
    private Long codigoAssociadoSPC;
    private String operadorSPC;
    private String senhaSPC;

    private Boolean usaVitio = false;
    private String linkCheckoutVitio;
    private String linkEbook;
    private String mensagemVitioQuerComprar;
    private String mensagemVitioWpp;

    private Boolean utilizaIntegracaoDelsoft = false;
    private String hostIntegracaoDelsoft;
    private Integer portaIntegracaoDelsoft;
    private String tokenIntegracaoDelsoft;
    private String nomeAplicacaoDelsoft;
    private String usuarioAplicacaoDelsoft;
    private String senhaAplicacaoDelsoft;
    private Integer planoAplicacaoDelsoft;
    private Boolean somenteVendaProdutosComEstoque;
    @Column(name = "carenciaRenovacao")
    private Integer carenciaRenovacao;
    private Boolean concContasPagarFacilitePay = false;
    private Boolean concContasReceberFacilitePay = false;
    private Boolean facilitePayReguaCobranca = false;
    private Boolean facilitePayCDLSPC = false;
    private Boolean facilitePayConciliacaoCartao = false;
    private Double valorMetaFacilitePay;
    private Boolean facilitePayReguaCobrancaEmail = false;
    private Boolean facilitePayReguaCobrancaSms = false;
    private Boolean facilitePayReguaCobrancaApp = false;
    private Boolean facilitePayReguaCobrancaWhatsApp = false;
    private Boolean facilitePayReguaCobrancaGymbotPro = false;
    private int qtdLmtContasConcFacilitePay = 0;
    private Boolean utilizaGestaoClientesComRestricoes = false;
    private Boolean habilitarCadastroEmpresaSesi = false;
    private String integracaoNuvemshopNomeApp;
    private String integracaoNuvemshopEmail;
    private String integracaoNuvemshopTokenAcesso;
    private String integracaoNuvemshopStoreId;
    private Boolean integracaoNuvemshopHabilitada = false;
    protected Boolean permiteContratosConcomintante = false;
    private Boolean pontuarApenasCampanhasAtivas = false;
    private Boolean utilizarPactoPrint = false;
    private Boolean horariocapacidadeporcategoria = false;
    private Boolean bvObrigatorio;

    @ManyToOne
    @JoinColumn(name = "questionarioprimeiracompra", foreignKey = @ForeignKey(name = "fk_configuracaosistema_questionarioprimeiracompra"))
    private Questionario questionarioPrimeiraCompra;
    @ManyToOne
    @JoinColumn(name = "questionarioprimeiravisita", foreignKey = @ForeignKey(name = "fk_empresa_questionarioprimeiravisita"))
    private Questionario questionarioPrimeiraVisita;
    @ManyToOne
    @JoinColumn(name = "questionariorematricula", foreignKey = @ForeignKey(name = "fk_empresa_questionariorematricula"))
    private Questionario questionarioRematricula;
    @ManyToOne
    @JoinColumn(name = "questionarioretorno", foreignKey = @ForeignKey(name = "fk_empresa_questionarioretorno"))
    private Questionario questionarioRetorno;
    @ManyToOne
    @JoinColumn(name = "questionarioretornocompra", foreignKey = @ForeignKey(name = "fk_configuracaosistema_questionarioretornocompra"))
    private Questionario questionarioRetornoCompra;
    private long nrDiasVigenteQuestionarioRetornoCompra;
    private Boolean integracaoManyChatHabilitada = false;
    private String integracaoManyChatTokenApi = "";
    private String integracaoManyChatTagUnidade = "";
    @Column(name = "valorlimitecaixaabertovendaavulsa")
    private Double valorLimiteCaixaAbertoVendaAvulsa;

    public Empresa() {
    }

    public Empresa(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getToleranciaOcupacaoTurma() {
        return toleranciaOcupacaoTurma;
    }

    public void setToleranciaOcupacaoTurma(Integer toleranciaOcupacaoTurma) {
        this.toleranciaOcupacaoTurma = toleranciaOcupacaoTurma;
    }

    public String getCodigoGymPass() {
        return codigoGymPass;
    }

    public void setCodigoGymPass(String codigoGymPass) {
        this.codigoGymPass = codigoGymPass;
    }

    public String getTokenApiGymPass() {
        return tokenApiGymPass;
    }

    public void setTokenApiGymPass(String tokenApiGymPass) {
        this.tokenApiGymPass = tokenApiGymPass;
    }

    public String getTokenAcademyGogood() {
        return tokenAcademyGogood;
    }

    public void setTokenAcademyGogood(String tokenAcademyGogood) {
        this.tokenAcademyGogood = tokenAcademyGogood;
    }

    public Integer getLimiteDeAcessosPorDiaGympass() {
        return limiteDeAcessosPorDiaGympass;
    }

    public void setLimiteDeAcessosPorDiaGympass(Integer limiteDeAcessosPorDiaGympass) {
        this.limiteDeAcessosPorDiaGympass = limiteDeAcessosPorDiaGympass;
    }

    public Integer getLimiteDeAulasPorDiaGympass() {
        return limiteDeAulasPorDiaGympass;
    }

    public void setLimiteDeAulasPorDiaGympass(Integer limiteDeAulasPorDiaGympass) {
        this.limiteDeAulasPorDiaGympass = limiteDeAulasPorDiaGympass;
    }

    public Boolean getSenhaAcessoOnzeDigitos() {
        return senhaAcessoOnzeDigitos;
    }

    public void setSenhaAcessoOnzeDigitos(Boolean senhaAcessoOnzeDigitos) {
        this.senhaAcessoOnzeDigitos = senhaAcessoOnzeDigitos;
    }

    public Double getJuroParcela() {
        return juroParcela;
    }

    public void setJuroParcela(Double juroParcela) {
        this.juroParcela = juroParcela;
    }

    public Double getMulta() {
        return multa;
    }

    public void setMulta(Double multa) {
        this.multa = multa;
    }

    public Boolean getAdicionarAulasDesmarcadasContratoAnterior() {
        return adicionarAulasDesmarcadasContratoAnterior;
    }

    public void setAdicionarAulasDesmarcadasContratoAnterior(Boolean adicionarAulasDesmarcadasContratoAnterior) {
        this.adicionarAulasDesmarcadasContratoAnterior = adicionarAulasDesmarcadasContratoAnterior;
    }

    public Boolean getHabilitarSomaDeAulaNaoVigente() {
        return habilitarSomaDeAulaNaoVigente;
    }

    public void setHabilitarSomaDeAulaNaoVigente(Boolean habilitarSomaDeAulaNaoVigente) {
        this.habilitarSomaDeAulaNaoVigente = habilitarSomaDeAulaNaoVigente;
    }

    public Boolean getHabilitarCobrancaAutomaticaNaVenda() {
        return habilitarCobrancaAutomaticaNaVenda;
    }

    public void setHabilitarCobrancaAutomatica(Boolean habilitarCobrancaAutomaticaNaVenda) {
        this.habilitarCobrancaAutomaticaNaVenda = habilitarCobrancaAutomaticaNaVenda;
    }

    public Boolean getTrabalharComPontuacao() {
        return trabalharComPontuacao;
    }

    public void setTrabalharComPontuacao(Boolean trabalharComPontuacao) {
        this.trabalharComPontuacao = trabalharComPontuacao;
    }

    public Boolean getUsarNfse() {
        return usarNfse;
    }

    public void setUsarNfse(Boolean usarNfse) {
        this.usarNfse = usarNfse;
    }

    public Boolean getUsarNfce() {
        return usarNfce;
    }

    public void setUsarNfce(Boolean usarNfce) {
        this.usarNfce = usarNfce;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public Boolean isIntegracaoMyWellneHabilitada() {
        return integracaoMyWellneHabilitada;
    }

    public void setIntegracaoMyWellneHabilitada(Boolean integracaoMyWellneHabilitada) {
        this.integracaoMyWellneHabilitada = integracaoMyWellneHabilitada;
    }

    public Boolean isIntegracaoMyWellnessEnviarVinculos() {
        return integracaoMyWellnessEnviarVinculos;
    }

    public void setIntegracaoMyWellnessEnviarVinculos(Boolean integracaoMyWellnessEnviarVinculos) {
        this.integracaoMyWellnessEnviarVinculos = integracaoMyWellnessEnviarVinculos;
    }

    public Boolean isIntegracaoMyWellnessEnviarGrupos() {
        return integracaoMyWellnessEnviarGrupos;
    }

    public void setIntegracaoMyWellnessEnviarGrupos(Boolean integracaoMyWellnessEnviarGrupos) {
        this.integracaoMyWellnessEnviarGrupos = integracaoMyWellnessEnviarGrupos;
    }

    public String getIntegracaoMyWellnessFacilityUrl() {
        return integracaoMyWellnessFacilityUrl;
    }

    public void setIntegracaoMyWellnessFacilityUrl(String integracaoMyWellnessFacilityUrl) {
        this.integracaoMyWellnessFacilityUrl = integracaoMyWellnessFacilityUrl;
    }

    public String getIntegracaMyWellneApiKey() {
        return integracaMyWellneApiKey;
    }

    public void setIntegracaMyWellneApiKey(String integracaMyWellneApiKey) {
        this.integracaMyWellneApiKey = integracaMyWellneApiKey;
    }

    public String getIntegracaoMyWellnessUser() {
        return integracaoMyWellnessUser;
    }

    public void setIntegracaoMyWellnessUser(String integracaoMyWellnessUser) {
        this.integracaoMyWellnessUser = integracaoMyWellnessUser;
    }

    public String getIntegracaoMyWellnessPassword() {
        return integracaoMyWellnessPassword;
    }

    public void setIntegracaoMyWellnessPassword(String integracaoMyWellnessPassword) {
        this.integracaoMyWellnessPassword = integracaoMyWellnessPassword;
    }

    public Integer getNrDiasVigenciaMyWellnessGymPass() {
        return nrDiasVigenciaMyWellnessGymPass;
    }

    public void setNrDiasVigenciaMyWellnessGymPass(Integer nrDiasVigenciaMyWellnessGymPass) {
        this.nrDiasVigenciaMyWellnessGymPass = nrDiasVigenciaMyWellnessGymPass;
    }

    public Integer getTipoVigenciaMyWellnessGympass() {
        return tipoVigenciaMyWellnessGympass;
    }

    public void setTipoVigenciaMyWellnessGympass(Integer tipoVigenciaMyWellnessGympass) {
        this.tipoVigenciaMyWellnessGympass = tipoVigenciaMyWellnessGympass;
    }

    public Boolean isIntegracaoMentorWebHabilitada() {
        return integracaoMentorWebHabilitada;
    }

    public void setIntegracaoMentorWebHabilitada(Boolean integracaoMentorWebHabilitada) {
        this.integracaoMentorWebHabilitada = integracaoMentorWebHabilitada;
    }

    public String getIntegracaoMentorWebUrl() {
        return integracaoMentorWebUrl;
    }

    public void setIntegracaoMentorWebUrl(String integracaoMentorWebUrl) {
        this.integracaoMentorWebUrl = integracaoMentorWebUrl;
    }

    public String getIntegracaoMentorWebServico() {
        return integracaoMentorWebServico;
    }

    public void setIntegracaoMentorWebServico(String integracaoMentorWebServico) {
        this.integracaoMentorWebServico = integracaoMentorWebServico;
    }

    public String getIntegracaoMentorWebUser() {
        return integracaoMentorWebUser;
    }

    public void setIntegracaoMentorWebUser(String integracaoMentorWebUser) {
        this.integracaoMentorWebUser = integracaoMentorWebUser;
    }

    public String getIntegracaoMentorWebPassword() {
        return integracaoMentorWebPassword;
    }

    public void setIntegracaoMentorWebPassword(String integracaoMentorWebPassword) {
        this.integracaoMentorWebPassword = integracaoMentorWebPassword;
    }

    public Boolean isUtilizaSistemaEstacionamento() {
        return utilizaSistemaEstacionamento;
    }

    public void setUtilizaSistemaEstacionamento(Boolean utilizaSistemaEstacionamento) {
        this.utilizaSistemaEstacionamento = utilizaSistemaEstacionamento;
    }

    public EmpresaConfigEstacionamento getEmpresaConfigEstacionamento() {
        return empresaConfigEstacionamento;
    }

    public void setEmpresaConfigEstacionamento(EmpresaConfigEstacionamento empresaConfigEstacionamento) {
        this.empresaConfigEstacionamento = empresaConfigEstacionamento;
    }

    public Boolean isUsarParceiroFidelidade() {
        return usarParceiroFidelidade;
    }

    public void setUsarParceiroFidelidade(Boolean usarParceiroFidelidade) {
        this.usarParceiroFidelidade = usarParceiroFidelidade;
    }

    public Boolean isNotificarWebhook() {
        return notificarWebhook;
    }

    public void setNotificarWebhook(Boolean notificarWebhook) {
        this.notificarWebhook = notificarWebhook;
    }

    public String getUrlWebhookNotificar() {
        return urlWebhookNotificar;
    }

    public void setUrlWebhookNotificar(String urlWebhookNotificar) {
        this.urlWebhookNotificar = urlWebhookNotificar;
    }

    public Boolean isIntegracaoAmigoFitHabilitada() {
        return integracaoAmigoFitHabilitada;
    }

    public void setIntegracaoAmigoFitHabilitada(Boolean integracaoAmigoFitHabilitada) {
        this.integracaoAmigoFitHabilitada = integracaoAmigoFitHabilitada;
    }

    public String getNomeUsuarioAmigoFit() {
        return nomeUsuarioAmigoFit;
    }

    public void setNomeUsuarioAmigoFit(String nomeUsuarioAmigoFit) {
        this.nomeUsuarioAmigoFit = nomeUsuarioAmigoFit;
    }

    public String getSenhaUsuarioAmigoFit() {
        return senhaUsuarioAmigoFit;
    }

    public void setSenhaUsuarioAmigoFit(String senhaUsuarioAmigoFit) {
        this.senhaUsuarioAmigoFit = senhaUsuarioAmigoFit;
    }


    public String getTokenBuzzLead() {
        return tokenBuzzLead;
    }

    public void setTokenBuzzLead(String tokenBuzzLead) {
        this.tokenBuzzLead = tokenBuzzLead;
    }

    public String getTokenSMS() {
        return tokenSMS;
    }

    public void setTokenSMS(String tokenSMS) {
        this.tokenSMS = tokenSMS;
    }

    public String getTokenSMSShortCode() {
        return tokenSMSShortCode;
    }

    public void setTokenSMSShortCode(String tokenSMSShortCode) {
        this.tokenSMSShortCode = tokenSMSShortCode;
    }

    public Boolean isIntegracaoWeHelpHabilitada() {
        return integracaoWeHelpHabilitada;
    }

    public void setIntegracaoWeHelpHabilitada(Boolean integracaoWeHelpHabilitada) {
        this.integracaoWeHelpHabilitada = integracaoWeHelpHabilitada;
    }

    public Boolean isCpfCodigoInternoWeHelp() {
        return cpfCodigoInternoWeHelp;
    }

    public void setCpfCodigoInternoWeHelp(Boolean cpfCodigoInternoWeHelp) {
        this.cpfCodigoInternoWeHelp = cpfCodigoInternoWeHelp;
    }

    public Boolean isIntegracaoF360RelFatHabilitada() {
        return integracaoF360RelFatHabilitada;
    }

    public void setIntegracaoF360RelFatHabilitada(Boolean integracaoF360RelFatHabilitada) {
        this.integracaoF360RelFatHabilitada = integracaoF360RelFatHabilitada;
    }

    public String getIntegracaoF360FtpServer() {
        return integracaoF360FtpServer;
    }

    public void setIntegracaoF360FtpServer(String integracaoF360FtpServer) {
        this.integracaoF360FtpServer = integracaoF360FtpServer;
    }

    public Integer getIntegracaoF360FtpPort() {
        return integracaoF360FtpPort;
    }

    public void setIntegracaoF360FtpPort(Integer integracaoF360FtpPort) {
        this.integracaoF360FtpPort = integracaoF360FtpPort;
    }

    public String getIntegracaoF360User() {
        return integracaoF360User;
    }

    public void setIntegracaoF360User(String integracaoF360User) {
        this.integracaoF360User = integracaoF360User;
    }

    public String getIntegracaoF360Password() {
        return integracaoF360Password;
    }

    public void setIntegracaoF360Password(String integracaoF360Password) {
        this.integracaoF360Password = integracaoF360Password;
    }

    public String getIntegracaoF360Dir() {
        return integracaoF360Dir;
    }

    public void setIntegracaoF360Dir(String integracaoF360Dir) {
        this.integracaoF360Dir = integracaoF360Dir;
    }

    public Boolean isIntegracaoF360Quinzenal() {
        return integracaoF360Quinzenal;
    }

    public void setIntegracaoF360Quinzenal(Boolean integracaoF360Quinzenal) {
        this.integracaoF360Quinzenal = integracaoF360Quinzenal;
    }

    public Boolean isUsarConciliadora() {
        return usarConciliadora;
    }

    public void setUsarConciliadora(Boolean usarConciliadora) {
        this.usarConciliadora = usarConciliadora;
    }

    public String getEmpresaConciliadora() {
        return empresaConciliadora;
    }

    public void setEmpresaConciliadora(String empresaConciliadora) {
        this.empresaConciliadora = empresaConciliadora;
    }

    public String getSenhaConciliadora() {
        return senhaConciliadora;
    }

    public void setSenhaConciliadora(String senhaConciliadora) {
        this.senhaConciliadora = senhaConciliadora;
    }

    public Boolean isIntegracaoSpiviHabilitada() {
        return integracaoSpiviHabilitada;
    }

    public void setIntegracaoSpiviHabilitada(Boolean integracaoSpiviHabilitada) {
        this.integracaoSpiviHabilitada = integracaoSpiviHabilitada;
    }

    public String getIntegracaoSpiviSourceName() {
        return integracaoSpiviSourceName;
    }

    public void setIntegracaoSpiviSourceName(String integracaoSpiviSourceName) {
        this.integracaoSpiviSourceName = integracaoSpiviSourceName;
    }

    public Integer getIntegracaoSpiviSiteID() {
        return integracaoSpiviSiteID;
    }

    public void setIntegracaoSpiviSiteID(Integer integracaoSpiviSiteID) {
        this.integracaoSpiviSiteID = integracaoSpiviSiteID;
    }

    public String getIntegracaoSpiviPassword() {
        return integracaoSpiviPassword;
    }

    public void setIntegracaoSpiviPassword(String integracaoSpiviPassword) {
        this.integracaoSpiviPassword = integracaoSpiviPassword;
    }

    public Boolean isUsaIntegracoesCrm() {
        return usaIntegracoesCrm;
    }

    public void setUsaIntegracoesCrm(Boolean usaIntegracoesCrm) {
        this.usaIntegracoesCrm = usaIntegracoesCrm;
    }

    public Boolean getUsarGestaoCreditosPersonal() {
        if (usarGestaoCreditosPersonal == null) {
            usarGestaoCreditosPersonal = false;
        }
        return usarGestaoCreditosPersonal;
    }

    public void setUsarGestaoCreditosPersonal(Boolean usarGestaoCreditosPersonal) {
        this.usarGestaoCreditosPersonal = usarGestaoCreditosPersonal;
    }

    public Boolean isConsultarNovoCadastroSPC() {
        return consultarNovoCadastroSPC;
    }

    public void setConsultarNovoCadastroSPC(Boolean consultarNovoCadastroSPC) {
        this.consultarNovoCadastroSPC = consultarNovoCadastroSPC;
    }

    public Boolean isPesquisaAutomaticaSPC() {
        return pesquisaAutomaticaSPC;
    }

    public void setPesquisaAutomaticaSPC(Boolean pesquisaAutomaticaSPC) {
        this.pesquisaAutomaticaSPC = pesquisaAutomaticaSPC;
    }

    public Long getCodigoAssociadoSPC() {
        return codigoAssociadoSPC;
    }

    public void setCodigoAssociadoSPC(Long codigoAssociadoSPC) {
        this.codigoAssociadoSPC = codigoAssociadoSPC;
    }

    public String getOperadorSPC() {
        return operadorSPC;
    }

    public void setOperadorSPC(String operadorSPC) {
        this.operadorSPC = operadorSPC;
    }

    public String getSenhaSPC() {
        return senhaSPC;
    }

    public void setSenhaSPC(String senhaSPC) {
        this.senhaSPC = senhaSPC;
    }

    public Boolean isUsaVitio() {
        return usaVitio;
    }

    public void setUsaVitio(Boolean usaVitio) {
        this.usaVitio = usaVitio;
    }

    public String getLinkCheckoutVitio() {
        return linkCheckoutVitio;
    }

    public void setLinkCheckoutVitio(String linkCheckoutVitio) {
        this.linkCheckoutVitio = linkCheckoutVitio;
    }

    public String getLinkEbook() {
        return linkEbook;
    }

    public void setLinkEbook(String linkEbook) {
        this.linkEbook = linkEbook;
    }

    public String getMensagemVitioQuerComprar() {
        return mensagemVitioQuerComprar;
    }

    public void setMensagemVitioQuerComprar(String mensagemVitioQuerComprar) {
        this.mensagemVitioQuerComprar = mensagemVitioQuerComprar;
    }

    public String getMensagemVitioWpp() {
        return mensagemVitioWpp;
    }

    public void setMensagemVitioWpp(String mensagemVitioWhatsapp) {
        this.mensagemVitioWpp = mensagemVitioWhatsapp;
    }

    public Boolean getUtilizaIntegracaoDelsoft() {
        return utilizaIntegracaoDelsoft;
    }

    public void setUtilizaIntegracaoDelsoft(Boolean utilizaIntegracaoDelsoft) {
        this.utilizaIntegracaoDelsoft = utilizaIntegracaoDelsoft;
    }

    public String getHostIntegracaoDelsoft() {
        return hostIntegracaoDelsoft;
    }

    public void setHostIntegracaoDelsoft(String hostIntegracaoDelsoft) {
        this.hostIntegracaoDelsoft = hostIntegracaoDelsoft;
    }

    public Integer getPortaIntegracaoDelsoft() {
        return portaIntegracaoDelsoft;
    }

    public void setPortaIntegracaoDelsoft(Integer portaIntegracaoDelsoft) {
        this.portaIntegracaoDelsoft = portaIntegracaoDelsoft;
    }

    public String getTokenIntegracaoDelsoft() {
        return tokenIntegracaoDelsoft;
    }

    public void setTokenIntegracaoDelsoft(String tokenIntegracaoDelsoft) {
        this.tokenIntegracaoDelsoft = tokenIntegracaoDelsoft;
    }

    public String getNomeAplicacaoDelsoft() {
        return nomeAplicacaoDelsoft;
    }

    public void setNomeAplicacaoDelsoft(String nomeAplicacaoDelsoft) {
        this.nomeAplicacaoDelsoft = nomeAplicacaoDelsoft;
    }

    public String getUsuarioAplicacaoDelsoft() {
        return usuarioAplicacaoDelsoft;
    }

    public void setUsuarioAplicacaoDelsoft(String usuarioAplicacaoDelsoft) {
        this.usuarioAplicacaoDelsoft = usuarioAplicacaoDelsoft;
    }

    public String getSenhaAplicacaoDelsoft() {
        return senhaAplicacaoDelsoft;
    }

    public void setSenhaAplicacaoDelsoft(String senhaAplicacaoDelsoft) {
        this.senhaAplicacaoDelsoft = senhaAplicacaoDelsoft;
    }

    public Integer getPlanoAplicacaoDelsoft() {
        return planoAplicacaoDelsoft;
    }

    public void setPlanoAplicacaoDelsoft(Integer planoAplicacaoDelsoft) {
        this.planoAplicacaoDelsoft = planoAplicacaoDelsoft;
    }

    public Integer getCarenciaRenovacao() {
        return carenciaRenovacao;
    }

    public void setCarenciaRenovacao(Integer carenciaRenovacao) {
        this.carenciaRenovacao = carenciaRenovacao;
    }

    public Integer getTipoGestaoNfse() {
        return tipoGestaoNfse;
    }

    public void setTipoGestaoNfse(Integer tipoGestaoNfse) {
        this.tipoGestaoNfse = tipoGestaoNfse;
    }

    public Boolean getSomenteVendaProdutosComEstoque() {
        return somenteVendaProdutosComEstoque;
    }

    public void setSomenteVendaProdutosComEstoque(Boolean somenteVendaProdutosComEstoque) {
        this.somenteVendaProdutosComEstoque = somenteVendaProdutosComEstoque;
    }

    public Boolean isConcContasPagarFacilitePay() {
        if (concContasPagarFacilitePay == null) return false;
        return concContasPagarFacilitePay;
    }

    public void setConcContasPagarFacilitePay(Boolean concContasPagarFacilitePay) {
        this.concContasPagarFacilitePay = concContasPagarFacilitePay;
    }

    public boolean isConcContasReceberFacilitePay() {
        if (concContasReceberFacilitePay == null) return false;
        return concContasReceberFacilitePay;
    }

    public void setConcContasReceberFacilitePay(Boolean concContasReceberFacilitePay) {
        this.concContasReceberFacilitePay = concContasReceberFacilitePay;
    }

    public boolean isFacilitePayReguaCobranca() {
        return facilitePayReguaCobranca;
    }

    public void setFacilitePayReguaCobranca(boolean facilitePayReguaCobranca) {
        this.facilitePayReguaCobranca = facilitePayReguaCobranca;
    }

    public boolean isFacilitePayCDLSPC() {
        if (facilitePayCDLSPC == null) return false;
        return facilitePayCDLSPC;
    }

    public void setFacilitePayCDLSPC(Boolean facilitePayCDLSPC) {
        this.facilitePayCDLSPC = facilitePayCDLSPC;
    }

    public Double getValorMetaFacilitePay() {
        return valorMetaFacilitePay;
    }

    public void setValorMetaFacilitePay(Double valorMetaFacilitePay) {
        this.valorMetaFacilitePay = valorMetaFacilitePay;
    }

    public boolean isFacilitePayConciliacaoCartao() {
        return facilitePayConciliacaoCartao;
    }

    public void setFacilitePayConciliacaoCartao(boolean facilitePayConciliacaoCartao) {
        this.facilitePayConciliacaoCartao = facilitePayConciliacaoCartao;
    }

    public Boolean getFacilitePayReguaCobranca() {
        if (facilitePayReguaCobranca == null) return false;
        return facilitePayReguaCobranca;
    }

    public void setFacilitePayReguaCobranca(Boolean facilitePayReguaCobranca) {
        this.facilitePayReguaCobranca = facilitePayReguaCobranca;
    }

    public boolean isFacilitePayReguaCobrancaEmail() {
        if (facilitePayReguaCobranca == null) return false;
        return facilitePayReguaCobrancaEmail;
    }

    public void setFacilitePayReguaCobrancaEmail(Boolean facilitePayReguaCobrancaEmail) {
        this.facilitePayReguaCobrancaEmail = facilitePayReguaCobrancaEmail;
    }

    public boolean isFacilitePayReguaCobrancaSms() {
        if (facilitePayReguaCobranca == null) return false;
        return facilitePayReguaCobrancaSms;
    }

    public void setFacilitePayReguaCobrancaSms(Boolean facilitePayReguaCobrancaSms) {
        this.facilitePayReguaCobrancaSms = facilitePayReguaCobrancaSms;
    }

    public boolean isFacilitePayReguaCobrancaApp() {
        if (facilitePayReguaCobrancaApp == null) return false;
        return facilitePayReguaCobrancaApp;
    }

    public void setFacilitePayReguaCobrancaApp(Boolean facilitePayReguaCobrancaApp) {
        this.facilitePayReguaCobrancaApp = facilitePayReguaCobrancaApp;
    }

    public boolean isFacilitePayReguaCobrancaWhatsApp() {
        if (facilitePayReguaCobrancaWhatsApp == null) return false;
        return facilitePayReguaCobrancaWhatsApp;
    }

    public void setFacilitePayReguaCobrancaWhatsApp(Boolean facilitePayReguaCobrancaWhatsApp) {
        this.facilitePayReguaCobrancaWhatsApp = facilitePayReguaCobrancaWhatsApp;
    }

    public Boolean isFacilitePayReguaCobrancaGymbotPro() {
        if (facilitePayReguaCobrancaGymbotPro == null) return false;
        return facilitePayReguaCobrancaGymbotPro;
    }

    public void setFacilitePayReguaCobrancaGymbotPro(Boolean facilitePayReguaCobrancaGymbotPro) {
        this.facilitePayReguaCobrancaGymbotPro = facilitePayReguaCobrancaGymbotPro;
    }

    public int getQtdLmtContasConcFacilitePay() {
        return qtdLmtContasConcFacilitePay;
    }

    public void setQtdLmtContasConcFacilitePay(int qtdLmtContasConcFacilitePay) {
        this.qtdLmtContasConcFacilitePay = qtdLmtContasConcFacilitePay;
    }

    public Integer getArredondamento() {
        return arredondamento;
    }

    public void setArredondamento(Integer arredondamento) {
        this.arredondamento = arredondamento;
    }

    public Boolean getUtilizaGestaoClientesComRestricoes() {
        return utilizaGestaoClientesComRestricoes;
    }

    public void setUtilizaGestaoClientesComRestricoes(Boolean utilizaGestaoClientesComRestricoes) {
        this.utilizaGestaoClientesComRestricoes = utilizaGestaoClientesComRestricoes;
    }

    public boolean isHabilitarCadastroEmpresaSesi() {
        if (habilitarCadastroEmpresaSesi == null) return false;
        return habilitarCadastroEmpresaSesi;
    }

    public void setHabilitarCadastroEmpresaSesi(Boolean habilitarCadastroEmpresaSesi) {
        this.habilitarCadastroEmpresaSesi = habilitarCadastroEmpresaSesi;
    }

    public String getIntegracaoNuvemshopNomeApp() {
        return integracaoNuvemshopNomeApp;
    }

    public void setIntegracaoNuvemshopNomeApp(String integracaoNuvemshopNomeApp) {
        this.integracaoNuvemshopNomeApp = integracaoNuvemshopNomeApp;
    }

    public String getIntegracaoNuvemshopEmail() {
        return integracaoNuvemshopEmail;
    }

    public void setIntegracaoNuvemshopEmail(String integracaoNuvemshopEmail) {
        this.integracaoNuvemshopEmail = integracaoNuvemshopEmail;
    }

    public String getIntegracaoNuvemshopStoreId() {
        return integracaoNuvemshopStoreId;
    }

    public void setIntegracaoNuvemshopStoreId(String integracaoNuvemshopStoreId) {
        this.integracaoNuvemshopStoreId = integracaoNuvemshopStoreId;
    }

    public String getIntegracaoNuvemshopTokenAcesso() {
        return integracaoNuvemshopTokenAcesso;
    }

    public void setIntegracaoNuvemshopTokenAcesso(String integracaoNuvemshopTokenAcesso) {
        this.integracaoNuvemshopTokenAcesso = integracaoNuvemshopTokenAcesso;
    }

    public boolean isIntegracaoNuvemshopHabilitada() {
        if (integracaoNuvemshopHabilitada == null) return false;
        return integracaoNuvemshopHabilitada;
    }

    public void setIntegracaoNuvemshopHabilitada(Boolean integracaoNuvemshopHabilitada) {
        this.integracaoNuvemshopHabilitada = integracaoNuvemshopHabilitada;
    }

    public boolean isPermiteContratosConcomintante() {
        if (permiteContratosConcomintante == null) return false;
        return permiteContratosConcomintante;
    }

    public void setPermiteContratosConcomintante(Boolean permiteContratosConcomintante) {
        this.permiteContratosConcomintante = permiteContratosConcomintante;
    }

    public Boolean getPontuarApenasCampanhasAtivas() {
        return pontuarApenasCampanhasAtivas;
    }

    public void setPontuarApenasCampanhasAtivas(Boolean pontuarapenascampanhasativas) {
        this.pontuarApenasCampanhasAtivas = pontuarapenascampanhasativas;
    }

    public Boolean getUtilizarPactoPrint() {
        return utilizarPactoPrint;
    }

    public void setUtilizarPactoPrint(Boolean utilizarPactoPrint) {
        this.utilizarPactoPrint = utilizarPactoPrint;
    }


    public Questionario getQuestionarioPrimeiraCompra() {
        return questionarioPrimeiraCompra;
    }

    public void setQuestionarioPrimeiraCompra(Questionario questionarioPrimeiraCompra) {
        this.questionarioPrimeiraCompra = questionarioPrimeiraCompra;
    }

    public Questionario getQuestionarioPrimeiraVisita() {
        return questionarioPrimeiraVisita;
    }

    public void setQuestionarioPrimeiraVisita(Questionario questionarioPrimeiraVisita) {
        this.questionarioPrimeiraVisita = questionarioPrimeiraVisita;
    }

    public Questionario getQuestionarioRematricula() {
        return questionarioRematricula;
    }

    public void setQuestionarioRematricula(Questionario questionarioRematricula) {
        this.questionarioRematricula = questionarioRematricula;
    }

    public Questionario getQuestionarioRetorno() {
        return questionarioRetorno;
    }

    public void setQuestionarioRetorno(Questionario questionarioRetorno) {
        this.questionarioRetorno = questionarioRetorno;
    }

    public Questionario getQuestionarioRetornoCompra() {
        return questionarioRetornoCompra;
    }

    public void setQuestionarioRetornoCompra(Questionario questionarioRetornoCompra) {
        this.questionarioRetornoCompra = questionarioRetornoCompra;
    }

    public Integer getNrDiasVigenteQuestionarioRematricula() {
        return nrDiasVigenteQuestionarioRematricula;
    }

    public void setNrDiasVigenteQuestionarioRematricula(Integer nrDiasVigenteQuestionarioRematricula) {
        this.nrDiasVigenteQuestionarioRematricula = nrDiasVigenteQuestionarioRematricula;
    }

    public Integer getNrDiasVigenteQuestionarioRetorno() {
        return nrDiasVigenteQuestionarioRetorno;
    }

    public void setNrDiasVigenteQuestionarioRetorno(Integer nrDiasVigenteQuestionarioRetorno) {
        this.nrDiasVigenteQuestionarioRetorno = nrDiasVigenteQuestionarioRetorno;
    }

    public long getNrDiasVigenteQuestionarioRetornoCompra() {
        return nrDiasVigenteQuestionarioRetornoCompra;
    }

    public void setNrDiasVigenteQuestionarioRetornoCompra(long nrDiasVigenteQuestionarioRetornoCompra) {
        this.nrDiasVigenteQuestionarioRetornoCompra = nrDiasVigenteQuestionarioRetornoCompra;
    }

    public Boolean getBvObrigatorio() {
        return bvObrigatorio;
    }

    public void setBvObrigatorio(Boolean bvObrigatorio) {
        this.bvObrigatorio = bvObrigatorio;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getIntegracaoManyChatHabilitada() {
        return integracaoManyChatHabilitada;
    }

    public void setIntegracaoManyChatHabilitada(Boolean integracaoManyChatHabilitada) {
        this.integracaoManyChatHabilitada = integracaoManyChatHabilitada;
    }

    public String getIntegracaoManyChatTokenApi() {
        return integracaoManyChatTokenApi;
    }

    public void setIntegracaoManyChatTokenApi(String integracaoManyChatTokenApi) {
        this.integracaoManyChatTokenApi = integracaoManyChatTokenApi;
    }

    public String getIntegracaoManyChatTagUnidade() {
        return integracaoManyChatTagUnidade;
    }

    public void setIntegracaoManyChatTagUnidade(String integracaoManyChatTagUnidade) {
        this.integracaoManyChatTagUnidade = integracaoManyChatTagUnidade;
    }

    public Boolean getUsarSescDf() {
        return usarSescDf;
    }

    public void setUsarSescDf(Boolean usarSescDf) {
        this.usarSescDf = usarSescDf;
    }

    public String getTokenSescDf() {
        return tokenSescDf;
    }

    public Boolean getUtilizaConfigCancelamentoSesc() {
        return utilizaConfigCancelamentoSesc;
    }

    public void setUtilizaConfigCancelamentoSesc(Boolean utilizaConfigCancelamentoSesc) {
        this.utilizaConfigCancelamentoSesc = utilizaConfigCancelamentoSesc;
    }

    public void setTokenSescDf(String tokenSescDf) {
        this.tokenSescDf = tokenSescDf;
    }

    public Boolean getHorariocapacidadeporcategoria() {
        return horariocapacidadeporcategoria;
    }

    public void setHorariocapacidadeporcategoria(Boolean horariocapacidadeporcategoria) {
        this.horariocapacidadeporcategoria = horariocapacidadeporcategoria;
    }

    public Double getValorLimiteCaixaAbertoVendaAvulsa() {
        if (valorLimiteCaixaAbertoVendaAvulsa == null) {
            valorLimiteCaixaAbertoVendaAvulsa = 0.0;
        }
        return valorLimiteCaixaAbertoVendaAvulsa;
    }

    public void setValorLimiteCaixaAbertoVendaAvulsa(Double valorLimiteCaixaAbertoVendaAvulsa) {
        this.valorLimiteCaixaAbertoVendaAvulsa = valorLimiteCaixaAbertoVendaAvulsa;
    }
}
