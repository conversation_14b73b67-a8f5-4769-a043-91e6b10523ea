package com.pacto.adm.core.entities;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.*;

@Entity
@Schema(name = "Categoria", description = "Informações da categoria")
public class Categoria {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código único identificador da categoria", example = "3")
    private Integer codigo;

    @Schema(description = "Nome da categoria", example = "Experiência Pacto")
    private String nome;

    @Schema(description = "Tipo da categoria", example = "AL")
    private String tipoCategoria;

    @Schema(description = "Quantidade de convites permitidos para a categoria", example = "100")
    private Integer nrconvitepermitido;

    @Schema(description = "Código do tipo da categoria no clube", example = "1")
    private Integer tipocategoriaclube;

    @Schema(description = "Código do tipo de bloqueio no caso de inadimplência", example = "1")
    private Integer tipobloqueioinadimplencia;

    @Schema(description = "Código do produto padrão da categoria", example = "1")
    private Integer produtopadrao;

    @Schema(description = "Nome externo da categoria", example = "Experiência Pacto")
    private String nomeexterno;

    public Categoria() {
    }

    public Categoria(Integer codigo, String nome, String tipoCategoria, Integer nrconvitepermitido, Integer tipocategoriaclube, Integer tipobloqueioinadimplencia, Integer produtopadrao, String nomeexterno ) {
        this.codigo = codigo;
        this.nome = nome;
        this.tipoCategoria = tipoCategoria;
        this.nrconvitepermitido = nrconvitepermitido;
        this.tipocategoriaclube = tipocategoriaclube;
        this.tipobloqueioinadimplencia = tipobloqueioinadimplencia;
        this.produtopadrao = produtopadrao;
        this.nomeexterno = nomeexterno;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTipoCategoria() {
        return tipoCategoria;
    }

    public void setTipoCategoria(String tipoCategoria) {
        this.tipoCategoria = tipoCategoria;
    }

    public Integer getNrconvitepermitido() {
        return nrconvitepermitido;
    }

    public void setNrconvitepermitido(Integer nrconvitepermitido) {
        this.nrconvitepermitido = nrconvitepermitido;
    }

    public Integer getTipocategoriaclube() {
        return tipocategoriaclube;
    }

    public void setTipocategoriaclube(Integer tipocategoriaclube) {
        this.tipocategoriaclube = tipocategoriaclube;
    }

    public Integer getTipobloqueioinadimplencia() {
        return tipobloqueioinadimplencia;
    }

    public void setTipobloqueioinadimplencia(Integer tipobloqueioinadimplencia) {
        this.tipobloqueioinadimplencia = tipobloqueioinadimplencia;
    }

    public Integer getProdutopadrao() {
        return produtopadrao;
    }

    public void setProdutopadrao(Integer produtopadrao) {
        this.produtopadrao = produtopadrao;
    }

    public String getNomeexterno() {
        return nomeexterno;
    }

    public void setNomeexterno(String nomeexterno) {
        this.nomeexterno = nomeexterno;
    }
}
