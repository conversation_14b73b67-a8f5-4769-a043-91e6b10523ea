package com.pacto.adm.core.entities;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
public class RespostaPergunta {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricaorespota;
    @ManyToOne
    @JoinColumn(name = "pergunta", foreignKey = @ForeignKey(name = "fk_respostapergunta_pergunta"))
    private Pergunta pergunta;
    private Integer nrQuestao;

    public RespostaPergunta() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricaorespota() {
        return descricaorespota;
    }

    public void setDescricaorespota(String descricaorespota) {
        this.descricaorespota = descricaorespota;
    }

    public Pergunta getPergunta() {
        return pergunta;
    }

    public void setPergunta(Pergunta pergunta) {
        this.pergunta = pergunta;
    }

    public Integer getNrQuestao() {
        return nrQuestao;
    }

    public void setNrQuestao(Integer nrQuestao) {
        this.nrQuestao = nrQuestao;
    }
}

