package com.pacto.adm.core.entities.financeiro;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.MovParcela;

import javax.persistence.*;

@Entity
public class RemessaItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer tipo;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "movParcela", foreignKey = @ForeignKey(name = "fk_remessaitem_movparcela"))
    private MovParcela movParcela;

    @ManyToOne
    @RelationalField
    @JoinColumn(name = "remessa", foreignKey = @ForeignKey(name = "fk_remessaitem_remessa"))
    private Remessa remessa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public MovParcela getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcela movParcela) {
        this.movParcela = movParcela;
    }

    public Remessa getRemessa() {
        return remessa;
    }

    public void setRemessa(Remessa remessa) {
        this.remessa = remessa;
    }
}
