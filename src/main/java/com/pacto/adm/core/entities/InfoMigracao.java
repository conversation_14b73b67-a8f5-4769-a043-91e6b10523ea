package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
public class InfoMigracao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer tipoInfo;
    private String info;
    private String origem;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario usuario;

    public InfoMigracao() {

    }
    public InfoMigracao(Integer tipoInfo, Integer usuario, String info, String origem) {
        this.tipoInfo = tipoInfo;
        this.info = info;
        this.origem = origem != null ? origem : "";
        this.usuario = new Usuario();
        this.usuario.setCodigo(usuario);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTipoInfo() {
        return tipoInfo;
    }

    public void setTipoInfo(Integer tipoInfo) {
        this.tipoInfo = tipoInfo;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }
}
