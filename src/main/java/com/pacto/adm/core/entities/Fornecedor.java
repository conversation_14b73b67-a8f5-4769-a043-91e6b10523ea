package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "fornecedor")
@Schema(name = "Fornecedor", description = "Informações sobre fornecedores, incluindo dados pessoais e validade.")
public class Fornecedor {

    @Schema(description = "Código único identificador do fornecedor.", example = "33")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Schema(description = "Data de validade do cadastro do fornecedor.", example = "2025-12-31T23:59:59Z")
    private Date dataValidade;

    @Schema(description = "Informações pessoais do fornecedor.")
    @RelationalField
    @OneToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_fornecedor_pessoa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }
}
