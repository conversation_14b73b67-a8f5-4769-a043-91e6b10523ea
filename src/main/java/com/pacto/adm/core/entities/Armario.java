package com.pacto.adm.core.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@Entity
public class Armario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private String descricao;

    @Column(name = "tamanhoarmario")
    private Integer tamanhoArmario;

    private Integer empresa;

    @Column(name = "responsavelcadastro")
    private Integer responsavelCadastro;

    @Column(name = "datacadastro")
    private Date dataCadastro;

    private Integer status;
    private Integer numeracao;

    @Column(name = "aluguelatual")
    private Integer aluguelAtual;

    private String grupo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getTamanhoArmario() {
        return tamanhoArmario;
    }

    public void setTamanhoArmario(Integer tamanhoArmario) {
        this.tamanhoArmario = tamanhoArmario;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getResponsavelCadastro() {
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(Integer responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getNumeracao() {
        return numeracao;
    }

    public void setNumeracao(Integer numeracao) {
        this.numeracao = numeracao;
    }

    public Integer getAluguelAtual() {
        return aluguelAtual;
    }

    public void setAluguelAtual(Integer aluguelAtual) {
        this.aluguelAtual = aluguelAtual;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }
}
