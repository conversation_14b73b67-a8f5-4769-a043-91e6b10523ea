package com.pacto.adm.core.entities.solicitacaocompra;

import com.pacto.adm.core.entities.Arquivo;
import com.pacto.adm.core.enumerador.SituacaoSolicitacaoCompraEnum;
import com.pacto.config.annotations.NomeEntidadeLog;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import java.util.Date;

@Entity
@Getter
@Setter
@NomeEntidadeLog("SolicitacaoCompra")
public class SolicitacaoCompra implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String titulo;
    @Column(name = "data_solicitacao")
    private Date dataSolicitacao;
    @Enumerated(EnumType.STRING)
    private SituacaoSolicitacaoCompraEnum situacao;
    private String descricao;
    @Column(name = "motivo_negacao")
    private String motivoNegacao;

    @Override
    public SolicitacaoCompra clone() {
        try {
            SolicitacaoCompra clone = (SolicitacaoCompra) super.clone();
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
