package com.pacto.adm.core.entities;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
@Schema(name = "Permiss<PERSON>", description = "Detalhes das permissões de acesso por perfil.")
public class Permissao {

    @Schema(description = "Código do perfil de acesso vinculado à permissão.", example = "101")
    protected Integer codPerfilAcesso;

    @Schema(description = "Nome da entidade à qual a permissão se refere.", example = "Contrato")
    protected String nomeEntidade;

    @Schema(description = "Permissões atribuídas à entidade (ex: CONSULTAR, EDITAR).", example = "CONSULTAR,EDITAR")
    protected String permissoes;

    @Schema(description = "Título de apresentação da permissão no sistema.", example = "Permissão")
    protected String tituloApresentacao;

    @Schema(description = "Código do tipo da permissão.", example = "1")
    protected Integer tipoPermissao;

    @Schema(description = "Valor específico para permissão.", example = "Aluno")
    protected String valorEspecifico;

    @Schema(description = "Valor inicial do intervalo permitido (quando aplicável).", example = "1000")
    protected String valorInicial;

    @Schema(description = "Valor final do intervalo permitido (quando aplicável).", example = "2000")
    protected String valorFinal;

    @Id
    @Schema(description = "Identificador único da permissão.", example = "501")
    private Integer id;

    public Integer getCodPerfilAcesso() {
        return codPerfilAcesso;
    }

    public void setCodPerfilAcesso(Integer codPerfilAcesso) {
        this.codPerfilAcesso = codPerfilAcesso;
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public String getPermissoes() {
        return permissoes;
    }

    public void setPermissoes(String permissoes) {
        this.permissoes = permissoes;
    }

    public String getTituloApresentacao() {
        return tituloApresentacao;
    }

    public void setTituloApresentacao(String tituloApresentacao) {
        this.tituloApresentacao = tituloApresentacao;
    }

    public Integer getTipoPermissao() {
        return tipoPermissao;
    }

    public void setTipoPermissao(Integer tipoPermissao) {
        this.tipoPermissao = tipoPermissao;
    }

    public String getValorEspecifico() {
        return valorEspecifico;
    }

    public void setValorEspecifico(String valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public String getValorInicial() {
        return valorInicial;
    }

    public void setValorInicial(String valorInicial) {
        this.valorInicial = valorInicial;
    }

    public String getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(String valorFinal) {
        this.valorFinal = valorFinal;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
