package com.pacto.adm.core.entities.pluggyItem;

import com.pacto.config.annotations.NomeEntidadeLog;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;


@Entity
@NomeEntidadeLog("PluggyItem")
public class PluggyItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer empresa;
    private String id;
    private String dadosRetorno;
    private Boolean ativo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDadosRetorno() {
        return dadosRetorno;
    }

    public void setDadosRetorno(String dadosRetorno) {
        this.dadosRetorno = dadosRetorno;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
