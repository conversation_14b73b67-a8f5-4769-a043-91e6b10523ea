package com.pacto.adm.core.entities.contrato;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

@Entity
public class Turma {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;

    @ManyToOne
    @JoinColumn(name = "modalidade", foreignKey = @ForeignKey(name = "fk_turma_modalidade"))
    private Modalidade modalidade;

    @Transient
    private Boolean turmaEscolhida;

    public Turma() {
    }

    public Turma(Integer codigo, String descricao, Boolean turmaEscolhida) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.turmaEscolhida = turmaEscolhida;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getTurmaEscolhida() {
        return turmaEscolhida;
    }

    public void setTurmaEscolhida(Boolean turmaEscolhida) {
        this.turmaEscolhida = turmaEscolhida;
    }

    public Modalidade getModalidade() {
        return modalidade;
    }

    public void setModalidade(Modalidade modalidade) {
        this.modalidade = modalidade;
    }
}
