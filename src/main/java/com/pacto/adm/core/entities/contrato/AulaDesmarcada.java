package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.operacaocoletiva.OperacaoColetiva;
import com.pacto.adm.core.entities.reposicao.Reposicao;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import com.pacto.adm.core.enumerador.origemsistema.OrigemSistemaEnumConverter;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.util.Date;

@Entity
public class AulaDesmarcada {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean permiteReporAulaDesmarcada;
    private Date dataLancamento;
    private Date dataOrigem;
    private Date dataReposicao;

    @Convert(converter = OrigemSistemaEnumConverter.class)
    @Column(name = "origemsitems", columnDefinition = "DEFAULT 1")
    private OrigemSistemaEnum origemSistema;
    private Boolean desmarcadaPorAfastamento;

    private String justificativa;


    @ManyToOne
    @JoinColumn(name = "reposicao", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Reposicao reposicao;
    @ManyToOne
    @JoinColumn(name = "operacaocoletiva", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OperacaoColetiva operacaoColetiva;
    @ManyToOne
    @JoinColumn(name = "turmadestino", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Turma turmaDestino;

    @ManyToOne
    @JoinColumn(name = "contratoanterior", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Contrato contratoAnterior;

    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "fk_auladesmarcada_contrato"), nullable = false)
    private Contrato contrato;
    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_auladesmarcada_cliente"), nullable = false)
    private Cliente cliente;
    @ManyToOne
    @JoinColumn(name = "turma", foreignKey = @ForeignKey(name = "fk_auladesmarcada_turma"), nullable = false)
    private Turma turma;
    @ManyToOne
    @JoinColumn(name = "horarioturma", foreignKey = @ForeignKey(name = "fk_auladesmarcada_horarioturma"), nullable = false)
    private HorarioTurma horarioTurma;
    @ManyToOne
    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(name = "fk_auladesmarcada_usuario"))
    private Usuario usuario;
    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_auladesmarcada_empresa"), nullable = false)
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getPermiteReporAulaDesmarcada() {
        return permiteReporAulaDesmarcada;
    }

    public void setPermiteReporAulaDesmarcada(Boolean permiteReporAulaDesmarcada) {
        this.permiteReporAulaDesmarcada = permiteReporAulaDesmarcada;
    }

    public Reposicao getReposicao() {
        return reposicao;
    }

    public void setReposicao(Reposicao reposicao) {
        this.reposicao = reposicao;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Turma getTurma() {
        return turma;
    }

    public void setTurma(Turma turma) {
        this.turma = turma;
    }

    public HorarioTurma getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurma horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataOrigem() {
        return dataOrigem;
    }

    public void setDataOrigem(Date dataOrigem) {
        this.dataOrigem = dataOrigem;
    }

    public Date getDataReposicao() {
        return dataReposicao;
    }

    public void setDataReposicao(Date dataReposicao) {
        this.dataReposicao = dataReposicao;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Boolean getDesmarcadaPorAfastamento() {
        return desmarcadaPorAfastamento;
    }

    public void setDesmarcadaPorAfastamento(Boolean desmarcadaPorAfastamento) {
        this.desmarcadaPorAfastamento = desmarcadaPorAfastamento;
    }

    public Contrato getContratoAnterior() {
        return contratoAnterior;
    }

    public void setContratoAnterior(Contrato contratoAnterior) {
        this.contratoAnterior = contratoAnterior;
    }

    public Turma getTurmaDestino() {
        return turmaDestino;
    }

    public void setTurmaDestino(Turma turmaDestino) {
        this.turmaDestino = turmaDestino;
    }

    public OperacaoColetiva getOperacaoColetiva() {
        return operacaoColetiva;
    }

    public void setOperacaoColetiva(OperacaoColetiva operacaoColetiva) {
        this.operacaoColetiva = operacaoColetiva;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
