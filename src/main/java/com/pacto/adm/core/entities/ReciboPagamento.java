package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
public class ReciboPagamento {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private BigDecimal valorTotal;
    private String nomePessoaPagador;
    private Date data;

    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Empresa empresa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "pessoapagador", foreignKey = @ForeignKey(name = "fk_recibopagamento_pessoa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoaPagador;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavellancamento", foreignKey = @ForeignKey(name = "fk_recibopagamento_usuario"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelLancamento;

    @RelationalField
    @OneToMany
    @JoinColumn(name = "recibopagamento", foreignKey = @ForeignKey(name = "fk_movpagamento_recibopagamento"))
    @NotFound(action = NotFoundAction.IGNORE)
    private List<MovPagamento> pagamentos;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getNomePessoaPagador() {
        return nomePessoaPagador;
    }

    public void setNomePessoaPagador(String nomePessoaPagador) {
        this.nomePessoaPagador = nomePessoaPagador;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Pessoa getPessoaPagador() {
        return pessoaPagador;
    }

    public void setPessoaPagador(Pessoa pessoa) {
        this.pessoaPagador = pessoa;
    }

    public Usuario getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Usuario responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public List<MovPagamento> getPagamentos() {
        return pagamentos;
    }

    public void setPagamentos(List<MovPagamento> pagamentos) {
        this.pagamentos = pagamentos;
    }
}
