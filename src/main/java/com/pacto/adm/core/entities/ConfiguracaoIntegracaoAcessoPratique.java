package com.pacto.adm.core.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class ConfiguracaoIntegracaoAcessoPratique {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean habilitada;
    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "configuracaointegracaofoguete_empresa_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getHabilitada() {
        return habilitada;
    }

    public void setHabilitada(Boolean habilitada) {
        this.habilitada = habilitada;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
