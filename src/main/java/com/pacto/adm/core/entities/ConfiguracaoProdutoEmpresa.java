package com.pacto.adm.core.entities;

import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class ConfiguracaoProdutoEmpresa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "produto", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Produto produto;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_configuracaoprodutoempresa_empresa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Empresa empresa;
    private Double valor;
    private Boolean desocio;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Boolean getDesocio() {
        return desocio;
    }

    public void setDesocio(Boolean desocio) {
        this.desocio = desocio;
    }
}

