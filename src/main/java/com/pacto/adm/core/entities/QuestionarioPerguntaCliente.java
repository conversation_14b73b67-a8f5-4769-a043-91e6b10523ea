package com.pacto.adm.core.entities;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
public class QuestionarioPerguntaCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer questionarioCliente;

    @ManyToOne(cascade = { CascadeType.PERSIST, CascadeType.MERGE })
    @JoinColumn(name = "perguntacliente")
    @NotFound(action = NotFoundAction.IGNORE)
    private PerguntaCliente perguntaCliente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getQuestionarioCliente() {
        return questionarioCliente;
    }

    public void setQuestionarioCliente(Integer questionarioCliente) {
        this.questionarioCliente = questionarioCliente;
    }

    public PerguntaCliente getPerguntaCliente() {
        return perguntaCliente;
    }

    public void setPerguntaCliente(PerguntaCliente perguntaCliente) {
        this.perguntaCliente = perguntaCliente;
    }
}
