package com.pacto.adm.core.entities;


import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.*;

@Entity
@Schema(name = "Classificação", description = "Informações da classificação")
public class Classificacao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código único identificador da classificação", example = "1")
    private Integer codigo;
    @Schema(description = "Nome da classificação", example = "Classificação da Pacto")
    private String  nome;
    @Schema(description = "Indica se deve enviar SMS automaticamente", example = "true")
    private Boolean enviarsmsautomatico;

    public Classificacao() {
    }

    public Classificacao(Integer codigo, String nome, Boolean enviarsmsautomatico) {
        this.codigo = codigo;
        this.nome = nome;
        this.enviarsmsautomatico = enviarsmsautomatico;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getEnviarsmsautomatico() {
        return enviarsmsautomatico;
    }

    public void setEnviarsmsautomatico(Boolean enviarsmsautomatico) {
        this.enviarsmsautomatico = enviarsmsautomatico;
    }
}
