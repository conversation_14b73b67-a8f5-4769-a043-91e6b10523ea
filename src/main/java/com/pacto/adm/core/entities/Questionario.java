package com.pacto.adm.core.entities;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import java.util.Set;

@Entity
public class Questionario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nomeInterno;
    private String tipoQuestionario;
    private String fundoCor;
    private String fundoImagem;
    private String textoInicio;
    private String textoFim;
    private Boolean somenteUmaResposta;
    private Boolean ativo;
    private String tituloPesquisa;

    @OneToMany(mappedBy = "questionario")
    private Set<QuestionarioPergunta> questionarioPerguntas;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeInterno() {
        return nomeInterno;
    }

    public void setNomeInterno(String nomeInterno) {
        this.nomeInterno = nomeInterno;
    }

    public String getTipoQuestionario() {
        return tipoQuestionario;
    }

    public void setTipoQuestionario(String tipoQuestionario) {
        this.tipoQuestionario = tipoQuestionario;
    }

    public String getFundoCor() {
        return fundoCor;
    }

    public void setFundoCor(String fundoCor) {
        this.fundoCor = fundoCor;
    }

    public String getFundoImagem() {
        return fundoImagem;
    }

    public void setFundoImagem(String fundoImagem) {
        this.fundoImagem = fundoImagem;
    }

    public String getTextoInicio() {
        return textoInicio;
    }

    public void setTextoInicio(String textoInicio) {
        this.textoInicio = textoInicio;
    }

    public String getTextoFim() {
        return textoFim;
    }

    public void setTextoFim(String textoFim) {
        this.textoFim = textoFim;
    }

    public Boolean getSomenteUmaResposta() {
        return somenteUmaResposta;
    }

    public void setSomenteUmaResposta(Boolean somenteUmaResposta) {
        this.somenteUmaResposta = somenteUmaResposta;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getTituloPesquisa() {
        return tituloPesquisa;
    }

    public void setTituloPesquisa(String tituloPesquisa) {
        this.tituloPesquisa = tituloPesquisa;
    }

    public Set<QuestionarioPergunta> getQuestionarioPerguntas() {
        return questionarioPerguntas;
    }

    public void setQuestionarioPerguntas(Set<QuestionarioPergunta> questionarioPerguntas) {
        this.questionarioPerguntas = questionarioPerguntas;
    }
}
