package com.pacto.adm.core.entities.contrato;

import javax.persistence.*;

@Entity
public class ContratoModalidadeHorarioTurma {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Double percOcupacao;
    private Double percDesconto;

    @ManyToOne
    @JoinColumn(name = "horarioTurma", foreignKey = @ForeignKey(name = "fk_contratomodalidadehorarioturma_horarioturma"))
    private HorarioTurma horarioTurma;

    @ManyToOne
    @JoinColumn(name = "contratoModalidadeTurma", foreignKey = @ForeignKey(name = "fk_contratomodalidadehorarioturma_contratomodalidadeturma"))
    private ContratoModalidadeTurma contratoModalidadeTurma;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getPercOcupacao() {
        return percOcupacao;
    }

    public void setPercOcupacao(Double percOcupacao) {
        this.percOcupacao = percOcupacao;
    }

    public Double getPercDesconto() {
        return percDesconto;
    }

    public void setPercDesconto(Double percDesconto) {
        this.percDesconto = percDesconto;
    }

    public HorarioTurma getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurma horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public ContratoModalidadeTurma getContratoModalidadeTurma() {
        return contratoModalidadeTurma;
    }

    public void setContratoModalidadeTurma(ContratoModalidadeTurma contratoModalidadeTurma) {
        this.contratoModalidadeTurma = contratoModalidadeTurma;
    }
}
