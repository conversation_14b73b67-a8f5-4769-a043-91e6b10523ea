package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.Categoria;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class PlanoCategoria {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer codigo;
    @ManyToOne
    @JoinColumn(name = "plano", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Plano plano;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "categoria")
    @NotFound(action = NotFoundAction.IGNORE)
    private Categoria categoria;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Plano getPlano() {
        return plano;
    }

    public void setPlano(Plano plano) {
        this.plano = plano;
    }

    public Categoria getCategoria() {
        return categoria;
    }

    public void setCategoria(Categoria categoria) {
        this.categoria = categoria;
    }
}
