package com.pacto.adm.core.entities;

import com.pacto.adm.core.entities.empresa.Empresa;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 *
 * User: HEBERT FREDERICK
 * Date: 19/06/2023
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class ConfigTotalPass implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private Integer empresa_codigo;
    private String nome;
    private String codigoTotalPass;
    @ManyToOne
    private Usuario usuarioLancou;
    private Boolean inativo;
    private Boolean permitirWod;
    private Date dataLancamento;
    private Integer limiteDeAcessosPorDia;
    private Integer limiteDeAulasPorDia;
    private String apikey;

}
