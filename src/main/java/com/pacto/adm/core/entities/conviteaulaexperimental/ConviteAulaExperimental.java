package com.pacto.adm.core.entities.conviteaulaexperimental;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.indicado.Indicado;
import com.pacto.adm.core.entities.passivo.Passivo;
import com.pacto.adm.core.entities.tipoconviteaulaexperimental.TipoConviteAulaExperimental;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "conviteaulaexperimental")
public class ConviteAulaExperimental {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dataLancamento;
    private Date dataValidacaoConvidado;

    @ManyToOne
    @JoinColumn(name = "clienteconvidado", foreignKey = @ForeignKey(name = "conviteaulaexperimental_clienteconvite_fk"))
    private Cliente clienteConvidado;

    @ManyToOne
    @JoinColumn(name = "clienteconvidou", foreignKey = @ForeignKey(name = "conviteaulaexperimental_clienteconvidou_fk"))
    private Cliente clienteConvidou;

    @ManyToOne
    @JoinColumn(name = "clienteindicadooupassivo", foreignKey = @ForeignKey(name = ""))
    private Cliente clienteIndicadoOuPassivo;

    @ManyToOne
    @JoinColumn(name = "colaboradorresponsavelconvite", foreignKey = @ForeignKey(name = "conviteaulaexperimental_colaborador_fk"))
    private Colaborador colaboradorResponsavelConvite;

    @ManyToOne
    @JoinColumn(name = "indicadoconvidado", foreignKey = @ForeignKey(name = "conviteaulaexperimental_indicado_fk"))
    private Indicado indicadoConvidado;

    @ManyToOne
    @JoinColumn(name = "passivoconvidado", foreignKey = @ForeignKey(name = "conviteaulaexperimental_passivo_fk"))
    private Passivo passivoConvidado;

    @ManyToOne
    @JoinColumn(name = "tipoconviteaulaexperimental", foreignKey = @ForeignKey(name = "conviteaulaexperimental_tpconvite_fk"), nullable = false)
    private TipoConviteAulaExperimental tipoConviteAulaExperimental;

    @ManyToOne
    @JoinColumn(name = "usuarioconvidou", foreignKey = @ForeignKey(name = "conviteaulaexperimental_usuario_fk"))
    private Usuario usuarioConvidou;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataValidacaoConvidado() {
        return dataValidacaoConvidado;
    }

    public void setDataValidacaoConvidado(Date dataValidacaoConvidado) {
        this.dataValidacaoConvidado = dataValidacaoConvidado;
    }

    public Cliente getClienteConvidado() {
        return clienteConvidado;
    }

    public void setClienteConvidado(Cliente clienteConvidado) {
        this.clienteConvidado = clienteConvidado;
    }

    public Cliente getClienteConvidou() {
        return clienteConvidou;
    }

    public void setClienteConvidou(Cliente clienteConvidou) {
        this.clienteConvidou = clienteConvidou;
    }

    public Cliente getClienteIndicadoOuPassivo() {
        return clienteIndicadoOuPassivo;
    }

    public void setClienteIndicadoOuPassivo(Cliente clienteIndicadoOuPassivo) {
        this.clienteIndicadoOuPassivo = clienteIndicadoOuPassivo;
    }

    public Colaborador getColaboradorResponsavelConvite() {
        return colaboradorResponsavelConvite;
    }

    public void setColaboradorResponsavelConvite(Colaborador colaboradorResponsavelConvite) {
        this.colaboradorResponsavelConvite = colaboradorResponsavelConvite;
    }

    public Indicado getIndicadoConvidado() {
        return indicadoConvidado;
    }

    public void setIndicadoConvidado(Indicado indicadoConvidado) {
        this.indicadoConvidado = indicadoConvidado;
    }

    public Passivo getPassivoConvidado() {
        return passivoConvidado;
    }

    public void setPassivoConvidado(Passivo passivoConvidado) {
        this.passivoConvidado = passivoConvidado;
    }

    public TipoConviteAulaExperimental getTipoConviteAulaExperimental() {
        return tipoConviteAulaExperimental;
    }

    public void setTipoConviteAulaExperimental(TipoConviteAulaExperimental tipoConviteAulaExperimental) {
        this.tipoConviteAulaExperimental = tipoConviteAulaExperimental;
    }

    public Usuario getUsuarioConvidou() {
        return usuarioConvidou;
    }

    public void setUsuarioConvidou(Usuario usuarioConvidou) {
        this.usuarioConvidou = usuarioConvidou;
    }
}
