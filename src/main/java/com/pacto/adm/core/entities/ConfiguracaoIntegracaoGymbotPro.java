package com.pacto.adm.core.entities;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
public class ConfiguracaoIntegracaoGymbotPro {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean ativo;
    private Integer empresa;
    private String descricao;
    private Integer tipoFluxo;
    private String fase;
    private String token;
    private String idFluxo;

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getTipoFluxo() {
        return tipoFluxo;
    }

    public void setTipoFluxo(Integer tipoFluxo) {
        this.tipoFluxo = tipoFluxo;
    }

    public String getFase() {
        return fase;
    }

    public void setFase(String fase) {
        this.fase = fase;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getIdFluxo() {
        return idFluxo;
    }

    public void setIdFluxo(String idFluxo) {
        this.idFluxo = idFluxo;
    }
}
