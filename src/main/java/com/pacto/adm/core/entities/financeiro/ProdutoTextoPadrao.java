package com.pacto.adm.core.entities.financeiro;

import com.pacto.adm.core.entities.*;
import com.pacto.adm.core.entities.contrato.PlanoTextoPadrao;
import com.pacto.config.annotations.RelationalField;

import javax.persistence.*;
import java.util.Date;

@Entity
public class ProdutoTextoPadrao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "vendaAvulsa", foreignKey = @ForeignKey(name = "fk_produtoTextoPadrao_vendaAvulsa"))
    private VendaAvulsa vendaAvulsa;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "produto", foreignKey = @ForeignKey(name = "fk_produtoTextoPadrao_produto"))
    private Produto produto;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "planoTextoPadrao", foreignKey = @ForeignKey(name = "fk_produtoTextoPadrao_planoTextoPadrao"))
    private PlanoTextoPadrao planoTextoPadrao;
    private String texto;
    private Date dataLancamento;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public VendaAvulsa getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsa vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public PlanoTextoPadrao getPlanoTextoPadrao() {
        return planoTextoPadrao;
    }

    public void setPlanoTextoPadrao(PlanoTextoPadrao planoTextoPadrao) {
        this.planoTextoPadrao = planoTextoPadrao;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }
}
