package com.pacto.adm.core.entities.conveniocobranca;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "conveniocobranca")
public class ConvenioCobranca {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private String descricao;

    @OneToMany(mappedBy = "convenioCobranca", fetch = FetchType.EAGER)
    public Set<ConvenioCobrancaEmpresa> empresas;

    public ConvenioCobranca() {
    }

    public ConvenioCobranca(Integer codigo, String descricao, Set<ConvenioCobrancaEmpresa> empresas) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.empresas = empresas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Set<ConvenioCobrancaEmpresa> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(Set<ConvenioCobrancaEmpresa> empresas) {
        this.empresas = empresas;
    }
}
