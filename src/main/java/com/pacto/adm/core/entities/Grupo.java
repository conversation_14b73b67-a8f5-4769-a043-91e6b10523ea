package com.pacto.adm.core.entities;

import com.pacto.adm.core.entities.contrato.SituacaoEnum;
import com.pacto.adm.core.enumerador.tipodesconto.TipoDescontoEnum;
import com.pacto.adm.core.enumerador.tipogrupo.TipoGrupoEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
public class Grupo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código único identificador do grupo", example = "22")
    private Integer codigo;

    @Schema(description = "Descrição do grupo", example = "Grupo da Academia")
    private String descricao;

    @Schema(description = "Percentual de desconto do grupo", example = "10.00")
    private Double percentualDescontoGrupo;

    @Schema(description = "Valor de desconto do grupo", example = "50.00")
    private Double valorDescontoGrupo;

    @Schema(description = "Tipo de desconto aplicado ao grupo. \n\n" +
            "**Valores disponíveis**\n" +
            "- 0 (Não aplicável)\n" +
            "- 1 (Percentual)\n" +
            "- 2 (Valor)\n" +
            "- 3 (Bônus)\n", example = "1", implementation = TipoDescontoEnum.class)
    private String tipoDesconto;

    @Schema(description = "Quantidade mínima de alunos necessários para aplicar o desconto do grupo", example = "1")
    private Integer quantidadeMinimaAluno;

    @Schema(description = "Situação dos alunos elegíveis para o grupo. \n\n" +
            "**Valores disponíveis**\n" +
            "- AT (Ativo)\n" +
            "- CA (Cancelado)\n" +
            "- IN (Inativo)\n" +
            "- TR (Trancado)\n", example = "AT", implementation = SituacaoEnum.class)
    private String situacaoAluno;

    @Schema(description = "Tipo do grupo de desconto. \n\n" +
            "**Valores disponíveis**\n" +
            "- FA (Em família)\n" +
            "- GR (Em grupo)\n", example = "FA", implementation = TipoGrupoEnum.class)
    private String tipo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getPercentualDescontoGrupo() {
        return percentualDescontoGrupo;
    }

    public void setPercentualDescontoGrupo(Double percentualDescontoGrupo) {
        this.percentualDescontoGrupo = percentualDescontoGrupo;
    }

    public Double getValorDescontoGrupo() {
        return valorDescontoGrupo;
    }

    public void setValorDescontoGrupo(Double valorDescontoGrupo) {
        this.valorDescontoGrupo = valorDescontoGrupo;
    }

    public String getTipoDesconto() {
        return tipoDesconto;
    }

    public void setTipoDesconto(String tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public Integer getQuantidadeMinimaAluno() {
        return quantidadeMinimaAluno;
    }

    public void setQuantidadeMinimaAluno(Integer quantidadeMinimaAluno) {
        this.quantidadeMinimaAluno = quantidadeMinimaAluno;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}
