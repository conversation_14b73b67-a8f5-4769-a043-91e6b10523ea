package com.pacto.adm.core.entities.passivo;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Evento;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.objecao.Objecao;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import com.pacto.adm.core.enumerador.origemsistema.OrigemSistemaEnumConverter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "passivo")
public class Passivo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dia;
    private String email;
    private Integer idLead;

    @Column(name = "lead", columnDefinition = "DEFAULT false")
    private Boolean lead;
    @Column(name = "metaextra", columnDefinition = "DEFAULT false")
    private Boolean metaExtra;
    private String nome;
    private String nomeConsulta;
    private String observacao;

    @Convert(converter = OrigemSistemaEnumConverter.class)
    @Column(name = "origemsistema", columnDefinition = "DEFAULT 1")
    private OrigemSistemaEnum origemSistema;
    private String telefoneCelular;
    private String telefoneResidencial;
    private String telefoneTrabalho;
    private String urlRd;
    private UUID uuid;

    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_passivo_cliente"))
    private Cliente cliente;

    @ManyToOne
    @JoinColumn(name = "colaboradorresponsavel", foreignKey = @ForeignKey(name = "fk_passivo_colaboradorresponsavel"))
    private Usuario colaboradorResponsavel;

    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Contrato contrato;

    @ManyToOne
    @JoinColumn(name = "emrpesa", foreignKey = @ForeignKey(name = "fk_passivo_empresa"))
    private Empresa emrpesa;

    @ManyToOne
    @JoinColumn(name = "evento", foreignKey = @ForeignKey(name = "fk_passivo_evento"))
    private Evento evento;

    @ManyToOne
    @JoinColumn(name = "objecao", foreignKey = @ForeignKey(name = "passivo_objecao_fk"))
    private Objecao objecao;

    @ManyToOne
    @JoinColumn(name = "responsavelcadastro", foreignKey = @ForeignKey(name = "fk_passivo_responsavelcadastro"))
    private Usuario responsavelCadastro;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getIdLead() {
        return idLead;
    }

    public void setIdLead(Integer idLead) {
        this.idLead = idLead;
    }

    public Boolean getLead() {
        return lead;
    }

    public void setLead(Boolean lead) {
        this.lead = lead;
    }

    public Boolean getMetaExtra() {
        return metaExtra;
    }

    public void setMetaExtra(Boolean metaExtra) {
        this.metaExtra = metaExtra;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeConsulta() {
        return nomeConsulta;
    }

    public void setNomeConsulta(String nomeConsulta) {
        this.nomeConsulta = nomeConsulta;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public String getTelefoneCelular() {
        return telefoneCelular;
    }

    public void setTelefoneCelular(String telefoneCelular) {
        this.telefoneCelular = telefoneCelular;
    }

    public String getTelefoneResidencial() {
        return telefoneResidencial;
    }

    public void setTelefoneResidencial(String telefoneResidencial) {
        this.telefoneResidencial = telefoneResidencial;
    }

    public String getTelefoneTrabalho() {
        return telefoneTrabalho;
    }

    public void setTelefoneTrabalho(String telefoneTrabalho) {
        this.telefoneTrabalho = telefoneTrabalho;
    }

    public String getUrlRd() {
        return urlRd;
    }

    public void setUrlRd(String urlRd) {
        this.urlRd = urlRd;
    }

    public UUID getUuid() {
        return uuid;
    }

    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Usuario getColaboradorResponsavel() {
        return colaboradorResponsavel;
    }

    public void setColaboradorResponsavel(Usuario colaboradorResponsavel) {
        this.colaboradorResponsavel = colaboradorResponsavel;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public Empresa getEmrpesa() {
        return emrpesa;
    }

    public void setEmrpesa(Empresa emrpesa) {
        this.emrpesa = emrpesa;
    }

    public Evento getEvento() {
        return evento;
    }

    public void setEvento(Evento evento) {
        this.evento = evento;
    }

    public Objecao getObjecao() {
        return objecao;
    }

    public void setObjecao(Objecao objecao) {
        this.objecao = objecao;
    }

    public Usuario getResponsavelCadastro() {
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(Usuario responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }
}
