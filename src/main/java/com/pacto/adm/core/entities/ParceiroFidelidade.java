package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Entity
public class ParceiroFidelidade {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer tipoParceiro;
    private boolean validarCliente;
    private String clientId;
    private String clientSecret;
    private String clientIdRedemption;
    private String clientSecretRedemption;
    private String codigoLoja;
    private String codigoMaquina;
    private String codigoOferta;
    private String codigoResgate;
    private String tags;
    private String cpf;
    private String token;
    private Date dataExpiracaoToken;
    private boolean ambienteProducao;
    private boolean parcelaVencidaGeraPonto;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_parceirofidelidade_empresa"))
    private Empresa empresa;

    @OneToMany(mappedBy = "parceiroFidelidade", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<TabelaParceiroFidelidade> tabelas;

    @OneToMany(mappedBy = "parceiroFidelidade", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<ProdutoParceiroFidelidade> produtos;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTipoParceiro() {
        return tipoParceiro;
    }

    public void setTipoParceiro(Integer tipoParceiro) {
        this.tipoParceiro = tipoParceiro;
    }

    public boolean isValidarCliente() {
        return validarCliente;
    }

    public void setValidarCliente(boolean validarCliente) {
        this.validarCliente = validarCliente;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getClientIdRedemption() {
        return clientIdRedemption;
    }

    public void setClientIdRedemption(String clientIdRedemption) {
        this.clientIdRedemption = clientIdRedemption;
    }

    public String getClientSecretRedemption() {
        return clientSecretRedemption;
    }

    public void setClientSecretRedemption(String clientSecretRedemption) {
        this.clientSecretRedemption = clientSecretRedemption;
    }

    public String getCodigoLoja() {
        return codigoLoja;
    }

    public void setCodigoLoja(String codigoLoja) {
        this.codigoLoja = codigoLoja;
    }

    public String getCodigoMaquina() {
        return codigoMaquina;
    }

    public void setCodigoMaquina(String codigoMaquina) {
        this.codigoMaquina = codigoMaquina;
    }

    public String getCodigoOferta() {
        return codigoOferta;
    }

    public void setCodigoOferta(String codigoOferta) {
        this.codigoOferta = codigoOferta;
    }

    public String getCodigoResgate() {
        return codigoResgate;
    }

    public void setCodigoResgate(String codigoResgate) {
        this.codigoResgate = codigoResgate;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getDataExpiracaoToken() {
        return dataExpiracaoToken;
    }

    public void setDataExpiracaoToken(Date dataExpiracaoToken) {
        this.dataExpiracaoToken = dataExpiracaoToken;
    }

    public boolean isAmbienteProducao() {
        return ambienteProducao;
    }

    public void setAmbienteProducao(boolean ambienteProducao) {
        this.ambienteProducao = ambienteProducao;
    }

    public boolean isParcelaVencidaGeraPonto() {
        return parcelaVencidaGeraPonto;
    }

    public void setParcelaVencidaGeraPonto(boolean parcelaVencidaGeraPonto) {
        this.parcelaVencidaGeraPonto = parcelaVencidaGeraPonto;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Set<TabelaParceiroFidelidade> getTabelas() {
        if (this.tabelas == null) {
            this.tabelas = new HashSet<>();
        }
        return this.tabelas;
    }

    public void setTabelas(Set<TabelaParceiroFidelidade> tabelas) {
        this.tabelas = tabelas;
    }

    public Set<ProdutoParceiroFidelidade> getProdutos() {
        if (this.produtos == null) {
            this.produtos = new HashSet<>();
        }
        return produtos;
    }

    public void setProdutos(Set<ProdutoParceiroFidelidade> produtos) {
        this.produtos = produtos;
    }
}
