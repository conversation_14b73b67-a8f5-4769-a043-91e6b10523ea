package com.pacto.adm.core.entities.contrato;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@Entity
public class PeriodoAcessoCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String tipoAcesso;
    private Date dataFinalAcesso;
    private Date dataInicioAcesso;
    private Integer aulaAvulsaDiaria;
    private Integer contrato;
    private Integer contratoBaseadoRenovacao;
    private Integer pessoa;
    private Integer responsavel;
    private Date dataLancamento;
    private String tokenGympass;
    private Integer reposicao;
    private String tipoGympass;
    private String tokenGogood;
    private Double valorGympass;
    private Boolean tipototalpass;
    private Integer produto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoAcesso() {
        return tipoAcesso;
    }

    public void setTipoAcesso(String tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public Date getDataFinalAcesso() {
        return dataFinalAcesso;
    }

    public void setDataFinalAcesso(Date dataFinalAcesso) {
        this.dataFinalAcesso = dataFinalAcesso;
    }

    public Date getDataInicioAcesso() {
        return dataInicioAcesso;
    }

    public void setDataInicioAcesso(Date dataInicioAcesso) {
        this.dataInicioAcesso = dataInicioAcesso;
    }

    public Integer getAulaAvulsaDiaria() {
        return aulaAvulsaDiaria;
    }

    public void setAulaAvulsaDiaria(Integer aulaAvulsaDiaria) {
        this.aulaAvulsaDiaria = aulaAvulsaDiaria;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getContratoBaseadoRenovacao() {
        return contratoBaseadoRenovacao;
    }

    public void setContratoBaseadoRenovacao(Integer contratoBaseadoRenovacao) {
        this.contratoBaseadoRenovacao = contratoBaseadoRenovacao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getTokenGympass() {
        return tokenGympass;
    }

    public void setTokenGympass(String tokenGympass) {
        this.tokenGympass = tokenGympass;
    }

    public Integer getReposicao() {
        return reposicao;
    }

    public void setReposicao(Integer reposicao) {
        this.reposicao = reposicao;
    }

    public String getTipoGympass() {
        return tipoGympass;
    }

    public void setTipoGympass(String tipoGympass) {
        this.tipoGympass = tipoGympass;
    }

    public Double getValorGympass() {
        return valorGympass;
    }

    public void setValorGympass(Double valorGympass) {
        this.valorGympass = valorGympass;
    }

    public Boolean getTipototalpass() { return tipototalpass; }

    public void setTipototalpass(Boolean tipototalpass) { this.tipototalpass = tipototalpass; }

    public String getTokenGogood() {
        return tokenGogood;
    }

    public void setTokenGogood(String tokenGogood) {
        this.tokenGogood = tokenGogood;
    }

    public Integer getProduto() { return produto; }

    public void setProduto(Integer produto) { this.produto = produto; }
}
