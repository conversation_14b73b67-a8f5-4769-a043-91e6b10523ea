package com.pacto.adm.core.entities.contrato;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.Usuario;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "contratoassinaturadigital")
public class ContratoAssinaturaDigital {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer contratoTextoPadrao;
    private Date lancamento;
    private String documentos;
    private String endereco;
    private String assinatura;
    private String atestado;
    private String anexo1;
    private String anexo2;
    private String anexoCancelamento;

    @RelationalField
    @OneToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "contratoassinaturadigital_contrato_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Contrato contrato;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "usuarioresponsavel", foreignKey = @ForeignKey(name = "contratoassinaturadigital_responsavel_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario usuarioResponsavel;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getContratoTextoPadrao() {
        return contratoTextoPadrao;
    }

    public void setContratoTextoPadrao(Integer contratoTextoPadrao) {
        this.contratoTextoPadrao = contratoTextoPadrao;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public String getDocumentos() {
        return documentos;
    }

    public void setDocumentos(String documentos) {
        this.documentos = documentos;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
    }

    public String getAtestado() {
        return atestado;
    }

    public void setAtestado(String atestado) {
        this.atestado = atestado;
    }

    public String getAnexo1() {
        return anexo1;
    }

    public void setAnexo1(String anexo1) {
        this.anexo1 = anexo1;
    }

    public String getAnexo2() {
        return anexo2;
    }

    public void setAnexo2(String anexo2) {
        this.anexo2 = anexo2;
    }

    public String getAnexoCancelamento() {
        return anexoCancelamento;
    }

    public void setAnexoCancelamento(String anexoCancelamento) {
        this.anexoCancelamento = anexoCancelamento;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public Usuario getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(Usuario usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }


}
