package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "clienteobservacao")
public class ClienteObservacao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String observacao;
    @Column(name = "datacadastro")
    private Date dataCadastro;
    @Column(name = "dataalteracao")
    private Date dataAlteracao;
    private boolean importante = false;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "clienteobservacao_cliente_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Cliente cliente;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "usuarioresponsavel", foreignKey = @ForeignKey(name = "clienteobservacao_usuarioresponsavel_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario usuarioResponsavel;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Usuario getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(Usuario usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public boolean isImportante() {
        return importante;
    }

    public void setImportante(boolean importante) {
        this.importante = importante;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }
}
