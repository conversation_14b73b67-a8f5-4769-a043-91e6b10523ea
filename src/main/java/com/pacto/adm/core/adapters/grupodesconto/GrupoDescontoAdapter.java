package com.pacto.adm.core.adapters.grupodesconto;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.grupodesconto.GrupoDescontoDTO;
import com.pacto.adm.core.entities.grupodesconto.GrupoDesconto;
import org.springframework.stereotype.Component;

@Component
public class GrupoDescontoAdapter implements AdapterInterface<GrupoDesconto, GrupoDescontoDTO> {

    @Override
    public GrupoDescontoDTO toDto(GrupoDesconto grupoDesconto) {
        GrupoDescontoDTO dto = new GrupoDescontoDTO();
        dto.setCodigo(grupoDesconto.getCodigo());
        dto.setDescricao(grupoDesconto.getDescricao());
        dto.setPercentualDescontoGrupo(grupoDesconto.getPercentualDescontoGrupo());
        dto.setTipo(grupoDesconto.getTipo());
        dto.setValorDescontoGrupo(grupoDesconto.getValorDescontoGrupo());
        dto.setTipoDesconto(grupoDesconto.getTipoDesconto());
        dto.setSituacaoAluno(grupoDesconto.getSituacaoAluno());
        dto.setQuantidadeMinimaAluno(grupoDesconto.getQuantidadeMinimaAluno());
        return dto;
    }

    @Override
    public GrupoDesconto toEntity(GrupoDescontoDTO grupoDescontoDTO) {
        GrupoDesconto grupoDesconto = new GrupoDesconto();
        grupoDesconto.setCodigo(grupoDescontoDTO.getCodigo());
        grupoDesconto.setDescricao(grupoDescontoDTO.getDescricao());
        grupoDesconto.setPercentualDescontoGrupo(grupoDescontoDTO.getPercentualDescontoGrupo());
        grupoDesconto.setTipo(grupoDescontoDTO.getTipo());
        grupoDesconto.setValorDescontoGrupo(grupoDescontoDTO.getValorDescontoGrupo());
        grupoDesconto.setTipoDesconto(grupoDescontoDTO.getTipoDesconto());
        grupoDesconto.setSituacaoAluno(grupoDescontoDTO.getSituacaoAluno());
        grupoDesconto.setQuantidadeMinimaAluno(grupoDescontoDTO.getQuantidadeMinimaAluno());
        return grupoDesconto;
    }
}
