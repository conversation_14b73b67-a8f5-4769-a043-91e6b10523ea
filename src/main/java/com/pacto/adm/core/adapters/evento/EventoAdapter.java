package com.pacto.adm.core.adapters.evento;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.indicacao.evento.EventoDTO;
import com.pacto.adm.core.entities.Evento;
import org.springframework.stereotype.Component;

@Component
public class EventoAdapter implements AdapterInterface<Evento, EventoDTO> {

    @Override
    public EventoDTO toDto(Evento evento) {
        if (evento == null) {
            return null;
        }
        EventoDTO eventoDTO = new EventoDTO();
        eventoDTO.setCodigo(evento.getCodigo());
        eventoDTO.setStatus(evento.getStatus());
        eventoDTO.setDescricao(evento.getDescricao());
        return eventoDTO;
    }

    @Override
    public Evento toEntity(EventoDTO eventoDTO) {
        if (eventoDTO == null) {
            return null;
        }
        Evento evento = new Evento();
        evento.setCodigo(eventoDTO.getCodigo());
        evento.setStatus(eventoDTO.getStatus());
        evento.setDescricao(eventoDTO.getDescricao());
        return evento;
    }
}
