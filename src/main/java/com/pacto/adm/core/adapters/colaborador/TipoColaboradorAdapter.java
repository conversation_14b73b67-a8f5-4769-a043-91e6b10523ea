package com.pacto.adm.core.adapters.colaborador;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.colaborador.TipoColaboradorDTO;
import com.pacto.adm.core.entities.TipoColaborador;
import org.springframework.stereotype.Component;

@Component
public class TipoColaboradorAdapter implements AdapterInterface<TipoColaborador, TipoColaboradorDTO> {

    @Override
    public TipoColaborador toEntity(TipoColaboradorDTO tipoColaboradorDTO) {
        if (tipoColaboradorDTO == null) {
            return null;
        }
        return new TipoColaborador(
                tipoColaboradorDTO.getCodigo(),
                tipoColaboradorDTO.getDescricao()
        );
    }

    @Override
    public TipoColaboradorDTO toDto(TipoColaborador tipoColaborador) {
        if (tipoColaborador == null) {
            return null;
        }
        return new TipoColaboradorDTO(
                tipoColaborador.getCodigo(),
                tipoColaborador.getDescricao()
        );
    }
}
