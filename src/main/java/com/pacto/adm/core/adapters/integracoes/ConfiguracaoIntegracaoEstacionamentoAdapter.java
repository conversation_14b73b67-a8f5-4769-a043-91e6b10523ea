package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoEstacionamentoDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoEstacionamentoAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoEstacionamentoDTO> {

    private final EmpresaConfigEstacionamentoAdapter empresaConfigEstacionamentoAdapter;

    public ConfiguracaoIntegracaoEstacionamentoAdapter(EmpresaConfigEstacionamentoAdapter empresaConfigEstacionamentoAdapter) {
        this.empresaConfigEstacionamentoAdapter = empresaConfigEstacionamentoAdapter;
    }

    @Override
    public ConfiguracaoIntegracaoEstacionamentoDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoEstacionamentoDTO configuracaoIntegracaoEstacionamentoDTO = new ConfiguracaoIntegracaoEstacionamentoDTO();
            configuracaoIntegracaoEstacionamentoDTO.setUtilizaSistemaEstacionamento(empresa.isUtilizaSistemaEstacionamento());
            configuracaoIntegracaoEstacionamentoDTO.setEmpresaConfigEstacionamento(empresaConfigEstacionamentoAdapter.toDto(empresa.getEmpresaConfigEstacionamento()));
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configuracaoIntegracaoEstacionamentoDTO.setEmpresa(empresaDTO);
            return configuracaoIntegracaoEstacionamentoDTO;
        }
        return null;
    }

}
