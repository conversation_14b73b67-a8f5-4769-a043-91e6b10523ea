package com.pacto.adm.core.adapters.passivo;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ClienteAdapter;
import com.pacto.adm.core.adapters.ContratoAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.adapters.evento.EventoAdapter;
import com.pacto.adm.core.adapters.objecao.ObjecaoAdapter;
import com.pacto.adm.core.dto.passivo.PassivoDTO;
import com.pacto.adm.core.entities.passivo.Passivo;
import org.springframework.stereotype.Component;

@Component
public class PassivoAdapter implements AdapterInterface<Passivo, PassivoDTO> {

    private final ClienteAdapter clienteAdapter;
    private final UsuarioAdapter usuarioAdapter;
    private final ContratoAdapter contratoAdapter;
    private final EmpresaAdapter empresaAdapter;
    private final EventoAdapter eventoAdapter;
    private final ObjecaoAdapter objecaoAdapter;

    public PassivoAdapter(
            ClienteAdapter clienteAdapter, UsuarioAdapter usuarioAdapter, ContratoAdapter contratoAdapter,
            EmpresaAdapter empresaAdapter, EventoAdapter eventoAdapter, ObjecaoAdapter objecaoAdapter
    ) {
        this.clienteAdapter = clienteAdapter;
        this.usuarioAdapter = usuarioAdapter;
        this.contratoAdapter = contratoAdapter;
        this.empresaAdapter = empresaAdapter;
        this.eventoAdapter = eventoAdapter;
        this.objecaoAdapter = objecaoAdapter;
    }

    @Override
    public PassivoDTO toDto(Passivo passivo) {
        PassivoDTO passivoDTO = new PassivoDTO();
        passivoDTO.setCodigo(passivo.getCodigo());
        passivoDTO.setDia(passivo.getDia());
        passivoDTO.setEmail(passivo.getEmail());
        passivoDTO.setIdLead(passivo.getIdLead());
        passivoDTO.setLead(passivo.getLead());
        passivoDTO.setMetaExtra(passivo.getMetaExtra());
        passivoDTO.setNome(passivo.getNome());
        passivoDTO.setNomeConsulta(passivo.getNomeConsulta());
        passivoDTO.setObservacao(passivo.getObservacao());
        passivoDTO.setOrigemSistema(passivo.getOrigemSistema());
        passivoDTO.setTelefoneCelular(passivo.getTelefoneCelular());
        passivoDTO.setTelefoneResidencial(passivo.getTelefoneResidencial());
        passivoDTO.setTelefoneTrabalho(passivo.getTelefoneTrabalho());
        passivoDTO.setUrlRd(passivo.getUrlRd());
        passivoDTO.setUuid(passivo.getUuid());
        if (passivo.getCliente() != null) {
            passivoDTO.setCliente(clienteAdapter.toDto(passivo.getCliente()));
        }
        if (passivo.getColaboradorResponsavel() != null) {
            passivoDTO.setColaboradorResponsavel(usuarioAdapter.toDto(passivo.getColaboradorResponsavel()));
        }
        if (passivo.getContrato() != null) {
            passivoDTO.setContrato(contratoAdapter.toDto(passivo.getContrato()));
        }
        if (passivo.getEmrpesa() != null) {
            passivoDTO.setEmrpesa(empresaAdapter.toDto(passivo.getEmrpesa()));
        }
        if (passivo.getEvento() != null) {
            passivoDTO.setEvento(eventoAdapter.toDto(passivo.getEvento()));
        }
        if (passivo.getObjecao() != null) {
            passivoDTO.setObjecao(objecaoAdapter.toDto(passivo.getObjecao()));
        }
        if (passivo.getResponsavelCadastro() != null) {
            passivoDTO.setResponsavelCadastro(usuarioAdapter.toDto(passivo.getResponsavelCadastro()));
        }
        return passivoDTO;
    }

    @Override
    public Passivo toEntity(PassivoDTO passivoDTO) {
        Passivo passivo = new Passivo();
        passivo.setCodigo(passivoDTO.getCodigo());
        passivo.setDia(passivoDTO.getDia());
        passivo.setEmail(passivoDTO.getEmail());
        passivo.setIdLead(passivoDTO.getIdLead());
        passivo.setLead(passivoDTO.getLead());
        passivo.setMetaExtra(passivoDTO.getMetaExtra());
        passivo.setNome(passivoDTO.getNome());
        passivo.setNomeConsulta(passivoDTO.getNomeConsulta());
        passivo.setObservacao(passivoDTO.getObservacao());
        passivo.setOrigemSistema(passivoDTO.getOrigemSistema());
        passivo.setTelefoneCelular(passivoDTO.getTelefoneCelular());
        passivo.setTelefoneResidencial(passivoDTO.getTelefoneResidencial());
        passivo.setTelefoneTrabalho(passivoDTO.getTelefoneTrabalho());
        passivo.setUrlRd(passivoDTO.getUrlRd());
        passivo.setUuid(passivoDTO.getUuid());
        if (passivoDTO.getCliente() != null) {
            passivo.setCliente(clienteAdapter.toEntity(passivoDTO.getCliente()));
        }
        if (passivoDTO.getColaboradorResponsavel() != null) {
            passivo.setColaboradorResponsavel(usuarioAdapter.toEntity(passivoDTO.getColaboradorResponsavel()));
        }
        if (passivoDTO.getContrato() != null) {
            passivo.setContrato(contratoAdapter.toEntity(passivoDTO.getContrato()));
        }
        if (passivoDTO.getEmrpesa() != null) {
            passivo.setEmrpesa(empresaAdapter.toEntity(passivoDTO.getEmrpesa()));
        }
        if (passivoDTO.getEvento() != null) {
            passivo.setEvento(eventoAdapter.toEntity(passivoDTO.getEvento()));
        }
        if (passivoDTO.getObjecao() != null) {
            passivo.setObjecao(objecaoAdapter.toEntity(passivoDTO.getObjecao()));
        }
        if (passivoDTO.getResponsavelCadastro() != null) {
            passivo.setResponsavelCadastro(usuarioAdapter.toEntity(passivoDTO.getResponsavelCadastro()));
        }
        return passivo;
    }
}
