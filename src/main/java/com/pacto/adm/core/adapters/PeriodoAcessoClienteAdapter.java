package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.PeriodoAcessoClienteDTO;
import com.pacto.adm.core.entities.contrato.PeriodoAcessoCliente;
import org.springframework.stereotype.Component;

@Component
public class PeriodoAcessoClienteAdapter implements AdapterInterface<PeriodoAcessoCliente, PeriodoAcessoClienteDTO> {

    @Override
    public PeriodoAcessoClienteDTO toDto(PeriodoAcessoCliente periodoAcessoCliente) {
        if (periodoAcessoCliente != null) {
            PeriodoAcessoClienteDTO periodoAcessoClienteDTO = new PeriodoAcessoClienteDTO();
            periodoAcessoClienteDTO.setDataAcesso(periodoAcessoCliente.getDataInicioAcesso());
            periodoAcessoClienteDTO.setCodigo(periodoAcessoCliente.getCodigo());
            periodoAcessoClienteDTO.setTipoAcesso(periodoAcessoCliente.getTipoAcesso());
            periodoAcessoClienteDTO.setToken(periodoAcessoCliente.getTokenGympass());
            periodoAcessoClienteDTO.setValor(periodoAcessoCliente.getValorGympass());
            periodoAcessoClienteDTO.setContrato(periodoAcessoCliente.getContrato());
            periodoAcessoClienteDTO.setProduto(periodoAcessoCliente.getProduto());

            periodoAcessoClienteDTO.setDataInicioAcesso(periodoAcessoCliente.getDataInicioAcesso());
            periodoAcessoClienteDTO.setDataFinalAcesso(periodoAcessoCliente.getDataFinalAcesso());
            periodoAcessoClienteDTO.setTipototalpass(periodoAcessoCliente.getTipototalpass());
            periodoAcessoClienteDTO.setTokenGogood(periodoAcessoCliente.getTokenGogood());
            return periodoAcessoClienteDTO;
        }
        return null;
    }

    @Override
    public PeriodoAcessoCliente toEntity(PeriodoAcessoClienteDTO dto) {
        if (dto != null) {
            PeriodoAcessoCliente entity = new PeriodoAcessoCliente();

            entity.setCodigo(dto.getCodigo());
            entity.setTipoAcesso(dto.getTipoAcesso());
            entity.setPessoa(dto.getPessoa());
            entity.setDataInicioAcesso(dto.getDataInicioAcesso());
            entity.setDataFinalAcesso(dto.getDataFinalAcesso());
            entity.setResponsavel(dto.getResponsavel());
            entity.setDataLancamento(dto.getDataLancamento());
            entity.setContrato(dto.getContrato());
            entity.setProduto(dto.getProduto());
            entity.setTipototalpass(dto.getTipototalpass());

//            entity.setAulaAvulsaDiaria();
//            entity.setContrato();
//            entity.setContratoBaseadoRenovacao();

//            entity.setReposicao();

//            entity.setTokenGympass();
//            entity.setTipoGympass();
//            entity.setValorGympass();

            return entity;
        }
        return null;
    }
}
