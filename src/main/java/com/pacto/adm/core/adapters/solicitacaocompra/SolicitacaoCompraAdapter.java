package com.pacto.adm.core.adapters.solicitacaocompra;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ArquivoAdapter;
import com.pacto.adm.core.dto.solicitacaocompra.SolicitacaoCompraDTO;
import com.pacto.adm.core.entities.solicitacaocompra.SolicitacaoCompra;
import com.pacto.adm.core.mscomunication.mediams.MediaMs;
import com.pacto.adm.core.services.interfaces.ArquivoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class SolicitacaoCompraAdapter implements AdapterInterface<SolicitacaoCompra, SolicitacaoCompraDTO> {

    @Autowired
    private ArquivoService arquivoService;

    @Override
    public SolicitacaoCompra toEntity(SolicitacaoCompraDTO solicitacaoCompraDTO) {
        if (Objects.nonNull(solicitacaoCompraDTO)) {
            SolicitacaoCompra solicitacaoCompra = new SolicitacaoCompra();
            solicitacaoCompra.setCodigo(solicitacaoCompraDTO.getCodigo());
            solicitacaoCompra.setSituacao(solicitacaoCompraDTO.getSituacao());
            solicitacaoCompra.setDescricao(solicitacaoCompraDTO.getDescricao());
            solicitacaoCompra.setTitulo(solicitacaoCompraDTO.getTitulo());
            solicitacaoCompra.setDataSolicitacao(solicitacaoCompraDTO.getDataSolicitacao());
            solicitacaoCompra.setMotivoNegacao(solicitacaoCompraDTO.getMotivoNegacao());

            return solicitacaoCompra;
        }
        return null;
    }

    public SolicitacaoCompra toResquestEntity(SolicitacaoCompraDTO solicitacaoCompraDTO, SolicitacaoCompra solicitacaoCompra) {
        solicitacaoCompra.setCodigo(solicitacaoCompraDTO.getCodigo() != null ? solicitacaoCompraDTO.getCodigo() : 0);
        solicitacaoCompra.setSituacao(solicitacaoCompraDTO.getSituacao() != null ? solicitacaoCompraDTO.getSituacao() : null);
        solicitacaoCompra.setDescricao(solicitacaoCompraDTO.getDescricao() != null ? solicitacaoCompraDTO.getDescricao() : null);
        solicitacaoCompra.setTitulo(solicitacaoCompraDTO.getTitulo() != null ? solicitacaoCompraDTO.getTitulo() : null);
        solicitacaoCompra.setDataSolicitacao(solicitacaoCompraDTO.getDataSolicitacao() != null ? solicitacaoCompraDTO.getDataSolicitacao() : null);
        solicitacaoCompra.setMotivoNegacao(solicitacaoCompraDTO.getMotivoNegacao() != null ? solicitacaoCompraDTO.getMotivoNegacao() : null);

        return solicitacaoCompra;
    }

    @Override
    public SolicitacaoCompraDTO toDto(SolicitacaoCompra solicitacaoCompra) {
        if (Objects.nonNull(solicitacaoCompra)) {
            SolicitacaoCompraDTO solicitacaoCompraDTO;
            try {
                solicitacaoCompraDTO = SolicitacaoCompraDTO.builder()
                        .codigo(solicitacaoCompra.getCodigo())
                        .situacao(solicitacaoCompra.getSituacao())
                        .descricao(solicitacaoCompra.getDescricao())
                        .titulo(solicitacaoCompra.getTitulo())
                        .dataSolicitacao(solicitacaoCompra.getDataSolicitacao())
                        .motivoNegacao(solicitacaoCompra.getMotivoNegacao())
                        .build();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            solicitacaoCompraDTO.setArquivos(arquivoService.findByIdSolicitacaoCompra(solicitacaoCompra.getCodigo()));

            return solicitacaoCompraDTO;
        }

        return null;
    }
}
