package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.integracoes.EmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.empresa.EmpresaConfigEstacionamento;
import org.springframework.stereotype.Component;

@Component
public class EmpresaConfigEstacionamentoAdapter implements AdapterInterface<EmpresaConfigEstacionamento, EmpresaConfigEstacionamentoDTO> {

    @Override
    public EmpresaConfigEstacionamentoDTO toDto(EmpresaConfigEstacionamento empresaConfigEstacionamento) {
        if (empresaConfigEstacionamento != null) {
            EmpresaConfigEstacionamentoDTO empresaConfigEstacionamentoDTO = new EmpresaConfigEstacionamentoDTO();
            empresaConfigEstacionamentoDTO.setCodigo(empresaConfigEstacionamento.getCodigo());
            empresaConfigEstacionamentoDTO.setFtpHost(empresaConfigEstacionamento.getFtpHost());
            empresaConfigEstacionamentoDTO.setFtpUser(empresaConfigEstacionamento.getFtpUser());
            empresaConfigEstacionamentoDTO.setFtpPass(empresaConfigEstacionamento.getFtpPass());
            empresaConfigEstacionamentoDTO.setFtpPort(empresaConfigEstacionamento.getFtpPort());
            empresaConfigEstacionamentoDTO.setEnviaValor(empresaConfigEstacionamento.isEnviaValor());
            empresaConfigEstacionamentoDTO.setEnviaHorario(empresaConfigEstacionamento.isEnviaHorario());
            empresaConfigEstacionamentoDTO.setEnviaTelefoneEmail(empresaConfigEstacionamento.isEnviaTelefoneEmail());
            empresaConfigEstacionamentoDTO.setNomeArquivo(empresaConfigEstacionamento.getNomeArquivo());
            empresaConfigEstacionamentoDTO.setProdutosAdicionar(empresaConfigEstacionamento.getProdutosAdicionar());
            empresaConfigEstacionamentoDTO.setEmpresa(empresaConfigEstacionamento.getEmpresa().getCodigo());
            return empresaConfigEstacionamentoDTO;
        }
        return null;
    }

    @Override
    public EmpresaConfigEstacionamento toEntity(EmpresaConfigEstacionamentoDTO empresaConfigEstacionamentoDTO) {
        if (empresaConfigEstacionamentoDTO != null) {
            EmpresaConfigEstacionamento empresaConfigEstacionamento = new EmpresaConfigEstacionamento();
            empresaConfigEstacionamento.setCodigo(empresaConfigEstacionamentoDTO.getCodigo());
            empresaConfigEstacionamento.setFtpHost(empresaConfigEstacionamentoDTO.getFtpHost());
            empresaConfigEstacionamento.setFtpUser(empresaConfigEstacionamentoDTO.getFtpUser());
            empresaConfigEstacionamento.setFtpPass(empresaConfigEstacionamentoDTO.getFtpPass());
            empresaConfigEstacionamento.setFtpPort(empresaConfigEstacionamentoDTO.getFtpPort());
            empresaConfigEstacionamento.setEnviaValor(empresaConfigEstacionamentoDTO.isEnviaValor());
            empresaConfigEstacionamento.setEnviaHorario(empresaConfigEstacionamentoDTO.isEnviaHorario());
            empresaConfigEstacionamento.setEnviaTelefoneEmail(empresaConfigEstacionamentoDTO.isEnviaTelefoneEmail());
            empresaConfigEstacionamento.setNomeArquivo(empresaConfigEstacionamentoDTO.getNomeArquivo());
            empresaConfigEstacionamento.setProdutosAdicionar(empresaConfigEstacionamentoDTO.getProdutosAdicionar());
            Empresa empresa = new Empresa();
            empresa.setCodigo(empresaConfigEstacionamentoDTO.getEmpresa());
            empresaConfigEstacionamento.setEmpresa(empresa);
            return empresaConfigEstacionamento;
        }
        return null;
    }
}
