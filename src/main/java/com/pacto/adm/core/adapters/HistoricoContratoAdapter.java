package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dao.interfaces.UsuarioDao;
import com.pacto.adm.core.dto.HistoricoContratoDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.contrato.HistoricoContrato;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HistoricoContratoAdapter implements AdapterInterface<HistoricoContrato, HistoricoContratoDTO> {

    @Autowired
    ContratoAdapter contratoAdapter;
    @Autowired
    JustificativaOperacaoAdapter justificativaOperacaoAdapter;
    @Autowired
    ArquivoAdapter arquivoAdapter;
    @Autowired
    UsuarioAdapter usuarioAdapter;
    @Autowired
    UsuarioDao usuarioDao;

    @Override
    public HistoricoContrato toEntity(HistoricoContratoDTO historicoContratoDTO) {
        HistoricoContrato historicoContrato = new HistoricoContrato();
        historicoContrato = toEntity(historicoContratoDTO, historicoContrato);
        return historicoContrato;
    }

    @Override
    public HistoricoContrato toEntity(HistoricoContratoDTO historicoContratoDTO, HistoricoContrato historicoContrato) {
        historicoContrato.setCodigo(historicoContratoDTO.getCodigo());
        historicoContrato.setDataFinalSituacao(historicoContratoDTO.getDataFinalSituacao());
        historicoContrato.setDataInicioSituacao(historicoContratoDTO.getDataInicioSituacao());
        historicoContrato.setDataRegistro(historicoContratoDTO.getDataRegistro());
        historicoContrato.setTipoHistorico(historicoContratoDTO.getTipoHistorico());
        historicoContrato.setSituacaoRelativaHistorico(historicoContratoDTO.getSituacaoRelativaHistorico());
        if (historicoContratoDTO.getResponsavelLiberacaoMudancaHistorico() != null) {
            historicoContrato.setResponsavelLiberacaoMudancaHistorico(historicoContratoDTO.getResponsavelLiberacaoMudancaHistorico().getCodigo());
        }
        if (historicoContratoDTO.getResponsavelRegistro() != null) {
            historicoContrato.setResponsavelRegistro(usuarioAdapter.toEntity(historicoContratoDTO.getResponsavelRegistro()));
        }

        historicoContrato.setDescricao(historicoContratoDTO.getDescricao());
        historicoContrato.setRetornoManual(historicoContratoDTO.getRetornoManual());
        historicoContrato.setDataInicioTemporal(historicoContratoDTO.getDataInicioTemporal());

        Contrato contrato = new Contrato();
        contrato.setCodigo(historicoContratoDTO.getContrato());
        historicoContrato.setContrato(contrato);

        return historicoContrato;
    }

    @Override
    public HistoricoContratoDTO toDto(HistoricoContrato historicoContrato) {
        try {
            HistoricoContratoDTO historicoContratoDTO = new HistoricoContratoDTO();

            historicoContratoDTO.setDataFinalSituacao(historicoContrato.getDataFinalSituacao());
            historicoContratoDTO.setDataInicioSituacao(historicoContrato.getDataInicioSituacao());
            historicoContratoDTO.setDataRegistro(historicoContrato.getDataRegistro());
            historicoContratoDTO.setTipoHistorico(historicoContrato.getTipoHistorico());
            historicoContratoDTO.setSituacaoRelativaHistorico(historicoContrato.getSituacaoRelativaHistorico());
            if (historicoContrato.getResponsavelLiberacaoMudancaHistorico() != null) {
                UsuarioDTO usuarioDTO = usuarioAdapter.toDto(usuarioDao.findById(historicoContrato.getResponsavelLiberacaoMudancaHistorico()));
                historicoContratoDTO.setResponsavelLiberacaoMudancaHistorico(usuarioDTO);
            }
            historicoContratoDTO.setResponsavelRegistro(usuarioAdapter.toDto(historicoContrato.getResponsavelRegistro()));
            historicoContratoDTO.setDescricao(historicoContrato.getDescricao());
            historicoContratoDTO.setRetornoManual(historicoContrato.isRetornoManual());
            historicoContratoDTO.setDataInicioTemporal(historicoContrato.getDataInicioTemporal());
            historicoContratoDTO.setContrato(historicoContrato.getContrato().getCodigo());

            return historicoContratoDTO;
        }  catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
