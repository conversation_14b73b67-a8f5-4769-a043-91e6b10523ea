package com.pacto.adm.core.adapters.conveniocobranca;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.conveniocobranca.ConvenioCobrancaDTO;
import com.pacto.adm.core.entities.conveniocobranca.ConvenioCobranca;
import org.springframework.stereotype.Component;

@Component
public class ConvenioCobrancaAdapter implements AdapterInterface<ConvenioCobranca, ConvenioCobrancaDTO> {

    private final ConvenioCobrancaEmpresaAdapter convenioCobrancaEmpresaAdapter;

    public ConvenioCobrancaAdapter(ConvenioCobrancaEmpresaAdapter convenioCobrancaEmpresaAdapter) {
        this.convenioCobrancaEmpresaAdapter = convenioCobrancaEmpresaAdapter;
    }

    @Override
    public ConvenioCobranca toEntity(ConvenioCobrancaDTO convenioCobrancaDTO) {

        if (convenioCobrancaDTO == null) {
            return null;
        }

        ConvenioCobranca convenioCobranca = new ConvenioCobranca();
        convenioCobranca.setCodigo(convenioCobrancaDTO.getCodigo());
        convenioCobranca.setDescricao(convenioCobrancaDTO.getDescricao());
        convenioCobranca.setEmpresas(convenioCobrancaEmpresaAdapter.toEntities(convenioCobrancaDTO.getEmpresas()));
        return convenioCobranca;
    }

    @Override
    public ConvenioCobrancaDTO toDto(ConvenioCobranca convenioCobranca) {
        if (convenioCobranca == null) {
            return null;
        }

        ConvenioCobrancaDTO convenioCobrancaDTO = new ConvenioCobrancaDTO();
        convenioCobrancaDTO.setCodigo(convenioCobranca.getCodigo());
        convenioCobrancaDTO.setDescricao(convenioCobranca.getDescricao());
        convenioCobrancaDTO.setEmpresas(convenioCobrancaEmpresaAdapter.toDtos(convenioCobranca.getEmpresas()));

        return convenioCobrancaDTO;
    }
}
