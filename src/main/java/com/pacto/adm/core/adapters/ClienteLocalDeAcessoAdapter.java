package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.ClienteLocalDeAcessoDTO;
import com.pacto.adm.core.entities.Cliente;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ClienteLocalDeAcessoAdapter implements AdapterInterface<Cliente, ClienteLocalDeAcessoDTO> {

    @Autowired
    PessoaAdapter pessoaAdapter;
    @Autowired
    EmpresaAdapter empresaAdapter;

    public ClienteLocalDeAcessoAdapter() {
    }

    @Override
    public ClienteLocalDeAcessoDTO toDto(Cliente cliente) {
        if (cliente != null) {
            ClienteLocalDeAcessoDTO clienteLocalDeAcessoDTO = new ClienteLocalDeAcessoDTO();
            clienteLocalDeAcessoDTO.setCodigo(cliente.getCodigo());
            clienteLocalDeAcessoDTO.setPessoa(cliente.getPessoa());
            clienteLocalDeAcessoDTO.setSituacao(cliente.getSituacao());
            clienteLocalDeAcessoDTO.setCodigoMatricula(cliente.getCodigoMatricula());
            clienteLocalDeAcessoDTO.setMatricula(cliente.getMatricula());
            return clienteLocalDeAcessoDTO;
        }
        return null;
    }

    @Override
    public Cliente toEntity(ClienteLocalDeAcessoDTO clienteLocalDeAcessoDTO) {
        if (clienteLocalDeAcessoDTO != null) {
            Cliente cliente = new Cliente();
            cliente.setCodigo(clienteLocalDeAcessoDTO.getCodigo());
            cliente.setPessoa(clienteLocalDeAcessoDTO.getPessoa());
            cliente.setSituacao(clienteLocalDeAcessoDTO.getSituacao());
            cliente.setCodigoMatricula(clienteLocalDeAcessoDTO.getCodigoMatricula());
            cliente.setMatricula(clienteLocalDeAcessoDTO.getMatricula());
            return cliente;
        }
        return null;
    }
}
