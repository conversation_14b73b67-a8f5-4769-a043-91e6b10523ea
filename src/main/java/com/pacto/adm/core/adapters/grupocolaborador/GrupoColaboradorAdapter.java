package com.pacto.adm.core.adapters.grupocolaborador;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.grupocolaborador.GrupoColaboradorDTO;
import com.pacto.adm.core.entities.GrupoColaborador;
import org.springframework.stereotype.Component;

@Component
public class GrupoColaboradorAdapter implements AdapterInterface<GrupoColaborador, GrupoColaboradorDTO> {
    
    private final GrupoColaboradorParticipanteAdapter grupoColaboradorParticipanteAdapter;
    private final EmpresaAdapter empresaAdapter;
    private final UsuarioAdapter usuarioAdapter;

    public GrupoColaboradorAdapter(
            GrupoColaboradorParticipanteAdapter grupoColaboradorParticipanteAdapter,
            EmpresaAdapter empresaAdapter, UsuarioAdapter usuarioAdapter
    ) {
        this.grupoColaboradorParticipanteAdapter = grupoColaboradorParticipanteAdapter;
        this.empresaAdapter = empresaAdapter;
        this.usuarioAdapter = usuarioAdapter;
    }


    @Override
    public GrupoColaboradorDTO toDto(GrupoColaborador entity) {
        if (entity == null) {
            return null;
        }

        GrupoColaboradorDTO dto = new GrupoColaboradorDTO();
        dto.setCodigo(entity.getCodigo());
        dto.setDescricao(entity.getDescricao());
        if (entity.getEmpresa() != null) {
            dto.setEmpresa(empresaAdapter.toDto(entity.getEmpresa()));
        }
        if (entity.getGerente() != null) {
            dto.setGerente(usuarioAdapter.toDto(entity.getGerente()));
        }
        dto.setGrupoColaboradorParticipantes(grupoColaboradorParticipanteAdapter.toDtos(entity.getGrupoColaboradorParticipantes()));
        dto.setSituacaoGrupo(entity.getSituacaoGrupo());
        dto.setTipoGrupo(entity.getTipoGrupo());
        dto.setSemGrupo(entity.isSemGrupo());

        return dto;
    }

    @Override
    public GrupoColaborador toEntity(GrupoColaboradorDTO dto) {
        if (dto == null) {
            return null;
        }

        GrupoColaborador entity = new GrupoColaborador();
        entity.setCodigo(dto.getCodigo());
        entity.setDescricao(dto.getDescricao());
        entity.setEmpresa(empresaAdapter.toEntity(dto.getEmpresa()));
        entity.setGerente(usuarioAdapter.toEntity(dto.getGerente()));
        entity.setGrupoColaboradorParticipantes(grupoColaboradorParticipanteAdapter.toEntities(dto.getGrupoColaboradorParticipantes()));
        entity.setSituacaoGrupo(dto.getSituacaoGrupo());
        entity.setTipoGrupo(dto.getTipoGrupo());
        entity.setSemGrupo(dto.isSemGrupo());

        return entity;
    }
   
}
