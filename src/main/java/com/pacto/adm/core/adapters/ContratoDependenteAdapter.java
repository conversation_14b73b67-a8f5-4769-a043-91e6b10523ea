package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.ContratoDependenteDTO;
import com.pacto.adm.core.entities.contrato.ContratoDependente;
import org.springframework.stereotype.Component;

@Component
public class ContratoDependenteAdapter implements AdapterInterface<ContratoDependente, ContratoDependenteDTO> {

    private final ClienteAdapter clienteAdapter;
    private final ContratoAdapter contratoAdapter;

    public ContratoDependenteAdapter(
            ClienteAdapter clienteAdapter,
            ContratoAdapter contratoAdapter
    ) {
        this.clienteAdapter = clienteAdapter;
        this.contratoAdapter = contratoAdapter;
    }

    @Override
    public ContratoDependente toEntity(ContratoDependenteDTO dto) {
        ContratoDependente obj = new ContratoDependente();
        obj = toEntity(dto, obj);
        return obj;
    }

    @Override
    public ContratoDependente toEntity(ContratoDependenteDTO dto, ContratoDependente obj) {
        if (dto == null) {
            return null;
        }
        obj.setCodigo(dto.getCodigo());
        obj.setDataInicio(dto.getDataInicio());
        obj.setDataFinal(dto.getDataFinal());
        obj.setDataFinalAjustada(dto.getDataFinalAjustada());
        obj.setPosicaoDependente(dto.getPosicaoDependente());
        obj.setTitular(dto.getTitular());

        if (dto.getCliente() != null) {
            obj.setCliente(clienteAdapter.toEntity(dto.getCliente()));
        }
        if (dto.getContrato() != null) {
            obj.setContrato(contratoAdapter.toEntity(dto.getContrato()));
        }
        return obj;
    }

    @Override
    public ContratoDependenteDTO toDto(ContratoDependente obj) {
        ContratoDependenteDTO dto = new ContratoDependenteDTO();
        if (obj != null) {
            dto.setCodigo(obj.getCodigo());
            dto.setDataInicio(obj.getDataInicio());
            dto.setDataFinal(obj.getDataFinal());
            dto.setDataFinalAjustada(obj.getDataFinalAjustada());
            dto.setPosicaoDependente(obj.getPosicaoDependente());
            dto.setTitular(obj.getTitular());

            if (obj.getCliente() != null) {
                dto.setCliente(clienteAdapter.toDto(obj.getCliente()));
            }
            if (obj.getContrato() != null) {
                dto.setContrato(contratoAdapter.toDto(obj.getContrato()));
            }
            return dto;
        }
        return null;
    }

}
