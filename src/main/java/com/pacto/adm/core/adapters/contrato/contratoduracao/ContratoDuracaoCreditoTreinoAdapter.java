package com.pacto.adm.core.adapters.contrato.contratoduracao;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.contrato.contratoduracao.ContratoDuracaoCreditoTreinoDTO;
import com.pacto.adm.core.entities.contrato.ContratoDuracaoCreditoTreino;
import org.springframework.stereotype.Component;

@Component
public class ContratoDuracaoCreditoTreinoAdapter implements AdapterInterface<ContratoDuracaoCreditoTreino, ContratoDuracaoCreditoTreinoDTO> {

    @Override
    public ContratoDuracaoCreditoTreinoDTO toDto(ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreino) {
        ContratoDuracaoCreditoTreinoDTO dto = new ContratoDuracaoCreditoTreinoDTO();
        if (contratoDuracaoCreditoTreino != null) {
            dto.setCodigo(contratoDuracaoCreditoTreino.getCodigo());
            dto.setTipoHorario(contratoDuracaoCreditoTreino.getTipoHorario());
            dto.setNumeroVezesSemana(contratoDuracaoCreditoTreino.getNumeroVezesSemana());
            dto.setQuantidadeCreditoCompra(contratoDuracaoCreditoTreino.getQuantidadeCreditoCompra());
            dto.setQuantidadeCreditoMensal(contratoDuracaoCreditoTreino.getQuantidadeCreditoMensal());
            dto.setQuantidadeCreditoDisponivel(contratoDuracaoCreditoTreino.getQuantidadeCreditoDisponivel());
            dto.setValorUnitario(contratoDuracaoCreditoTreino.getValorUnitario());
            dto.setCreditoTreinoNaoCumulativo(contratoDuracaoCreditoTreino.isCreditoTreinoNaoCumulativo());
            dto.setDataUltimoCreditoMensal(contratoDuracaoCreditoTreino.getDataUltimoCreditoMensal());
        }
        return dto;
    }

    @Override
    public ContratoDuracaoCreditoTreino toEntity(ContratoDuracaoCreditoTreinoDTO contratoDuracaoCreditoTreinoDTO) {
        ContratoDuracaoCreditoTreino entity = new ContratoDuracaoCreditoTreino();
        if (contratoDuracaoCreditoTreinoDTO != null) {
            entity.setCodigo(contratoDuracaoCreditoTreinoDTO.getCodigo());
            entity.setTipoHorario(contratoDuracaoCreditoTreinoDTO.getTipoHorario());
            entity.setNumeroVezesSemana(contratoDuracaoCreditoTreinoDTO.getNumeroVezesSemana());
            entity.setQuantidadeCreditoCompra(contratoDuracaoCreditoTreinoDTO.getQuantidadeCreditoCompra());
            entity.setQuantidadeCreditoMensal(contratoDuracaoCreditoTreinoDTO.getQuantidadeCreditoMensal());
            entity.setQuantidadeCreditoDisponivel(contratoDuracaoCreditoTreinoDTO.getQuantidadeCreditoDisponivel());
            entity.setValorUnitario(contratoDuracaoCreditoTreinoDTO.getValorUnitario());
            entity.setCreditoTreinoNaoCumulativo(contratoDuracaoCreditoTreinoDTO.getCreditoTreinoNaoCumulativo());
            entity.setDataUltimoCreditoMensal(contratoDuracaoCreditoTreinoDTO.getDataUltimoCreditoMensal());
        }
        return entity;
    }
}
