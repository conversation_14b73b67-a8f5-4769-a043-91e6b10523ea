package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.ClienteObservacaoDTO;
import com.pacto.adm.core.entities.ClienteObservacao;
import com.pacto.adm.core.util.Util;
import org.springframework.stereotype.Component;

@Component
public class ClienteObservacaoAdapter implements AdapterInterface<ClienteObservacao, ClienteObservacaoDTO> {

    private final ClienteAdapter clienteAdapter;
    private final UsuarioAdapter usuarioAdapter;

    public ClienteObservacaoAdapter(ClienteAdapter clienteAdapter, UsuarioAdapter usuarioAdapter) {
        this.clienteAdapter = clienteAdapter;
        this.usuarioAdapter = usuarioAdapter;
    }

    @Override
    public ClienteObservacao toEntity(ClienteObservacaoDTO dto) {
        ClienteObservacao obj = new ClienteObservacao();
        obj = toEntity(dto, obj);
        return obj;
    }

    @Override
    public ClienteObservacao toEntity(ClienteObservacaoDTO dto, ClienteObservacao obj) {
        obj.setCodigo(dto.getCodigo());
        obj.setDataCadastro(dto.getDataCadastro());
        obj.setObservacao(dto.getObservacao());
        obj.setImportante(dto.getImportante() != null && dto.getImportante());
        if (dto.getCliente() != null) {
            obj.setCliente(clienteAdapter.toEntity(dto.getCliente()));
        }
        if (dto.getUsuario() != null) {
            obj.setUsuarioResponsavel(usuarioAdapter.toEntity(dto.getUsuario()));
        }
        return obj;
    }

    @Override
    public ClienteObservacaoDTO toDto(ClienteObservacao obj) {
        ClienteObservacaoDTO dto = new ClienteObservacaoDTO();
        if (obj != null) {
            dto.setCodigo(obj.getCodigo());
            dto.setDataCadastro(obj.getDataCadastro());
            dto.setObservacao(obj.getObservacao());
            dto.setImportante(obj.isImportante());
            if (obj.getCliente() != null) {
                dto.setCliente(clienteAdapter.toDto(obj.getCliente()));
            }
            if (obj.getUsuarioResponsavel() != null) {
                dto.setUsuario(usuarioAdapter.toDto(obj.getUsuarioResponsavel()));
            }
            try {
                String msg = Util.trocarAcentuacaoHTMLPorAcentuacao(dto.getObservacao());
                String msgReplace = msg.replaceAll("(?s)<[^>]*>(\\s*<[^>]*>)*", " ").replaceAll("&nbsp;", "").replaceAll("Untitled document", "").replaceAll("\n", " ");
                dto.setObservacaoSemHTML(Util.removerEspacosInicioFimString(msgReplace));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return dto;
        }
        return null;
    }

}
