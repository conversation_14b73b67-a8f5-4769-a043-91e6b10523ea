package com.pacto.adm.core.adapters.auladesmarcada;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ClienteAdapter;
import com.pacto.adm.core.adapters.ContratoAdapter;
import com.pacto.adm.core.adapters.HorarioTurmaAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.adapters.operacaocoletiva.OperacaoColetivaAdapter;
import com.pacto.adm.core.adapters.reposicao.ReposicaoAdapter;
import com.pacto.adm.core.adapters.turma.TurmaAdapter;
import com.pacto.adm.core.dto.auladesmarcada.AulaDesmarcadaDTO;
import com.pacto.adm.core.entities.contrato.AulaDesmarcada;
import org.springframework.stereotype.Component;

@Component
public class AulaDesmarcadaAdapter implements AdapterInterface<AulaDesmarcada, AulaDesmarcadaDTO> {

    private final ContratoAdapter contratoAdapter;
    private final ReposicaoAdapter reposicaoAdapter;
    private final OperacaoColetivaAdapter operacaoColetivaAdapter;
    private final TurmaAdapter turmaAdapter;
    private final ClienteAdapter clienteAdapter;
    private final HorarioTurmaAdapter horarioTurmaAdapter;
    private final UsuarioAdapter usuarioAdapter;
    private final EmpresaAdapter empresaAdapter;

    public AulaDesmarcadaAdapter(
            ContratoAdapter contratoAdapter, ReposicaoAdapter reposicaoAdapter,
            OperacaoColetivaAdapter operacaoColetivaAdapter, TurmaAdapter turmaAdapter,
            ClienteAdapter clienteAdapter, HorarioTurmaAdapter horarioTurmaAdapter,
            UsuarioAdapter usuarioAdapter, EmpresaAdapter empresaAdapter
    ) {
        this.contratoAdapter = contratoAdapter;
        this.reposicaoAdapter = reposicaoAdapter;
        this.operacaoColetivaAdapter = operacaoColetivaAdapter;
        this.turmaAdapter = turmaAdapter;
        this.clienteAdapter = clienteAdapter;
        this.horarioTurmaAdapter = horarioTurmaAdapter;
        this.usuarioAdapter = usuarioAdapter;
        this.empresaAdapter = empresaAdapter;
    }

    @Override
    public AulaDesmarcadaDTO toDto(AulaDesmarcada aulaDesmarcada) {
        AulaDesmarcadaDTO aulaDesmarcadaDTO = new AulaDesmarcadaDTO();
        aulaDesmarcadaDTO.setCodigo(aulaDesmarcada.getCodigo());
        aulaDesmarcadaDTO.setPermiteReporAulaDesmarcada(aulaDesmarcada.getPermiteReporAulaDesmarcada());
        aulaDesmarcadaDTO.setDataLancamento(aulaDesmarcada.getDataLancamento());
        aulaDesmarcadaDTO.setDataOrigem(aulaDesmarcada.getDataOrigem());
        aulaDesmarcadaDTO.setDataReposicao(aulaDesmarcada.getDataReposicao());
        aulaDesmarcadaDTO.setOrigemSistema(aulaDesmarcada.getOrigemSistema());
        aulaDesmarcadaDTO.setDesmarcadaPorAfastamento(aulaDesmarcada.getDesmarcadaPorAfastamento());
        aulaDesmarcadaDTO.setJustificativa(aulaDesmarcada.getJustificativa());
        if (aulaDesmarcada.getReposicao() != null) {
            aulaDesmarcadaDTO.setReposicao(reposicaoAdapter.toDto(aulaDesmarcada.getReposicao()));
        }
        if (aulaDesmarcada.getOperacaoColetiva() != null) {
            aulaDesmarcadaDTO.setOperacaoColetiva(operacaoColetivaAdapter.toDto(aulaDesmarcada.getOperacaoColetiva()));
        }
        if (aulaDesmarcada.getTurmaDestino() != null) {
            aulaDesmarcadaDTO.setTurmaDestino(turmaAdapter.toDto(aulaDesmarcada.getTurmaDestino()));
        }
        if (aulaDesmarcada.getContratoAnterior() != null) {
            aulaDesmarcadaDTO.setContratoAnterior(contratoAdapter.toDto(aulaDesmarcada.getContratoAnterior()));
        }
        if (aulaDesmarcada.getContrato() != null) {
            aulaDesmarcadaDTO.setContrato(contratoAdapter.toDto(aulaDesmarcada.getContrato()));
        }
        if (aulaDesmarcada.getCliente() != null) {
            aulaDesmarcadaDTO.setCliente(clienteAdapter.toDto(aulaDesmarcada.getCliente()));
        }
        if (aulaDesmarcada.getTurma() != null) {
            aulaDesmarcadaDTO.setTurma(turmaAdapter.toDto(aulaDesmarcada.getTurma()));
        }
        if (aulaDesmarcada.getHorarioTurma() != null) {
            aulaDesmarcadaDTO.setHorarioTurma(horarioTurmaAdapter.toDto(aulaDesmarcada.getHorarioTurma()));
        }
        if (aulaDesmarcada.getUsuario() != null) {
            aulaDesmarcadaDTO.setUsuario(usuarioAdapter.toDto(aulaDesmarcada.getUsuario()));
        }
        if (aulaDesmarcada.getEmpresa() != null) {
            aulaDesmarcadaDTO.setEmpresa(empresaAdapter.toDto(aulaDesmarcada.getEmpresa()));
        }

        return aulaDesmarcadaDTO;
    }

    @Override
    public AulaDesmarcada toEntity(AulaDesmarcadaDTO aulaDesmarcadaDTO) {
        AulaDesmarcada aulaDesmarcada = new AulaDesmarcada();
        aulaDesmarcada.setCodigo(aulaDesmarcadaDTO.getCodigo());
        aulaDesmarcada.setPermiteReporAulaDesmarcada(aulaDesmarcadaDTO.getPermiteReporAulaDesmarcada());
        aulaDesmarcada.setDataLancamento(aulaDesmarcadaDTO.getDataLancamento());
        aulaDesmarcada.setDataOrigem(aulaDesmarcadaDTO.getDataOrigem());
        aulaDesmarcada.setDataReposicao(aulaDesmarcadaDTO.getDataReposicao());
        aulaDesmarcada.setOrigemSistema(aulaDesmarcadaDTO.getOrigemSistema());
        aulaDesmarcada.setDesmarcadaPorAfastamento(aulaDesmarcadaDTO.getDesmarcadaPorAfastamento());
        aulaDesmarcada.setJustificativa(aulaDesmarcadaDTO.getJustificativa());
        if (aulaDesmarcadaDTO.getReposicao() != null) {
            aulaDesmarcada.setReposicao(reposicaoAdapter.toEntity(aulaDesmarcadaDTO.getReposicao()));
        }
        if (aulaDesmarcadaDTO.getOperacaoColetiva() != null) {
            aulaDesmarcada.setOperacaoColetiva(operacaoColetivaAdapter.toEntity(aulaDesmarcadaDTO.getOperacaoColetiva()));
        }
        if (aulaDesmarcadaDTO.getTurmaDestino() != null) {
            aulaDesmarcada.setTurmaDestino(turmaAdapter.toEntity(aulaDesmarcadaDTO.getTurmaDestino()));
        }
        if (aulaDesmarcadaDTO.getContratoAnterior() != null) {
            aulaDesmarcada.setContratoAnterior(contratoAdapter.toEntity(aulaDesmarcadaDTO.getContratoAnterior()));
        }
        if (aulaDesmarcadaDTO.getContrato() != null) {
            aulaDesmarcada.setContrato(contratoAdapter.toEntity(aulaDesmarcadaDTO.getContrato()));
        }
        if (aulaDesmarcadaDTO.getCliente() != null) {
            aulaDesmarcada.setCliente(clienteAdapter.toEntity(aulaDesmarcadaDTO.getCliente()));
        }
        if (aulaDesmarcadaDTO.getTurma() != null) {
            aulaDesmarcada.setTurma(turmaAdapter.toEntity(aulaDesmarcadaDTO.getTurma()));
        }
        if (aulaDesmarcadaDTO.getHorarioTurma() != null) {
            aulaDesmarcada.setHorarioTurma(horarioTurmaAdapter.toEntity(aulaDesmarcadaDTO.getHorarioTurma()));
        }
        if (aulaDesmarcadaDTO.getUsuario() != null) {
            aulaDesmarcada.setUsuario(usuarioAdapter.toEntity(aulaDesmarcadaDTO.getUsuario()));
        }
        if (aulaDesmarcadaDTO.getEmpresa() != null) {
            aulaDesmarcada.setEmpresa(empresaAdapter.toEntity(aulaDesmarcadaDTO.getEmpresa()));
        }

        return aulaDesmarcada;
    }
}
