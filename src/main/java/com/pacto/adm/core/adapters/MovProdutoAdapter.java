package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.adapters.recibodevolucao.ReciboDevolucaoAdapter;
import com.pacto.adm.core.dto.MovProdutoDTO;
import com.pacto.adm.core.dto.VendaAvulsaDTO;
import com.pacto.adm.core.entities.MovProduto;
import com.pacto.adm.core.enumerador.TipoProdutoEnum;
import com.pacto.adm.core.enumerador.TipoVigenciaEnum;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MovProdutoAdapter implements AdapterInterface<MovProduto, MovProdutoDTO> {

    @Autowired
    private ProdutoAdapter produtoAdapter;
    @Autowired
    private EmpresaAdapter empresaAdapter;
    @Autowired
    private ReciboDevolucaoAdapter reciboDevolucaoAdapter;
    @Autowired
    private ContratoAdapter contratoAdapter;

    @Override
    public MovProduto toEntity(MovProdutoDTO movProdutoDTO) {
        MovProduto movProduto = new MovProduto();
        movProduto = toEntity(movProdutoDTO, movProduto);
        return movProduto;
    }

    @Override
    public MovProduto toEntity(MovProdutoDTO movProdutoDTO, MovProduto movProduto) {
        movProduto.setCodigo(movProdutoDTO.getCodigo());
        movProduto.setContrato(contratoAdapter.toEntity(movProdutoDTO.getContrato()));
        movProduto.setDescricao(movProdutoDTO.getDescricao());
        movProduto.setQuantidade(movProdutoDTO.getQuantidade());
        movProduto.setPrecoUnitario(movProdutoDTO.getPrecoUnitario());
        movProduto.setValorDesconto(movProdutoDTO.getValorDesconto());
        movProduto.setTotalFinal(movProdutoDTO.getTotalFinal());
        movProduto.setSituacao(movProdutoDTO.getSituacao());
        movProduto.setDataLancamento(movProdutoDTO.getDataLancamento());
        movProduto.setDataInicioVigencia(movProdutoDTO.getDataInicioVigencia());
        movProduto.setDataFinalVigencia(movProdutoDTO.getDataFinalVigencia());
        movProduto.setProduto(produtoAdapter.toEntity(movProdutoDTO.getProduto()));
        movProduto.setValorParcialmentePago(movProdutoDTO.getValorParcialmentePago());
        if (movProdutoDTO.getEmpresa() != null) {
            movProduto.setEmpresa(empresaAdapter.toEntity(movProdutoDTO.getEmpresa()));
        }
        if (movProdutoDTO.getReciboDevolucao() != null) {
            movProduto.setReciboDevolucao(reciboDevolucaoAdapter.toEntity(movProdutoDTO.getReciboDevolucao()));
        }
        return movProduto;
    }

    @Override
    public MovProdutoDTO toDto(MovProduto movProduto) {
        MovProdutoDTO movProdutoDTO = new MovProdutoDTO();
        movProdutoDTO.setCodigo(movProduto.getCodigo());
        movProdutoDTO.setContrato(contratoAdapter.toDto(movProduto.getContrato()));
        movProdutoDTO.setDescricao(movProduto.getDescricao());
        movProdutoDTO.setQuantidade(movProduto.getQuantidade());
        movProdutoDTO.setPrecoUnitario(movProduto.getPrecoUnitario());
        movProdutoDTO.setValorDesconto(movProduto.getValorDesconto());
        movProdutoDTO.setTotalFinal(movProduto.getTotalFinal());
        movProdutoDTO.setSituacao(movProduto.getSituacao());
        movProdutoDTO.setDataLancamento(movProduto.getDataLancamento());
        movProdutoDTO.setDataInicioVigencia(movProduto.getDataInicioVigencia());
        movProdutoDTO.setDataFinalVigencia(movProduto.getDataFinalVigencia());
        movProdutoDTO.setProduto(produtoAdapter.toDto(movProduto.getProduto()));
        movProdutoDTO.setValorParcialmentePago(movProduto.getValorParcialmentePago());
        movProdutoDTO.setRenovavelAutomaticamente(movProduto.isRenovavelAutomaticamente());
        try {
            boolean comVigencia = ((movProduto.getProduto().getTipoVigencia() != null &&
                    movProduto.getProduto().getTipoVigencia().equals(TipoVigenciaEnum.INTERVALO_DIAS.getCodigo()) ||
                    (movProduto.getProduto().getTipoVigencia() != null &&
                            movProduto.getProduto().getTipoVigencia().equals(TipoVigenciaEnum.VIGENCIA_VARIAVEL.getCodigo()))) &&
                    (movProduto.getProduto().getTipoProduto().equals(TipoProdutoEnum.SERVICO.getCodigo()) ||
                            movProduto.getProduto().getTipoProduto().equals(TipoProdutoEnum.ATESTADO.getCodigo())) ||
                    movProduto.getProduto().getTipoProduto().equals(TipoProdutoEnum.DESAFIO.getCodigo()));
            movProdutoDTO.setComVigencia(comVigencia);
        } catch (Exception ex) {
            ex.printStackTrace();
            movProdutoDTO.setComVigencia(false);
        }

        if (movProduto.getEmpresa() != null) {
            movProdutoDTO.setEmpresa(empresaAdapter.toDto(movProduto.getEmpresa()));
        }
        if (movProduto.getReciboDevolucao() != null) {
            movProdutoDTO.setReciboDevolucao(reciboDevolucaoAdapter.toDto(movProduto.getReciboDevolucao()));
        }

        if(movProduto.getVendaAvulsa() != null && !UteisValidacao.emptyNumber(movProduto.getVendaAvulsa().getCodigo())) {
            VendaAvulsaDTO vendaAvulsaDTO = new VendaAvulsaDTO();
            vendaAvulsaDTO.setCodigo(movProduto.getVendaAvulsa().getCodigo());
            movProdutoDTO.setVendaAvulsa(vendaAvulsaDTO);
        }
        return movProdutoDTO;
    }
}
