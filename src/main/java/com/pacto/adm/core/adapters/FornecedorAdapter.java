package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.FornecedorDTO;
import com.pacto.adm.core.entities.Fornecedor;
import org.springframework.stereotype.Component;

@Component
public class FornecedorAdapter implements AdapterInterface<Fornecedor, FornecedorDTO> {

    public FornecedorAdapter() {
    }

    @Override
    public Fornecedor toEntity(FornecedorDTO dto) {
        Fornecedor obj = new Fornecedor();
        obj = toEntity(dto, obj);
        return obj;
    }

    @Override
    public Fornecedor toEntity(FornecedorDTO dto, Fornecedor obj) {
        obj.setCodigo(dto.getCodigo());
        return obj;
    }

    @Override
    public FornecedorDTO toDto(Fornecedor obj) {
        FornecedorDTO dto = new FornecedorDTO();
        if (obj != null) {
            dto.setCodigo(obj.getCodigo());
            dto.setNome(obj.getPessoa().getNome());
            return dto;
        }
        return null;
    }

}
