package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.LiberacaoAcessoDTO;
import com.pacto.adm.core.entities.LiberacaoAcesso;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LiberacaoAcessoAdapter implements AdapterInterface<LiberacaoAcesso, LiberacaoAcessoDTO> {

    @Autowired
    LocalDeAcessoAdapter localDeAcessoAdapter;
    @Autowired
    ColetorAdapter coletorAdapter;
    @Autowired
    UsuarioAdapter usuarioAdapter;
    @Autowired
    PessoaAdapter pessoaAdapter;

    @Override
    public LiberacaoAcesso toEntity(LiberacaoAcessoDTO dto) {
        if (dto != null) {
            LiberacaoAcesso obj = new LiberacaoAcesso();
            obj.setCodigo(dto.getCodigo());
            obj.setPessoa(pessoaAdapter.toEntity(dto.getPessoa()));
            obj.setTipoLiberacao(dto.getTipoLiberacao());
            obj.setSentido(dto.getSentido());
            obj.setLocalAcesso(localDeAcessoAdapter.toEntity(dto.getLocalAcesso()));
            obj.setColetor(coletorAdapter.toEntity(dto.getColetor()));
            obj.setUsuario(usuarioAdapter.toEntity(dto.getUsuario()));
            obj.setDataHora(dto.getDataHora());
            obj.setJustificativa(dto.getJustificativa());
            obj.setDataHoraJustificativa(dto.getDataHoraJustificativa());
            obj.setUsuarioJustificou(usuarioAdapter.toEntity(dto.getUsuarioJustificou()));
            obj.setNomeGenerico(dto.getNomeGenerico());
            return obj;
        }
        return null;
    }

    @Override
    public LiberacaoAcessoDTO toDto(LiberacaoAcesso obj) {
        if (obj != null) {
            LiberacaoAcessoDTO dto = new LiberacaoAcessoDTO();
            dto.setCodigo(obj.getCodigo());
            dto.setPessoa(pessoaAdapter.toDto(obj.getPessoa()));
            dto.setTipoLiberacao(obj.getTipoLiberacao());
            dto.setSentido(obj.getSentido());
            dto.setLocalAcesso(localDeAcessoAdapter.toDto(obj.getLocalAcesso()));
            dto.setColetor(coletorAdapter.toDto(obj.getColetor()));
            dto.setUsuario(usuarioAdapter.toDto(obj.getUsuario()));
            dto.setDataHora(obj.getDataHora());
            dto.setJustificativa(obj.getJustificativa());
            dto.setDataHoraJustificativa(obj.getDataHoraJustificativa());
            dto.setUsuarioJustificou(usuarioAdapter.toDto(obj.getUsuarioJustificou()));
            dto.setNomeGenerico(obj.getNomeGenerico());
            return dto;
        }
        return null;
    }
}
