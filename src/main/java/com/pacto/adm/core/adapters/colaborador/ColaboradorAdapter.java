package com.pacto.adm.core.adapters.colaborador;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.PessoaAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.entities.Colaborador;
import org.hibernate.LazyInitializationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ColaboradorAdapter implements AdapterInterface<Colaborador, ColaboradorDTO> {

    private final Logger LOG = LoggerFactory.getLogger(ColaboradorAdapter.class);

    private final PessoaAdapter pessoaAdapter;
    private final EmpresaAdapter empresaAdapter;
    private final TipoColaboradorAdapter tipoColaboradorAdapter;

    public ColaboradorAdapter(PessoaAdapter pessoaAdapter, EmpresaAdapter empresaAdapter, TipoColaboradorAdapter tipoColaboradorAdapter) {
        this.pessoaAdapter = pessoaAdapter;
        this.empresaAdapter = empresaAdapter;
        this.tipoColaboradorAdapter = tipoColaboradorAdapter;
    }

    @Override
    public ColaboradorDTO toDto(Colaborador colaborador) {
        if (colaborador == null) {
            return null;
        }
        ColaboradorDTO colaboradorDTO = new ColaboradorDTO(
                colaborador.getCodigo(),
                colaborador.getSituacao(),
                pessoaAdapter.toDto(colaborador.getPessoa()),
                empresaAdapter.toDto(colaborador.getEmpresa())
        );
        if (colaborador.getTiposColaborador() != null) {
            try {
                colaboradorDTO.setTiposColaborador(tipoColaboradorAdapter.toDtos(colaborador.getTiposColaborador()));
            } catch (LazyInitializationException e) {
                LOG.info("Não foi possível converter os tipos de colaborador para entity devido a não ter sido inicializado na consulta.");
            }
        }
        return colaboradorDTO;
    }

    @Override
    public Colaborador toEntity(ColaboradorDTO colaboradorDTO) {
        if (colaboradorDTO == null) {
            return null;
        }
        Colaborador colaborador = new Colaborador(
                colaboradorDTO.getCodigo(),
                colaboradorDTO.getSituacao(),
                pessoaAdapter.toEntity(colaboradorDTO.getPessoa()),
                empresaAdapter.toEntity(colaboradorDTO.getEmpresa())
        );
        if (colaboradorDTO.getTiposColaborador() != null) {
            colaborador.setTiposColaborador(tipoColaboradorAdapter.toEntities(colaboradorDTO.getTiposColaborador()));
        }

        return colaborador;
    }
}
