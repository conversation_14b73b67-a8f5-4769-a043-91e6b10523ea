package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoDelsoftDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoDelsoftAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoDelsoftDTO> {

    @Override
    public ConfiguracaoIntegracaoDelsoftDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoDelsoftDTO configDTO = new ConfiguracaoIntegracaoDelsoftDTO();
            configDTO.setUtilizaIntegracaoDelsoft(empresa.getUtilizaIntegracaoDelsoft());
            configDTO.setHostIntegracaoDelsoft(empresa.getHostIntegracaoDelsoft());
            configDTO.setPortaIntegracaoDelsoft(empresa.getPortaIntegracaoDelsoft());
            configDTO.setTokenIntegracaoDelsoft(empresa.getTokenIntegracaoDelsoft());
            configDTO.setNomeAplicacaoDelsoft(empresa.getNomeAplicacaoDelsoft());
            configDTO.setUsuarioAplicacaoDelsoft(empresa.getUsuarioAplicacaoDelsoft());
            configDTO.setSenhaAplicacaoDelsoft(empresa.getSenhaAplicacaoDelsoft());
            configDTO.setPlanoAplicacaoDelsoft(empresa.getPlanoAplicacaoDelsoft());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configDTO.setEmpresa(empresaDTO);
            return configDTO;
        }
        return null;
    }

    public Empresa toEntity(ConfiguracaoIntegracaoDelsoftDTO configDTO, Empresa empresa) {
        if (configDTO != null && configDTO.getEmpresa() != null) {
            try {
                empresa.setUtilizaIntegracaoDelsoft(configDTO.getUtilizaIntegracaoDelsoft());
                empresa.setHostIntegracaoDelsoft(configDTO.getHostIntegracaoDelsoft());
                empresa.setPortaIntegracaoDelsoft(configDTO.getPortaIntegracaoDelsoft());
                empresa.setTokenIntegracaoDelsoft(configDTO.getTokenIntegracaoDelsoft());
                empresa.setNomeAplicacaoDelsoft(configDTO.getNomeAplicacaoDelsoft());
                empresa.setUsuarioAplicacaoDelsoft(configDTO.getUsuarioAplicacaoDelsoft());
                empresa.setSenhaAplicacaoDelsoft(configDTO.getSenhaAplicacaoDelsoft());
                empresa.setPlanoAplicacaoDelsoft(configDTO.getPlanoAplicacaoDelsoft());
                return empresa;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
