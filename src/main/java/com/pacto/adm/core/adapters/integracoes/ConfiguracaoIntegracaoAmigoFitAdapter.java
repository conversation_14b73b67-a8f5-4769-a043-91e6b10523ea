package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoAmigoFitDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoAmigoFitAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoAmigoFitDTO> {

    @Override
    public ConfiguracaoIntegracaoAmigoFitDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoAmigoFitDTO configuracaoIntegracaoAmigoFitDTO = new ConfiguracaoIntegracaoAmigoFitDTO();
            configuracaoIntegracaoAmigoFitDTO.setHabilitada(empresa.isIntegracaoAmigoFitHabilitada());
            configuracaoIntegracaoAmigoFitDTO.setNomeUsuarioAmigoFit(empresa.getNomeUsuarioAmigoFit());
            configuracaoIntegracaoAmigoFitDTO.setSenhaUsuarioAmigoFit(empresa.getSenhaUsuarioAmigoFit());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configuracaoIntegracaoAmigoFitDTO.setEmpresa(empresaDTO);
            return configuracaoIntegracaoAmigoFitDTO;
        }
        return null;
    }

    public Empresa toEntity(ConfiguracaoIntegracaoAmigoFitDTO configAmigoFitDTO, Empresa empresa) {
        if (configAmigoFitDTO != null && configAmigoFitDTO.getEmpresa() != null) {
            try {
                empresa.setIntegracaoAmigoFitHabilitada(configAmigoFitDTO.isHabilitada());
                empresa.setNomeUsuarioAmigoFit(configAmigoFitDTO.getNomeUsuarioAmigoFit());
                empresa.setSenhaUsuarioAmigoFit(configAmigoFitDTO.getSenhaUsuarioAmigoFit());
                return empresa;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
