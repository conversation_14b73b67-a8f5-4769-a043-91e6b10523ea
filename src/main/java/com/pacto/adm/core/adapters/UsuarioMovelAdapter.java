package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.colaborador.ColaboradorAdapter;
import com.pacto.adm.core.dto.UsuarioMovelDTO;
import com.pacto.adm.core.entities.UsuarioMovel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UsuarioMovelAdapter implements AdapterInterface<UsuarioMovel, UsuarioMovelDTO> {

    @Autowired
    private ClienteAdapter clienteAdapter;
    @Autowired
    private ColaboradorAdapter colaboradorAdapter;

    @Override
    public UsuarioMovel toEntity(UsuarioMovelDTO dto) {
        UsuarioMovel obj = new UsuarioMovel();
        obj = toEntity(dto, obj);
        return obj;
    }

    @Override
    public UsuarioMovel toEntity(UsuarioMovelDTO dto, UsuarioMovel obj) {
        obj.setCodigo(dto.getCodigo());
        obj.setAtivo(dto.getAtivo());
        obj.setNome(dto.getNome());
        obj.setCpf(dto.getCpf());
        return obj;
    }

    @Override
    public UsuarioMovelDTO toDto(UsuarioMovel obj) {
        if (obj == null) {
            return null;
        }
        UsuarioMovelDTO dto = new UsuarioMovelDTO();
        dto.setCodigo(obj.getCodigo());
        dto.setAtivo(obj.getAtivo());
        dto.setNome(obj.getNome());
        dto.setCpf(obj.getCpf());

        if (obj.getCliente() != null) {
            dto.setCliente(clienteAdapter.toDto(obj.getCliente()));
        }
        if (obj.getColaborador() != null) {
            dto.setColaborador(colaboradorAdapter.toDto(obj.getColaborador()));
        }
        return dto;
    }
}
