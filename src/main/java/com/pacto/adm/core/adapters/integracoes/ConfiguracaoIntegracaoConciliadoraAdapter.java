package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoConciliadoraDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoConciliadoraAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoConciliadoraDTO> {

    @Override
    public ConfiguracaoIntegracaoConciliadoraDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoConciliadoraDTO configuracaoIntegracaoConciliadoraDTO = new ConfiguracaoIntegracaoConciliadoraDTO();
            configuracaoIntegracaoConciliadoraDTO.setEmpresaConciliadora(empresa.getEmpresaConciliadora());
            configuracaoIntegracaoConciliadoraDTO.setUsarConciliadora(empresa.isUsarConciliadora());
            configuracaoIntegracaoConciliadoraDTO.setSenhaConciliadora(empresa.getSenhaConciliadora());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configuracaoIntegracaoConciliadoraDTO.setEmpresa(empresaDTO);
            return configuracaoIntegracaoConciliadoraDTO;
        }
        return null;
    }
}
