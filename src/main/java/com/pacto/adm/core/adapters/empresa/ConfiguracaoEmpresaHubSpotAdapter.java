package com.pacto.adm.core.adapters.empresa;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaHubSpotDTO;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaHubSpot;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoEmpresaHubSpotAdapter implements AdapterInterface<ConfiguracaoEmpresaHubSpot, ConfiguracaoEmpresaHubSpotDTO> {

    public ConfiguracaoEmpresaHubSpotDTO toDto(ConfiguracaoEmpresaHubSpot configEntity) {
        ConfiguracaoEmpresaHubSpotDTO configDTO = new ConfiguracaoEmpresaHubSpotDTO();
        configDTO.setCodigo(configEntity.getCodigo());
        configDTO.setClientSecret(configEntity.getClientSecret());
        configDTO.setEmpresaUsaHub(configEntity.isEmpresaUsaHub());
        configDTO.setHoraExpiracao(configEntity.getHoraExpiracao());
        configDTO.setUrl_instalacao(configEntity.getUrl_instalacao());
        configDTO.setUrl_redirect(configEntity.getUrl_redirect());
        configDTO.setClientId(configEntity.getClientId());
        configDTO.setAppId(configEntity.getAppId());
        configDTO.setToken(configEntity.getToken());
        configDTO.setAcaoObjecao(configEntity.getAcaoObjecao());
        configDTO.setHoraLimite(configEntity.getHoraLimite());
        if (configEntity.getResponsavelPadrao() != null) {
            UsuarioDTO responsavelPadrao = new UsuarioDTO();
            responsavelPadrao.setCodigo(configEntity.getResponsavelPadrao().getCodigo());
            responsavelPadrao.setNome(configEntity.getResponsavelPadrao().getNome());
            configDTO.setResponsavelPadrao(responsavelPadrao);
        }
        if (configEntity.getEmpresa() != null) {
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(configEntity.getEmpresa().getCodigo());
            empresaDTO.setNome(configEntity.getEmpresa().getNome());
            configDTO.setEmpresa(empresaDTO);
        }
        return configDTO;
    }

    @Override
    public ConfiguracaoEmpresaHubSpot toEntity(ConfiguracaoEmpresaHubSpotDTO configDTO) {
        if (configDTO != null) {
            ConfiguracaoEmpresaHubSpot configEntity = new ConfiguracaoEmpresaHubSpot();
            configEntity.setCodigo(configDTO.getCodigo());
            configEntity.setClientSecret(configDTO.getClientSecret());
            configEntity.setEmpresaUsaHub(configDTO.getEmpresaUsaHub());
            configEntity.setHoraExpiracao(configDTO.getHoraExpiracao());
            configEntity.setUrl_instalacao(configDTO.getUrl_instalacao());
            configEntity.setUrl_redirect(configDTO.getUrl_redirect());
            configEntity.setClientId(configDTO.getClientId());
            configEntity.setAppId(configDTO.getAppId());
            configEntity.setToken(configDTO.getToken());
            configEntity.setAcaoObjecao(configDTO.getAcaoObjecao());
            configEntity.setHoraLimite(configDTO.getHoraLimite());
            if (configDTO.getResponsavelPadrao() != null) {
                Usuario usuarioEntity = new Usuario();
                usuarioEntity.setCodigo(configDTO.getResponsavelPadrao().getCodigo());
                usuarioEntity.setNome(configDTO.getResponsavelPadrao().getNome());
                configEntity.setResponsavelPadrao(usuarioEntity);
            }
            if (configDTO.getEmpresa() != null) {
                Empresa empresaEntity = new Empresa();
                empresaEntity.setCodigo(configDTO.getEmpresa().getCodigo());
                empresaEntity.setNome(configDTO.getEmpresa().getNome());
                configEntity.setEmpresa(empresaEntity);
            }
            return configEntity;
        }
        return null;
    }
}
