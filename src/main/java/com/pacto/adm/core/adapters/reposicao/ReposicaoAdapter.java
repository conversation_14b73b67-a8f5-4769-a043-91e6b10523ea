package com.pacto.adm.core.adapters.reposicao;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ClienteAdapter;
import com.pacto.adm.core.adapters.ContratoAdapter;
import com.pacto.adm.core.adapters.HorarioTurmaAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.adapters.conviteaulaexperimental.ConviteAulaExperimentalAdapter;
import com.pacto.adm.core.adapters.turma.TurmaAdapter;
import com.pacto.adm.core.dto.reposicao.ReposicaoDTO;
import com.pacto.adm.core.entities.reposicao.Reposicao;
import org.springframework.stereotype.Component;

@Component
public class ReposicaoAdapter implements AdapterInterface<Reposicao, ReposicaoDTO> {

    private final ClienteAdapter clienteAdapter;
    private final ContratoAdapter contratoAdapter;
    private final ConviteAulaExperimentalAdapter conviteAulaExperimentalAdapter;
    private final HorarioTurmaAdapter horarioTurmaAdapter;
    private final TurmaAdapter turmaAdapter;
    private final UsuarioAdapter usuarioAdapter;

    public ReposicaoAdapter(
            ClienteAdapter clienteAdapter, ContratoAdapter contratoAdapter,
            ConviteAulaExperimentalAdapter conviteAulaExperimentalAdapter, HorarioTurmaAdapter horarioTurmaAdapter,
            TurmaAdapter turmaAdapter, UsuarioAdapter usuarioAdapter
    ) {
        this.clienteAdapter = clienteAdapter;
        this.contratoAdapter = contratoAdapter;
        this.horarioTurmaAdapter = horarioTurmaAdapter;
        this.turmaAdapter = turmaAdapter;
        this.usuarioAdapter = usuarioAdapter;
        this.conviteAulaExperimentalAdapter = conviteAulaExperimentalAdapter;
    }


    @Override
    public ReposicaoDTO toDto(Reposicao reposicao) {
        ReposicaoDTO reposicaoDTO = new ReposicaoDTO();
        reposicaoDTO.setCodigo(reposicao.getCodigo());
        reposicaoDTO.setDataLancamento(reposicao.getDataLancamento());
        reposicaoDTO.setDataOrigem(reposicao.getDataOrigem());
        reposicaoDTO.setDataPresenca(reposicao.getDataPresenca());
        reposicaoDTO.setDataReposicao(reposicao.getDataReposicao());
        reposicaoDTO.setMarcacaoAula(reposicao.getMarcacaoAula());
        reposicaoDTO.setOrigemSistema(reposicao.getOrigemSistema());
        reposicaoDTO.setSpiviEventId(reposicao.getSpiviEventId());
        reposicaoDTO.setSpiviSearId(reposicao.getSpiviSearId());
        if (reposicao.getCliente() != null) {
            reposicaoDTO.setCliente(clienteAdapter.toDto(reposicao.getCliente()));
        }
        if (reposicao.getContrato() != null) {
            reposicaoDTO.setContrato(contratoAdapter.toDto(reposicao.getContrato()));
        }
        if (reposicao.getConviteAulaExperimental() != null) {
            reposicaoDTO.setConviteAulaExperimental(conviteAulaExperimentalAdapter.toDto(reposicao.getConviteAulaExperimental()));
        }
        if (reposicao.getHorarioTurma() != null) {
            reposicaoDTO.setHorarioTurma(horarioTurmaAdapter.toDto(reposicao.getHorarioTurma()));
        }
        if (reposicao.getHorarioTurmaOrigem() != null) {
            reposicaoDTO.setHorarioTurmaOrigem(horarioTurmaAdapter.toDto(reposicao.getHorarioTurmaOrigem()));
        }
        if (reposicao.getTurmaDestino() != null) {
            reposicaoDTO.setTurmaDestino(turmaAdapter.toDto(reposicao.getTurmaDestino()));
        }
        if (reposicao.getTurmaOrigem() != null) {
            reposicaoDTO.setTurmaOrigem(turmaAdapter.toDto(reposicao.getTurmaOrigem()));
        }
        if (reposicao.getUsuario() != null) {
            reposicaoDTO.setUsuario(usuarioAdapter.toDto(reposicao.getUsuario()));
        }
        return reposicaoDTO;
    }

    @Override
    public Reposicao toEntity(ReposicaoDTO reposicaoDTO) {
        Reposicao reposicao = new Reposicao();
        reposicao.setCodigo(reposicaoDTO.getCodigo());
        reposicao.setDataLancamento(reposicaoDTO.getDataLancamento());
        reposicao.setDataOrigem(reposicaoDTO.getDataOrigem());
        reposicao.setDataPresenca(reposicaoDTO.getDataPresenca());
        reposicao.setDataReposicao(reposicaoDTO.getDataReposicao());
        reposicao.setMarcacaoAula(reposicaoDTO.getMarcacaoAula());
        reposicao.setOrigemSistema(reposicaoDTO.getOrigemSistema());
        reposicao.setSpiviEventId(reposicaoDTO.getSpiviEventId());
        reposicao.setSpiviSearId(reposicaoDTO.getSpiviSearId());
        if (reposicaoDTO.getCliente() != null) {
            reposicao.setCliente(clienteAdapter.toEntity(reposicaoDTO.getCliente()));
        }
        if (reposicaoDTO.getContrato() != null) {
            reposicao.setContrato(contratoAdapter.toEntity(reposicaoDTO.getContrato()));
        }
        if (reposicaoDTO.getConviteAulaExperimental() != null) {
            reposicao.setConviteAulaExperimental(conviteAulaExperimentalAdapter.toEntity(reposicaoDTO.getConviteAulaExperimental()));
        }
        if (reposicaoDTO.getHorarioTurma() != null) {
            reposicao.setHorarioTurma(horarioTurmaAdapter.toEntity(reposicaoDTO.getHorarioTurma()));
        }
        if (reposicaoDTO.getHorarioTurmaOrigem() != null) {
            reposicao.setHorarioTurmaOrigem(horarioTurmaAdapter.toEntity(reposicaoDTO.getHorarioTurmaOrigem()));
        }
        if (reposicaoDTO.getTurmaDestino() != null) {
            reposicao.setTurmaDestino(turmaAdapter.toEntity(reposicaoDTO.getTurmaDestino()));
        }
        if (reposicaoDTO.getTurmaOrigem() != null) {
            reposicao.setTurmaOrigem(turmaAdapter.toEntity(reposicaoDTO.getTurmaOrigem()));
        }
        if (reposicaoDTO.getUsuario() != null) {
            reposicao.setUsuario(usuarioAdapter.toEntity(reposicaoDTO.getUsuario()));
        }
        return reposicao;
    }
}
