package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.integracoes.ProdutoParceiroFidelidadeDTO;
import com.pacto.adm.core.entities.ProdutoParceiroFidelidade;
import org.springframework.stereotype.Component;

@Component
public class ProdutoParceiroFidelidadeAdapter implements AdapterInterface<ProdutoParceiroFidelidade, ProdutoParceiroFidelidadeDTO> {

    @Override
    public ProdutoParceiroFidelidadeDTO toDto(ProdutoParceiroFidelidade produtoParceiroFidelidade) {
        if (produtoParceiroFidelidade != null) {
            ProdutoParceiroFidelidadeDTO produtoParceiroFidelidadeDTO = new ProdutoParceiroFidelidadeDTO();
            produtoParceiroFidelidadeDTO.setCodigo(produtoParceiroFidelidade.getCodigo());
            produtoParceiroFidelidadeDTO.setDescricao(produtoParceiroFidelidade.getDescricao());
            produtoParceiroFidelidadeDTO.setPontos(produtoParceiroFidelidade.getPontos());
            produtoParceiroFidelidadeDTO.setValor(produtoParceiroFidelidade.getValor());
            produtoParceiroFidelidadeDTO.setCodigoExterno(produtoParceiroFidelidade.getCodigoExterno());
            produtoParceiroFidelidadeDTO.setParceiroFidelidade(produtoParceiroFidelidade.getParceiroFidelidade().getCodigo());
            return produtoParceiroFidelidadeDTO;
        }
        return null;
    }

    @Override
    public ProdutoParceiroFidelidade toEntity(ProdutoParceiroFidelidadeDTO produtoParceiroFidelidadeDTO) {
        if (produtoParceiroFidelidadeDTO != null) {
            ProdutoParceiroFidelidade produtoParceiroFidelidade = new ProdutoParceiroFidelidade();
            produtoParceiroFidelidade.setCodigo(produtoParceiroFidelidadeDTO.getCodigo());
            produtoParceiroFidelidade.setDescricao(produtoParceiroFidelidadeDTO.getDescricao());
            produtoParceiroFidelidade.setPontos(produtoParceiroFidelidadeDTO.getPontos());
            produtoParceiroFidelidade.setValor(produtoParceiroFidelidadeDTO.getValor());
            produtoParceiroFidelidade.setCodigoExterno(produtoParceiroFidelidadeDTO.getCodigoExterno());
            return produtoParceiroFidelidade;
        }
        return null;
    }
}
