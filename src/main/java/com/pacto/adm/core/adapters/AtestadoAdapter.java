package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.ArquivoDTO;
import com.pacto.adm.core.dto.AtestadoDTO;
import com.pacto.adm.core.entities.Atestado;
import com.pacto.adm.core.enumerador.MidiaEntidadeEnum;
import com.pacto.adm.core.mscomunication.mediams.MediaMs;
import com.pacto.config.security.interfaces.RequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AtestadoAdapter implements AdapterInterface<Atestado, AtestadoDTO> {

    @Autowired
    private ArquivoAdapter arquivoAdapter;
    @Autowired
    private MovProdutoAdapter movProdutoAdapter;
    @Autowired
    private MediaMs mediaMs;
    @Autowired
    private RequestService requestService;

    @Override
    public Atestado toEntity(AtestadoDTO atestadoDTO) {
        if (atestadoDTO != null) {
            Atestado atestado = new Atestado();
            atestado.setCodigo(atestadoDTO.getCodigo());
            atestado.setParqPositivo(atestadoDTO.getParqPositivo());
            atestado.setObservacao(atestadoDTO.getObservacao());
            atestado.setAvaliacaoFisicaTW(atestadoDTO.getAvaliacaoFisicaTW());
            atestado.setArquivo(arquivoAdapter.toEntity(atestadoDTO.getArquivo()));
            atestado.setMovProduto(movProdutoAdapter.toEntity(atestadoDTO.getMovProduto()));
            return atestado;
        }
        return null;
    }

    @Override
    public AtestadoDTO toDto(Atestado atestado) {
        if (atestado != null) {
            AtestadoDTO atestadoDTO = new AtestadoDTO();
            atestadoDTO.setCodigo(atestado.getCodigo());
            atestadoDTO.setParqPositivo(atestado.getParqPositivo());
            atestadoDTO.setObservacao(atestado.getObservacao());
            atestadoDTO.setAvaliacaoFisicaTW(atestado.getAvaliacaoFisicaTW());
            atestadoDTO.setMovProduto(movProdutoAdapter.toDto(atestado.getMovProduto()));
            atestadoDTO.setData(atestado.getDataRegistro() != null ? atestado.getDataRegistro() : atestadoDTO.getMovProduto().getDataLancamento());
            if (atestado.getArquivo() != null) {
                atestadoDTO.setArquivo(arquivoAdapter.toDto(atestado.getArquivo()));
                atestadoDTO.setUrlArquivo(obterUrlArquivoAtestadoAptidao(atestadoDTO.getArquivo()));
            }
            return atestadoDTO;
        }
        return null;
    }

    private String obterUrlArquivoAtestadoAptidao(ArquivoDTO arquivoDTO) {
        try {
            if (arquivoDTO != null) {
                return mediaMs.getUrlArquivo(requestService.getUsuarioAtual().getChave(), arquivoDTO.getCodigo(), arquivoDTO.getExtensao(), MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
