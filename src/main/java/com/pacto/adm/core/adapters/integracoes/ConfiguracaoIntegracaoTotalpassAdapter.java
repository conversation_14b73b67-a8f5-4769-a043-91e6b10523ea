package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoTotalPassDTO;
import com.pacto.adm.core.entities.ConfigTotalPass;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoTotalpassAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoTotalPassDTO> {

    public ConfiguracaoIntegracaoTotalPassDTO toDto(Empresa empresa, ConfigTotalPass configTotalPass) {
        if (empresa != null) {

            ConfiguracaoIntegracaoTotalPassDTO configuracaoIntegracaoTotalPassDTO = new ConfiguracaoIntegracaoTotalPassDTO();
            configuracaoIntegracaoTotalPassDTO.setCodigoTotalpass(configTotalPass.getCodigoTotalPass());
            configuracaoIntegracaoTotalPassDTO.setApiKey(configTotalPass.getApikey());
            configuracaoIntegracaoTotalPassDTO.setInativo(configTotalPass.getInativo());
            configuracaoIntegracaoTotalPassDTO.setPermitirWod(configTotalPass.getPermitirWod());
            configuracaoIntegracaoTotalPassDTO.setLimiteDeAcessosPorDia(configTotalPass.getLimiteDeAcessosPorDia());
            configuracaoIntegracaoTotalPassDTO.setLimiteDeAulasPorDia(configTotalPass.getLimiteDeAulasPorDia());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configuracaoIntegracaoTotalPassDTO.setEmpresa(empresaDTO);
            return configuracaoIntegracaoTotalPassDTO;
        }
        return null;
    }
}
