package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.MovParcelaDTO;
import com.pacto.adm.core.entities.MovParcela;
import com.pacto.adm.core.entities.MovProdutoParcela;
import com.pacto.adm.core.enumerador.SituacaoMovParcelaEnum;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Component;

@Component
public class MovParcelaAdapter implements AdapterInterface<MovParcela, MovParcelaDTO> {

    private final EmpresaAdapter empresaAdapter;

    public MovParcelaAdapter(EmpresaAdapter empresaAdapter) {
        this.empresaAdapter = empresaAdapter;
    }

    @Override
    public MovParcela toEntity(MovParcelaDTO movParcelaDTO) {
        MovParcela movParcela = new MovParcela();
        movParcela = toEntity(movParcelaDTO, movParcela);
        return movParcela;
    }

    @Override
    public MovParcela toEntity(MovParcelaDTO movParcelaDTO, MovParcela movParcela) {
        movParcela.setCodigo(movParcelaDTO.getCodigo());
        movParcela.setContrato(movParcelaDTO.getContrato());
        movParcela.setDescricao(movParcelaDTO.getDescricao());
        movParcela.setValorParcela(movParcelaDTO.getValor());
        movParcela.setSituacao(movParcelaDTO.getSituacao());
        movParcela.setDataVencimento(movParcelaDTO.getDataVencimento());
        movParcela.setDataLancamento(movParcelaDTO.getDataLancamento());

        if (movParcelaDTO.getPessoa() != null) {

        }
        if (movParcelaDTO.getEmpresa() != null) {
            movParcela.setEmpresa(empresaAdapter.toEntity(movParcelaDTO.getEmpresa()));
        }

        return movParcela;
    }

    @Override
    public MovParcelaDTO toDto(MovParcela movParcela) {
        MovParcelaDTO movParcelaDTO = new MovParcelaDTO();
        movParcelaDTO.setCodigo(movParcela.getCodigo());
        movParcelaDTO.setContrato(movParcela.getContrato());
        movParcelaDTO.setDescricao(movParcela.getDescricao());
        movParcelaDTO.setValor(movParcela.getValorParcela());
        movParcelaDTO.setSituacao(movParcela.getSituacao());
        movParcelaDTO.setSituacaoDescricao(SituacaoMovParcelaEnum.obterPorCodigo(movParcela.getSituacao()).getDescricao());
        movParcelaDTO.setDataVencimento(movParcela.getDataVencimento());
        movParcelaDTO.setDataLancamento(movParcela.getDataLancamento());
        movParcelaDTO.setNrTentativas(movParcela.getNrTentativas());

        if (movParcela.getPessoa() != null) {

        }

        if (movParcela.getEmpresa() != null) {
            movParcelaDTO.setEmpresa(empresaAdapter.toDto(movParcela.getEmpresa()));
        }
        if(movParcela.getMovProdutosParcelas().size() >0 ){
            MovProdutoParcela movProdutoParcela = movParcela.getMovProdutosParcelas().iterator().next();
            if(movProdutoParcela.getReciboPagamento()!=null && !UteisValidacao.emptyList(movProdutoParcela.getReciboPagamento().getPagamentos())){
                movParcelaDTO.setDataPagamento(movProdutoParcela.getReciboPagamento().getPagamentos().get(0).getDataPagamento());
            }
        }
        return movParcelaDTO;
    }
}
