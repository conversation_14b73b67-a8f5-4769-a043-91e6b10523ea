package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.AluguelArmarioDTO;
import com.pacto.adm.core.entities.AluguelArmario;
import org.springframework.stereotype.Component;

@Component
public class AluguelArmarioAdapter implements AdapterInterface<AluguelArmario, AluguelArmarioDTO> {

    private final ArmarioAdapter armarioAdapter;
    private final ClienteAdapter clienteAdapter;
    private final UsuarioAdapter usuarioAdapter;
    private final MovProdutoAdapter movProdutoAdapter;

    public AluguelArmarioAdapter(ClienteAdapter clienteAdapter, UsuarioAdapter usuarioAdapter,
                                 MovProdutoAdapter movProdutoAdapter, ArmarioAdapter armarioAdapter) {
        this.clienteAdapter = clienteAdapter;
        this.usuarioAdapter = usuarioAdapter;
        this.movProdutoAdapter = movProdutoAdapter;
        this.armarioAdapter = armarioAdapter;
    }

    @Override
    public AluguelArmario toEntity(AluguelArmarioDTO dto) {
        AluguelArmario obj = new AluguelArmario();
        obj = toEntity(dto, obj);
        return obj;
    }

    @Override
    public AluguelArmario toEntity(AluguelArmarioDTO dto, AluguelArmario obj) {
        obj.setCodigo(dto.getCodigo());
        obj.setDataCadastro(dto.getDataCadastro());
        obj.setValor(dto.getValor());
        obj.setFimOriginal(dto.getFimOriginal());
        obj.setDataRenovacaoAutomatica(dto.getDataRenovacaoAutomatica());
        obj.setContratoAssinado(dto.getContratoAssinado());
        obj.setRenovarAutomatico(dto.getRenovarAutomatico());
        obj.setDataInicio(dto.getDataInicio());
        obj.setChaveDevolvida(dto.getChaveDevolvida());
        if (dto.getArmario() != null) {
            obj.setArmario(armarioAdapter.toEntity(dto.getArmario()));
        }
        if (dto.getCliente() != null) {
            obj.setCliente(clienteAdapter.toEntity(dto.getCliente()));
        }
        if (dto.getResponsavelCadastro() != null) {
            obj.setResponsavelCadastro(usuarioAdapter.toEntity(dto.getResponsavelCadastro()));
        }
        if (dto.getMovProduto() != null) {
            obj.setMovProduto(movProdutoAdapter.toEntity(dto.getMovProduto()));
        }
        return obj;
    }

    @Override
    public AluguelArmarioDTO toDto(AluguelArmario obj) {
        AluguelArmarioDTO dto = new AluguelArmarioDTO();
        if (obj != null) {
            dto.setCodigo(obj.getCodigo());
            dto.setDataCadastro(obj.getDataCadastro());
            dto.setValor(obj.getValor());
            dto.setFimOriginal(obj.getFimOriginal());
            dto.setDataRenovacaoAutomatica(obj.getDataRenovacaoAutomatica());
            dto.setContratoAssinado(obj.getContratoAssinado());
            dto.setRenovarAutomatico(obj.getRenovarAutomatico());
            dto.setDataInicio(obj.getDataInicio());
            dto.setChaveDevolvida(obj.getChaveDevolvida());
            dto.setCodigo(obj.getCodigo());
            dto.setDataCadastro(obj.getDataCadastro());
            if (obj.getArmario() != null) {
                dto.setArmario(armarioAdapter.toDto(obj.getArmario()));
            }
            if (obj.getCliente() != null) {
                dto.setCliente(clienteAdapter.toDto(obj.getCliente()));
            }
            if (obj.getResponsavelCadastro() != null) {
                dto.setResponsavelCadastro(usuarioAdapter.toDto(obj.getResponsavelCadastro()));
            }
            if (obj.getMovProduto() != null) {
                dto.setMovProduto(movProdutoAdapter.toDto(obj.getMovProduto()));
            }
            return dto;
        }
        return null;
    }

}
