package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.FormaPagamentoDTO;
import com.pacto.adm.core.entities.financeiro.FormaPagamento;
import org.springframework.stereotype.Component;


@Component
public class FormaPagamentoAdapter implements AdapterInterface<FormaPagamento, FormaPagamentoDTO> {

    @Override
    public FormaPagamentoDTO toDto(FormaPagamento formaPagamento) {
        FormaPagamentoDTO formaPagamentoDTO = new FormaPagamentoDTO();
        formaPagamentoDTO.setTipoFormaPagamento(formaPagamento.getTipoFormaPagamento());
        formaPagamentoDTO.setCodigo(formaPagamento.getCodigo());
        formaPagamentoDTO.setAtivo(formaPagamento.isAtivo());
        formaPagamentoDTO.setDescricao(formaPagamento.getDescricao());
        return formaPagamentoDTO;
    }

}
