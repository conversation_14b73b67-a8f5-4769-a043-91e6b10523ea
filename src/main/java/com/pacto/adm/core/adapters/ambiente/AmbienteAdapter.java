package com.pacto.adm.core.adapters.ambiente;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.ambiente.AmbienteDTO;
import com.pacto.adm.core.entities.Ambiente;
import org.springframework.stereotype.Component;

@Component
public class AmbienteAdapter implements AdapterInterface<Ambiente, AmbienteDTO> {

    @Override
    public Ambiente toEntity(AmbienteDTO ambienteDTO) {
        return new Ambiente(
                ambienteDTO.getCodigo(),
                ambienteDTO.getDescricao()
        );
    }

    @Override
    public AmbienteDTO toDto(Ambiente ambiente) {
        return new AmbienteDTO(
                ambiente.getCodigo(),
                ambiente.getDescricao()
        );
    }
}
