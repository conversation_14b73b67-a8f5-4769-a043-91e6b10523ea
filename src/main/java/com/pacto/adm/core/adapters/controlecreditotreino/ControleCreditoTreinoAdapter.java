package com.pacto.adm.core.adapters.controlecreditotreino;

import com.pacto.adm.core.adapters.AcessoClienteAdapter;
import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ContratoAdapter;
import com.pacto.adm.core.adapters.HorarioTurmaAdapter;
import com.pacto.adm.core.adapters.ModalidadeAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.adapters.auladesmarcada.AulaDesmarcadaAdapter;
import com.pacto.adm.core.adapters.reposicao.ReposicaoAdapter;
import com.pacto.adm.core.dto.controlecreditotreino.ControleCreditoTreinoDTO;
import com.pacto.adm.core.entities.contrato.ControleCreditoTreino;
import org.springframework.stereotype.Component;

@Component
public class ControleCreditoTreinoAdapter implements AdapterInterface<ControleCreditoTreino, ControleCreditoTreinoDTO> {

    private final AulaDesmarcadaAdapter aulaDesmarcadaAdapter;
    private final HorarioTurmaAdapter horarioTurmaAdapter;
    private final ContratoAdapter contratoAdapter;
    private final UsuarioAdapter usuarioAdapter;
    private final ModalidadeAdapter modalidadeAdapter;
    private final ReposicaoAdapter reposicaoAdapter;
    private final AcessoClienteAdapter acessoClienteAdapter;

    public ControleCreditoTreinoAdapter(
            AulaDesmarcadaAdapter aulaDesmarcadaAdapter, HorarioTurmaAdapter horarioTurmaAdapter,
            ContratoAdapter contratoAdapter, UsuarioAdapter usuarioAdapter, ModalidadeAdapter modalidadeAdapter,
            ReposicaoAdapter reposicaoAdapter, AcessoClienteAdapter acessoClienteAdapter) {
        this.aulaDesmarcadaAdapter = aulaDesmarcadaAdapter;
        this.horarioTurmaAdapter = horarioTurmaAdapter;
        this.contratoAdapter = contratoAdapter;
        this.usuarioAdapter = usuarioAdapter;
        this.modalidadeAdapter = modalidadeAdapter;
        this.reposicaoAdapter = reposicaoAdapter;
        this.acessoClienteAdapter = acessoClienteAdapter;
    }

    @Override
    public ControleCreditoTreinoDTO toDto(ControleCreditoTreino controleCreditoTreino) {
        ControleCreditoTreinoDTO controleCreditoTreinoDTO = new ControleCreditoTreinoDTO();
        controleCreditoTreinoDTO.setCodigo(controleCreditoTreino.getCodigo());
        controleCreditoTreinoDTO.setQuantidade(controleCreditoTreino.getQuantidade());
        controleCreditoTreinoDTO.setDataLancamento(controleCreditoTreino.getDataLancamento());
        controleCreditoTreinoDTO.setDataOperacao(controleCreditoTreino.getDataOperacao());
        controleCreditoTreinoDTO.setObservacao(controleCreditoTreino.getObservacao());
        controleCreditoTreinoDTO.setDescricaoAulaMarcada(controleCreditoTreino.getDescricaoAulaMarcada());
        controleCreditoTreinoDTO.setTipoOperacaoCreditoTreino(controleCreditoTreino.getTipoOperacaoCreditoTreino());
        if (controleCreditoTreino.getAulaDesmarcada() != null) {
            controleCreditoTreinoDTO.setAulaDesmarcada(aulaDesmarcadaAdapter.toDto(controleCreditoTreino.getAulaDesmarcada()));
        }
        if (controleCreditoTreino.getHorarioTurmaFalta() != null) {
            controleCreditoTreinoDTO.setHorarioTurmaFalta(horarioTurmaAdapter.toDto(controleCreditoTreino.getHorarioTurmaFalta()));
        }
        if (controleCreditoTreino.getContratoOrigem() != null) {
            controleCreditoTreinoDTO.setContratoOrigem(contratoAdapter.toDto(controleCreditoTreino.getContratoOrigem()));
        }
        if (controleCreditoTreino.getModalidade() != null) {
            controleCreditoTreinoDTO.setModalidade(modalidadeAdapter.toDto(controleCreditoTreino.getModalidade()));
        }
        if (controleCreditoTreino.getReposicao() != null) {
            controleCreditoTreinoDTO.setReposicao(reposicaoAdapter.toDto(controleCreditoTreino.getReposicao()));
        }
        if (controleCreditoTreino.getAcessoCliente() != null) {
            controleCreditoTreinoDTO.setAcessoCliente(acessoClienteAdapter.toDto(controleCreditoTreino.getAcessoCliente()));
        }
        if (controleCreditoTreino.getUsuario() != null) {
            controleCreditoTreinoDTO.setUsuario(usuarioAdapter.toDto(controleCreditoTreino.getUsuario()));
        }
        if (controleCreditoTreino.getContrato() != null) {
            controleCreditoTreinoDTO.setContrato(contratoAdapter.toDto(controleCreditoTreino.getContrato()));
        }
        controleCreditoTreinoDTO.setCodigoTipoAjusteManualCreditoTreino(controleCreditoTreino.getCodigoTipoAjusteManualCreditoTreino());
        controleCreditoTreinoDTO.setSaldo(controleCreditoTreino.getSaldo());
        controleCreditoTreinoDTO.setDescricaoAulaMarcada(controleCreditoTreino.getDescricaoAulaMarcada());

        return controleCreditoTreinoDTO;
    }

    @Override
    public ControleCreditoTreino toEntity(ControleCreditoTreinoDTO controleCreditoTreinoDTO) {
        ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino();
        controleCreditoTreino.setCodigo(controleCreditoTreinoDTO.getCodigo());
        controleCreditoTreino.setQuantidade(controleCreditoTreinoDTO.getQuantidade());
        controleCreditoTreino.setDataLancamento(controleCreditoTreinoDTO.getDataLancamento());
        controleCreditoTreino.setDataOperacao(controleCreditoTreinoDTO.getDataOperacao());
        controleCreditoTreino.setObservacao(controleCreditoTreinoDTO.getObservacao());
        controleCreditoTreino.setDescricaoAulaMarcada(controleCreditoTreinoDTO.getDescricaoAulaMarcada());
        controleCreditoTreino.setTipoOperacaoCreditoTreino(controleCreditoTreinoDTO.getTipoOperacaoCreditoTreino());
        if (controleCreditoTreinoDTO.getAulaDesmarcada() != null) {
            controleCreditoTreino.setAulaDesmarcada(aulaDesmarcadaAdapter.toEntity(controleCreditoTreinoDTO.getAulaDesmarcada()));
        }
        if (controleCreditoTreinoDTO.getHorarioTurmaFalta() != null) {
            controleCreditoTreino.setHorarioTurmaFalta(horarioTurmaAdapter.toEntity(controleCreditoTreinoDTO.getHorarioTurmaFalta()));
        }
        if (controleCreditoTreinoDTO.getContratoOrigem() != null) {
            controleCreditoTreino.setContratoOrigem(contratoAdapter.toEntity(controleCreditoTreinoDTO.getContratoOrigem()));
        }
        if (controleCreditoTreinoDTO.getModalidade() != null) {
            controleCreditoTreino.setModalidade(modalidadeAdapter.toEntity(controleCreditoTreinoDTO.getModalidade()));
        }
        if (controleCreditoTreinoDTO.getReposicao() != null) {
            controleCreditoTreino.setReposicao(reposicaoAdapter.toEntity(controleCreditoTreinoDTO.getReposicao()));
        }
        if (controleCreditoTreinoDTO.getAcessoCliente() != null) {
            controleCreditoTreino.setAcessoCliente(acessoClienteAdapter.toEntity(controleCreditoTreinoDTO.getAcessoCliente()));
        }
        if (controleCreditoTreinoDTO.getUsuario() != null) {
            controleCreditoTreino.setUsuario(usuarioAdapter.toEntity(controleCreditoTreinoDTO.getUsuario()));
        }
        if (controleCreditoTreinoDTO.getContrato() != null) {
            controleCreditoTreino.setContrato(contratoAdapter.toEntity(controleCreditoTreinoDTO.getContrato()));
        }
        controleCreditoTreino.setCodigoTipoAjusteManualCreditoTreino(controleCreditoTreino.getCodigoTipoAjusteManualCreditoTreino());
        controleCreditoTreino.setSaldo(controleCreditoTreino.getSaldo());
        controleCreditoTreino.setDescricaoAulaMarcada(controleCreditoTreino.getDescricaoAulaMarcada());

        return controleCreditoTreino;
    }
}
