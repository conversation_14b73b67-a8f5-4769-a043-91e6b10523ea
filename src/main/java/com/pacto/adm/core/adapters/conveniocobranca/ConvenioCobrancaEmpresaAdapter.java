package com.pacto.adm.core.adapters.conveniocobranca;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.conveniocobranca.ConvenioCobrancaEmpresaDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.entities.conveniocobranca.ConvenioCobrancaEmpresa;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.empresa.EmpresaCodNome;
import org.springframework.stereotype.Component;

@Component
public class ConvenioCobrancaEmpresaAdapter implements AdapterInterface<ConvenioCobrancaEmpresa, ConvenioCobrancaEmpresaDTO> {

    private final EmpresaAdapter empresaAdapter;

    public ConvenioCobrancaEmpresaAdapter(EmpresaAdapter empresaAdapter) {
        this.empresaAdapter = empresaAdapter;
    }

    @Override
    public ConvenioCobrancaEmpresaDTO toDto(ConvenioCobrancaEmpresa convenioCobrancaEmpresa) {
        if (convenioCobrancaEmpresa == null) {
            return null;
        }
        ConvenioCobrancaEmpresaDTO convenioCobrancaEmpresaDTO = new ConvenioCobrancaEmpresaDTO();
        convenioCobrancaEmpresaDTO.setCodigo(convenioCobrancaEmpresa.getCodigo());
        EmpresaCodNome empresaCodNome = convenioCobrancaEmpresa.getEmpresa();
        Empresa empresa = new Empresa(empresaCodNome.getCodigo(), empresaCodNome.getNome());
        convenioCobrancaEmpresaDTO.setEmpresa(empresaAdapter.toDto(empresa));

        return convenioCobrancaEmpresaDTO;
    }

    @Override
    public ConvenioCobrancaEmpresa toEntity(ConvenioCobrancaEmpresaDTO convenioCobrancaEmpresaDTO) {
        if (convenioCobrancaEmpresaDTO == null) {
            return null;
        }
        ConvenioCobrancaEmpresa convenioCobrancaEmpresa = new ConvenioCobrancaEmpresa();
        convenioCobrancaEmpresa.setCodigo(convenioCobrancaEmpresaDTO.getCodigo());
        EmpresaDTO empresaDTO = new EmpresaDTO(
                convenioCobrancaEmpresaDTO.getEmpresa().getCodigo(),
                convenioCobrancaEmpresaDTO.getEmpresa().getNome()
        );
        Empresa empresa = empresaAdapter.toEntity(empresaDTO);
        EmpresaCodNome empresaCodNome = new EmpresaCodNome();
        empresaCodNome.setCodigo(empresa.getCodigo());
        empresaCodNome.setNome(empresa.getNome());
        convenioCobrancaEmpresa.setEmpresa(empresaCodNome);

        return convenioCobrancaEmpresa;
    }
}
