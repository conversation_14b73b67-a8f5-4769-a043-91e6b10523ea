package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.ComprovanteVacinaDTO;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.contrato.ComprovanteVacina;
import org.springframework.stereotype.Component;

@Component
public class ComprovanteVacinaAdapter implements AdapterInterface<ComprovanteVacina, ComprovanteVacinaDTO> {

    @Override
    public ComprovanteVacina toEntity(ComprovanteVacinaDTO comprovanteVacinaDTO) {
        if (comprovanteVacinaDTO != null) {
            ComprovanteVacina comprovanteVacina = new ComprovanteVacina();
            comprovanteVacina.setCodigo(comprovanteVacinaDTO.getCodigo());
            comprovanteVacina.setTipo(comprovanteVacinaDTO.getTipo());
            comprovanteVacina.setDataAplicacao(comprovanteVacinaDTO.getDataAplicacao());
            comprovanteVacina.setFabricante(comprovanteVacinaDTO.getFabricante());
            comprovanteVacina.setLote(comprovanteVacinaDTO.getLote());
            comprovanteVacina.setEstabelecimentoSaude(comprovanteVacinaDTO.getEstabelecimentoSaude());
            comprovanteVacina.setVacinador(comprovanteVacinaDTO.getVacinador());
            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(comprovanteVacinaDTO.getPessoa());
            comprovanteVacina.setPessoa(pessoa);
            Usuario usuario = new Usuario();
            usuario.setCodigo(comprovanteVacinaDTO.getUsuarioResponsavel());
            comprovanteVacina.setUsuarioResponsavel(usuario);
            return comprovanteVacina;
        }
        return null;
    }

    @Override
    public ComprovanteVacinaDTO toDto(ComprovanteVacina comprovanteVacina) {
        if (comprovanteVacina != null) {
            ComprovanteVacinaDTO comprovanteVacinaDTO = new ComprovanteVacinaDTO();
            comprovanteVacinaDTO.setCodigo(comprovanteVacina.getCodigo());
            comprovanteVacinaDTO.setTipo(comprovanteVacina.getTipo());
            comprovanteVacinaDTO.setDataAplicacao(comprovanteVacina.getDataAplicacao());
            comprovanteVacinaDTO.setFabricante(comprovanteVacina.getFabricante());
            comprovanteVacinaDTO.setLote(comprovanteVacina.getLote());
            comprovanteVacinaDTO.setEstabelecimentoSaude(comprovanteVacina.getEstabelecimentoSaude());
            comprovanteVacinaDTO.setVacinador(comprovanteVacina.getVacinador());
            comprovanteVacinaDTO.setPessoa(comprovanteVacina.getPessoa().getCodigo());
            comprovanteVacinaDTO.setUsuarioResponsavel(comprovanteVacina.getUsuarioResponsavel().getCodigo());
            return comprovanteVacinaDTO;
        }
        return null;
    }
}
