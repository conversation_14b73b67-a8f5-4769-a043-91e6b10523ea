package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoAmigoFitDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoCDLSPCDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoCDLSPCAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoCDLSPCDTO> {

    @Override
    public ConfiguracaoIntegracaoCDLSPCDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoCDLSPCDTO configDTO = new ConfiguracaoIntegracaoCDLSPCDTO();
            configDTO.setConsultarNovoCadastroSPC(empresa.isConsultarNovoCadastroSPC());
            configDTO.setCodigoAssociadoSPC(empresa.getCodigoAssociadoSPC());
            configDTO.setOperadorSPC(empresa.getOperadorSPC());
            configDTO.setSenhaSPC(empresa.getSenhaSPC());
            configDTO.setPesquisaAutomaticaSPC(empresa.isPesquisaAutomaticaSPC());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configDTO.setEmpresa(empresaDTO);
            return configDTO;
        }
        return null;
    }

    public Empresa toEntity(ConfiguracaoIntegracaoCDLSPCDTO configDTO, Empresa empresa) {
        if (configDTO != null && configDTO.getEmpresa() != null) {
            try {
                empresa.setConsultarNovoCadastroSPC(configDTO.isConsultarNovoCadastroSPC());
                empresa.setCodigoAssociadoSPC(configDTO.getCodigoAssociadoSPC());
                empresa.setOperadorSPC(configDTO.getOperadorSPC());
                empresa.setSenhaSPC(configDTO.getSenhaSPC());
                empresa.setConsultarNovoCadastroSPC(configDTO.isConsultarNovoCadastroSPC());
                empresa.setPesquisaAutomaticaSPC(configDTO.isPesquisaAutomaticaSPC());
                return empresa;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
