package com.pacto.adm.core.adapters;


import com.pacto.adm.core.dto.ClienteRestricaoDTO;
import com.pacto.adm.core.entities.ClienteRestricao;
import com.pacto.adm.core.enumerador.clienterestricao.TipoClienteRestricaoEnum;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Component;

@Component
public class ClienteRestricaoAdapter implements AdapterInterface<ClienteRestricao, ClienteRestricaoDTO> {


    @Override
    public ClienteRestricaoDTO toDto(ClienteRestricao clienteRestricao) {
        if (clienteRestricao != null) {
            ClienteRestricaoDTO clienteRestricaoDTO = new ClienteRestricaoDTO();
            clienteRestricaoDTO.setCodigo(clienteRestricao.getCodigo());
            clienteRestricaoDTO.setCodigoMatricula(clienteRestricao.getCodigoMatricula());
            clienteRestricaoDTO.setNome(clienteRestricao.getNome());
            clienteRestricaoDTO.setCpf(Uteis.aplicarMascara(clienteRestricao.getCpf(), "999.999.999-99"));
            clienteRestricaoDTO.setObservacao(clienteRestricao.getObservacao());
            clienteRestricaoDTO.setNomeEmpresa(clienteRestricao.getNomeEmpresa());
            clienteRestricaoDTO.setChaveEmpresa(clienteRestricao.getChaveEmpresa());
            clienteRestricaoDTO.setCodigoEmpresa(clienteRestricao.getCodigoEmpresa());
            clienteRestricaoDTO.setTipo(clienteRestricao.getTipo());
            return clienteRestricaoDTO;
        }
        return null;
    }

    @Override
    public ClienteRestricao toEntity(ClienteRestricaoDTO clienteRestricaoDTO) {
        if (clienteRestricaoDTO != null) {
            ClienteRestricao clienteRestricao = new ClienteRestricao();
            clienteRestricao.setCodigo(clienteRestricaoDTO.getCodigo());
            clienteRestricao.setCodigoEmpresa(clienteRestricaoDTO.getCodigoEmpresa());
            clienteRestricao.setCodigoMatricula(clienteRestricaoDTO.getCodigoMatricula());
            clienteRestricao.setNome(clienteRestricaoDTO.getNome());
            clienteRestricao.setCpf(clienteRestricaoDTO.getCpf());
            clienteRestricao.setObservacao(clienteRestricaoDTO.getObservacao());
            clienteRestricao.setNomeEmpresa(clienteRestricaoDTO.getNomeEmpresa());
            clienteRestricao.setChaveEmpresa(clienteRestricaoDTO.getChaveEmpresa());
            clienteRestricao.setTipo(clienteRestricaoDTO.getTipo());
            return clienteRestricao;
        }
        return null;
    }

}
