package com.pacto.adm.core.adapters.contratomodalidade;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.contratomodalidade.ContratoModalidadeCreditoDTO;
import com.pacto.adm.core.entities.contrato.ContratoModalidadeCredito;
import org.springframework.stereotype.Component;

@Component
public class ContratoModalidadeCreditoAdapter implements AdapterInterface<ContratoModalidadeCredito, ContratoModalidadeCreditoDTO> {

    @Override
    public ContratoModalidadeCredito toEntity(ContratoModalidadeCreditoDTO contratoModalidadeCredito) {
        return new ContratoModalidadeCredito(
                contratoModalidadeCredito.getCodigo(),
                contratoModalidadeCredito.getQtdCreditoCompra(),
                contratoModalidadeCredito.getQtdCreditoDisponivel(),
                contratoModalidadeCredito.getValorUnitario(),
                contratoModalidadeCredito.getValorMensal(),
                contratoModalidadeCredito.getValorTotal()
        );
    }
    @Override
    public ContratoModalidadeCreditoDTO toDto(ContratoModalidadeCredito contratoModalidadeCredito) {
        return new ContratoModalidadeCreditoDTO(
                contratoModalidadeCredito.getCodigo(),
                contratoModalidadeCredito.getQtdCreditoCompra(),
                contratoModalidadeCredito.getQtdCreditoDisponivel(),
                contratoModalidadeCredito.getValorUnitario(),
                contratoModalidadeCredito.getValorMensal(),
                contratoModalidadeCredito.getValorTotal()
        );
    }
}
