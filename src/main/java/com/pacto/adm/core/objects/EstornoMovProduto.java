package com.pacto.adm.core.objects;

import com.pacto.adm.core.entities.*;
import com.pacto.adm.core.entities.financeiro.Caixa;
import com.pacto.adm.core.entities.financeiro.RemessaItem;
import com.pacto.adm.core.entities.financeiro.Transacao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class EstornoMovProduto {

    protected MovProduto movProduto;
    protected Usuario responsavelEstorno;
    protected Integer contrato;
    protected Integer vendaavulsa;
    protected Integer aulaAvulsaDiaria;
    protected Integer personal;
    protected MovParcela parcela;
    protected Cliente clienteVO;
    protected List<EstornoRecibo> listaEstornoRecibo;
    protected List<MovParcela> listaMovParcela;
    protected List<MovParcela> listaMovParcelaVinculadas;
    protected List<MovProduto> listaMovProduto;
    protected Boolean exiteOutroContratoPagouMinhaParcela;
    private List<Transacao> listaTransacoes;
    private Caixa caixa;
    private List<RemessaItem> listaItensRemessa;
    private boolean estornarOperadora;

    public EstornoMovProduto() {
        inicializarDados();
    }

    public void inicializarDados() {
        setListaMovProduto(new ArrayList<>());
        setListaMovParcela(new ArrayList<>());
        setListaEstornoRecibo(new ArrayList<>());
        setResponsavelEstorno(new Usuario());
        setListaTransacoes(new ArrayList<>());
        setListaItensRemessa(new ArrayList<>());
        setEstornarOperadora(true);
        setMovProduto(new MovProduto());
        setClienteVO(new Cliente());
        setCaixa(new Caixa());
        setContrato(0);
        setExiteOutroContratoPagouMinhaParcela(false);
    }

    public MovProduto getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProduto movProduto) {
        this.movProduto = movProduto;
    }

    public Usuario getResponsavelEstorno() {
        return responsavelEstorno;
    }

    public void setResponsavelEstorno(Usuario responsavelEstorno) {
        this.responsavelEstorno = responsavelEstorno;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getVendaavulsa() {
        return vendaavulsa;
    }

    public void setVendaavulsa(Integer vendaavulsa) {
        this.vendaavulsa = vendaavulsa;
    }

    public Integer getAulaAvulsaDiaria() {
        return aulaAvulsaDiaria;
    }

    public void setAulaAvulsaDiaria(Integer aulaAvulsaDiaria) {
        this.aulaAvulsaDiaria = aulaAvulsaDiaria;
    }

    public Integer getPersonal() {
        return personal;
    }

    public void setPersonal(Integer personal) {
        this.personal = personal;
    }

    public MovParcela getParcela() {
        return parcela;
    }

    public void setParcela(MovParcela parcela) {
        this.parcela = parcela;
    }

    public Cliente getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(Cliente clienteVO) {
        this.clienteVO = clienteVO;
    }

    public List<EstornoRecibo> getListaEstornoRecibo() {
        return listaEstornoRecibo;
    }

    public void setListaEstornoRecibo(List<EstornoRecibo> listaEstornoRecibo) {
        this.listaEstornoRecibo = listaEstornoRecibo;
    }

    public List<MovParcela> getListaMovParcela() {
        return listaMovParcela;
    }

    public void setListaMovParcela(List<MovParcela> listaMovParcela) {
        this.listaMovParcela = listaMovParcela;
    }

    public List<MovParcela> getListaMovParcelaVinculadas() {
        return listaMovParcelaVinculadas;
    }

    public void setListaMovParcelaVinculadas(List<MovParcela> listaMovParcelaVinculadas) {
        this.listaMovParcelaVinculadas = listaMovParcelaVinculadas;
    }

    public List<MovProduto> getListaMovProduto() {
        return listaMovProduto;
    }

    public void setListaMovProduto(List<MovProduto> listaMovProduto) {
        this.listaMovProduto = listaMovProduto;
    }

    public Boolean getExiteOutroContratoPagouMinhaParcela() {
        return exiteOutroContratoPagouMinhaParcela;
    }

    public void setExiteOutroContratoPagouMinhaParcela(Boolean exiteOutroContratoPagouMinhaParcela) {
        this.exiteOutroContratoPagouMinhaParcela = exiteOutroContratoPagouMinhaParcela;
    }

    public List<Transacao> getListaTransacoes() {
        return listaTransacoes;
    }

    public void setListaTransacoes(List<Transacao> listaTransacoes) {
        this.listaTransacoes = listaTransacoes;
    }

    public Caixa getCaixa() {
        return caixa;
    }

    public void setCaixa(Caixa caixa) {
        this.caixa = caixa;
    }

    public List<RemessaItem> getListaItensRemessa() {
        return listaItensRemessa;
    }

    public void setListaItensRemessa(List<RemessaItem> listaItensRemessa) {
        this.listaItensRemessa = listaItensRemessa;
    }

    public boolean isEstornarOperadora() {
        return estornarOperadora;
    }

    public void setEstornarOperadora(boolean estornarOperadora) {
        this.estornarOperadora = estornarOperadora;
    }

    public void estornarMovProduto(String key, boolean estornarOperadora) throws Exception {
    }
}
