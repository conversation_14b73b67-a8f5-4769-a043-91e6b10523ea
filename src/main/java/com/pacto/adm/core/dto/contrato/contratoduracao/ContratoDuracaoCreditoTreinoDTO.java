package com.pacto.adm.core.dto.contrato.contratoduracao;

import com.pacto.adm.core.enumerador.TipoHorarioCreditoTreinoEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

public class ContratoDuracaoCreditoTreinoDTO {
    @Schema(description = "Código único identificador da duração do crédito de treino", example = "1")
    private Integer codigo;

    @Schema(description = "Tipo de horário associado ao crédito de treino (ex.: MATUTINO, VESPERTINO)", example = "MATUTINO")
    private TipoHorarioCreditoTreinoEnum tipoHorario;

    @Schema(description = "Número de vezes por semana que o crédito pode ser utilizado", example = "3")
    private Integer numeroVezesSemana;

    @Schema(description = "Quantidade total de créditos comprados", example = "30")
    private Integer quantidadeCreditoCompra;

    @Schema(description = "Quantidade de créditos ainda disponíveis para uso", example = "25")
    private Integer quantidadeCreditoDisponivel;

    @Schema(description = "Valor unitário de cada crédito de treino", example = "50.00")
    private Double valorUnitario;

    @Schema(description = "Indica se o crédito de treino é não cumulativo", example = "true")
    private Boolean creditoTreinoNaoCumulativo;

    @Schema(description = "Quantidade de créditos mensais concedidos", example = "10")
    private Integer quantidadeCreditoMensal;

    @Schema(description = "Data da última concessão de crédito mensal", example = "2023-10-15")
    private Date dataUltimoCreditoMensal;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoHorarioCreditoTreinoEnum getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(TipoHorarioCreditoTreinoEnum tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public Integer getNumeroVezesSemana() {
        return numeroVezesSemana;
    }

    public void setNumeroVezesSemana(Integer numeroVezesSemana) {
        this.numeroVezesSemana = numeroVezesSemana;
    }

    public Integer getQuantidadeCreditoCompra() {
        return quantidadeCreditoCompra;
    }

    public void setQuantidadeCreditoCompra(Integer quantidadeCreditoCompra) {
        this.quantidadeCreditoCompra = quantidadeCreditoCompra;
    }

    public Integer getQuantidadeCreditoDisponivel() {
        return quantidadeCreditoDisponivel;
    }

    public void setQuantidadeCreditoDisponivel(Integer quantidadeCreditoDisponivel) {
        this.quantidadeCreditoDisponivel = quantidadeCreditoDisponivel;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Boolean getCreditoTreinoNaoCumulativo() {
        return creditoTreinoNaoCumulativo;
    }

    public void setCreditoTreinoNaoCumulativo(Boolean creditoTreinoNaoCumulativo) {
        this.creditoTreinoNaoCumulativo = creditoTreinoNaoCumulativo;
    }

    public Integer getQuantidadeCreditoMensal() {
        return quantidadeCreditoMensal;
    }

    public void setQuantidadeCreditoMensal(Integer quantidadeCreditoMensal) {
        this.quantidadeCreditoMensal = quantidadeCreditoMensal;
    }

    public Date getDataUltimoCreditoMensal() {
        return dataUltimoCreditoMensal;
    }

    public void setDataUltimoCreditoMensal(Date dataUltimoCreditoMensal) {
        this.dataUltimoCreditoMensal = dataUltimoCreditoMensal;
    }
}
