package com.pacto.adm.core.dto.contrato;

import com.pacto.adm.core.dto.PessoaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

public class ContratoRecorrenciaDTO {
    @Schema(description = "Código único identificador do contrato recorrente", example = "1")
    private Integer codigo;

    @Schema(description = "Indica se a anuidade está incluída na parcela", example = "true")
    private Boolean anuidadeNaParcela;

    @Schema(description = "Indica se o cancelamento é proporcional", example = "false")
    private Boolean cancelamentoProporcional;

    @Schema(description = "Indica se o cancelamento proporcional aplica-se apenas à renovação", example = "true")
    private Boolean cancelamentoPropocionalSomenteRenovacao;

    @Schema(description = "Data em que o contrato foi inutilizado", example = "2023-10-01T00:00:00Z")
    private Date dataInutilizada;

    @Schema(description = "Número de dias para bloqueio de acesso após o vencimento", example = "30")
    private Integer diasBloqueioAcesso;

    @Schema(description = "Número de dias para cancelamento automático após o vencimento", example = "60")
    private Integer diasCancelamentoAutomatico;

    @Schema(description = "Dia do vencimento da anuidade", example = "15")
    private Integer diaVencimentoAnuidade;

    @Schema(description = "Dia do vencimento do cartão", example = "5")
    private Integer diaVencimentoCartao;

    @Schema(description = "Período de fidelidade em meses", example = "12")
    private Integer fidelidade;

    @Schema(description = "Mês do vencimento da anuidade", example = "12")
    private Integer mesVencimentoAnuidade;

    @Schema(description = "Número do cartão associado ao contrato", example = "**** **** **** 1234")
    private String numeroCartao;

    @Schema(description = "Número da parcela da anuidade", example = "3")
    private Integer parcelaAnuidade;

    @Schema(description = "Indica se a anuidade será parcelada", example = "true")
    private Boolean parcelarAnuidade;

    @Schema(description = "Detalhes da pessoa associada ao contrato recorrente")
    private PessoaDTO pessoa;

    @Schema(description = "Quantidade de dias para cobrar a anuidade total", example = "365")
    private Integer qtdDiasCobrarAnuidadeTotal;

    @Schema(description = "Quantidade de dias para cobrar a próxima parcela", example = "30")
    private Integer qtdDiasCobrarProximaParcela;

    @Schema(description = "Indica se o contrato é renovável automaticamente", example = "true")
    private Boolean renovavelAutomaticamente;

    @Schema(description = "Última transação aprovada no sistema de pagamento", example = "TXN-123456789")
    private String ultimaTransacaoAprovada;

    @Schema(description = "Valor da anuidade", example = "500.00")
    private Double valorAnuidade;

    @Schema(description = "Valor mensal do contrato", example = "100.00")
    private Double valorMensal;

    public ContratoRecorrenciaDTO() {
    }

    public ContratoRecorrenciaDTO(
            Integer codigo, Boolean anuidadeNaParcela, Boolean cancelamentoProporcional,
            Boolean cancelamentoPropocionalSomenteRenovacao, Date dataInutilizada, Integer diasBloqueioAcesso,
            Integer diasCancelamentoAutomatico, Integer diaVencimentoAnuidade, Integer diaVencimentoCartao,
            Integer fidelidade, Integer mesVencimentoAnuidade, String numeroCartao, Integer parcelaAnuidade,
            Boolean parcelarAnuidade, PessoaDTO pessoa, Integer qtdDiasCobrarAnuidadeTotal,
            Integer qtdDiasCobrarProximaParcela, Boolean renovavelAutomaticamente, String ultimaTransacaoAprovada,
            Double valorAnuidade, Double valorMensal
    ) {
        this.codigo = codigo;
        this.anuidadeNaParcela = anuidadeNaParcela;
        this.cancelamentoProporcional = cancelamentoProporcional;
        this.cancelamentoPropocionalSomenteRenovacao = cancelamentoPropocionalSomenteRenovacao;
        this.dataInutilizada = dataInutilizada;
        this.diasBloqueioAcesso = diasBloqueioAcesso;
        this.diasCancelamentoAutomatico = diasCancelamentoAutomatico;
        this.diaVencimentoAnuidade = diaVencimentoAnuidade;
        this.diaVencimentoCartao = diaVencimentoCartao;
        this.fidelidade = fidelidade;
        this.mesVencimentoAnuidade = mesVencimentoAnuidade;
        this.numeroCartao = numeroCartao;
        this.parcelaAnuidade = parcelaAnuidade;
        this.parcelarAnuidade = parcelarAnuidade;
        this.pessoa = pessoa;
        this.qtdDiasCobrarAnuidadeTotal = qtdDiasCobrarAnuidadeTotal;
        this.qtdDiasCobrarProximaParcela = qtdDiasCobrarProximaParcela;
        this.renovavelAutomaticamente = renovavelAutomaticamente;
        this.ultimaTransacaoAprovada = ultimaTransacaoAprovada;
        this.valorAnuidade = valorAnuidade;
        this.valorMensal = valorMensal;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAnuidadeNaParcela() {
        return anuidadeNaParcela;
    }

    public void setAnuidadeNaParcela(Boolean anuidadeNaParcela) {
        this.anuidadeNaParcela = anuidadeNaParcela;
    }

    public Boolean getCancelamentoProporcional() {
        return cancelamentoProporcional;
    }

    public void setCancelamentoProporcional(Boolean cancelamentoProporcional) {
        this.cancelamentoProporcional = cancelamentoProporcional;
    }

    public Boolean getCancelamentoPropocionalSomenteRenovacao() {
        return cancelamentoPropocionalSomenteRenovacao;
    }

    public void setCancelamentoPropocionalSomenteRenovacao(Boolean cancelamentoPropocionalSomenteRenovacao) {
        this.cancelamentoPropocionalSomenteRenovacao = cancelamentoPropocionalSomenteRenovacao;
    }

    public Date getDataInutilizada() {
        return dataInutilizada;
    }

    public void setDataInutilizada(Date dataInutilizada) {
        this.dataInutilizada = dataInutilizada;
    }

    public Integer getDiasBloqueioAcesso() {
        return diasBloqueioAcesso;
    }

    public void setDiasBloqueioAcesso(Integer diasBloqueioAcesso) {
        this.diasBloqueioAcesso = diasBloqueioAcesso;
    }

    public Integer getDiasCancelamentoAutomatico() {
        return diasCancelamentoAutomatico;
    }

    public void setDiasCancelamentoAutomatico(Integer diasCancelamentoAutomatico) {
        this.diasCancelamentoAutomatico = diasCancelamentoAutomatico;
    }

    public Integer getDiaVencimentoAnuidade() {
        return diaVencimentoAnuidade;
    }

    public void setDiaVencimentoAnuidade(Integer diaVencimentoAnuidade) {
        this.diaVencimentoAnuidade = diaVencimentoAnuidade;
    }

    public Integer getDiaVencimentoCartao() {
        return diaVencimentoCartao;
    }

    public void setDiaVencimentoCartao(Integer diaVencimentoCartao) {
        this.diaVencimentoCartao = diaVencimentoCartao;
    }

    public Integer getFidelidade() {
        return fidelidade;
    }

    public void setFidelidade(Integer fidelidade) {
        this.fidelidade = fidelidade;
    }

    public Integer getMesVencimentoAnuidade() {
        return mesVencimentoAnuidade;
    }

    public void setMesVencimentoAnuidade(Integer mesVencimentoAnuidade) {
        this.mesVencimentoAnuidade = mesVencimentoAnuidade;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public Integer getParcelaAnuidade() {
        return parcelaAnuidade;
    }

    public void setParcelaAnuidade(Integer parcelaAnuidade) {
        this.parcelaAnuidade = parcelaAnuidade;
    }

    public Boolean getParcelarAnuidade() {
        return parcelarAnuidade;
    }

    public void setParcelarAnuidade(Boolean parcelarAnuidade) {
        this.parcelarAnuidade = parcelarAnuidade;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getQtdDiasCobrarAnuidadeTotal() {
        return qtdDiasCobrarAnuidadeTotal;
    }

    public void setQtdDiasCobrarAnuidadeTotal(Integer qtdDiasCobrarAnuidadeTotal) {
        this.qtdDiasCobrarAnuidadeTotal = qtdDiasCobrarAnuidadeTotal;
    }

    public Integer getQtdDiasCobrarProximaParcela() {
        return qtdDiasCobrarProximaParcela;
    }

    public void setQtdDiasCobrarProximaParcela(Integer qtdDiasCobrarProximaParcela) {
        this.qtdDiasCobrarProximaParcela = qtdDiasCobrarProximaParcela;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public String getUltimaTransacaoAprovada() {
        return ultimaTransacaoAprovada;
    }

    public void setUltimaTransacaoAprovada(String ultimaTransacaoAprovada) {
        this.ultimaTransacaoAprovada = ultimaTransacaoAprovada;
    }

    public Double getValorAnuidade() {
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }
}
