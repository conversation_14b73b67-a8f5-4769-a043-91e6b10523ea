package com.pacto.adm.core.dto.enveloperesposta.turma;

import com.pacto.adm.core.entities.contrato.NivelTurma;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaNivelTurma {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private NivelTurma content;

    public NivelTurma getContent() {
        return content;
    }

    public void setContent(NivelTurma content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 32, "
                    + "\"descricao\": \"INTERMEDIÁRIO\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
