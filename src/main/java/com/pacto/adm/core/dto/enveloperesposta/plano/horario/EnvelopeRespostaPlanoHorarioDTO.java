package com.pacto.adm.core.dto.enveloperesposta.plano.horario;

import com.pacto.adm.core.dto.PlanoDTO;
import com.pacto.adm.core.dto.negociacao.PlanoHorarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;


public class EnvelopeRespostaPlanoHorarioDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PlanoHorarioDTO content;

    public PlanoHorarioDTO getContent() {
        return content;
    }

    public void setContent(PlanoHorarioDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"descricao\": \"Segunda a Sexta, 08:00 - 12:00\", "
                    + "\"livre\": true";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
