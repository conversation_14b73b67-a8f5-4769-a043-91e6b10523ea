package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaBitrix24DTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoBuzzLeadDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoBuzzLeadDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoBuzzLeadDTO content;

    public ConfiguracaoIntegracaoBuzzLeadDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoBuzzLeadDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1001, "
                    + "\"habilitada\": true, "
                    + "\"acaoObjecao\": 3, "
                    + "\"tokenBuzzLead\": \"token-buzzlead-abc123\", "
                    + "\"tokenAcessoZw\": \"token-zw-xyz456\", "
                    + "\"urlWebHook\": \"https://www.exemplo.com/webhook-buzzlead\", "
                    + "\"responsavelPadrao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"horaLimite\": \"18:00\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"codigo\": 1001, "
                    + "\"habilitada\": true, "
                    + "\"acaoObjecao\": 3, "
                    + "\"tokenBuzzLead\": \"token-buzzlead-abc123\", "
                    + "\"tokenAcessoZw\": \"token-zw-xyz456\", "
                    + "\"urlWebHook\": \"https://www.exemplo.com/webhook-buzzlead\", "
                    + "\"responsavelPadrao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"horaLimite\": \"18:00\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
