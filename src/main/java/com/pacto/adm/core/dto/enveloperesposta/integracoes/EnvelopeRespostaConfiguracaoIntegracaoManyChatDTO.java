package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoManyChatDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoManyChatDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoManyChatDTO content;

    public ConfiguracaoIntegracaoManyChatDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoManyChatDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"habilitada\": true, "
                    + "\"token\": \"manychat-token-xyz\", "
                    + "\"tagUnidade\": {" + EnvelopeRespostaTagDTO.atributos + "}, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"habilitada\": true, "
                    + "\"token\": \"manychat-token-xyz\", "
                    + "\"tagUnidade\": {" + EnvelopeRespostaTagDTO.atributos + "}, "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";



}
