package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Resposta da Pergunta", description = "Informações da resposta à pergunta")
public class RespostaPergClienteDTO {

    @Schema(description = "Cõdigo identificador da resposta", example = "1")
    private Integer codigo;

    @Schema(description = "Resposta textual da pergunta (Caso a pergunta seja de múltipla escolha esse campo irá retornar Null)", example = "A academia Pacto oferece um conjunto enorme de aparelhos e aulas.")
    private String respostaTextual;

    @Schema(description = "Indica se é uma resposta por opção de múltipla escolha", example = "false")
    private Boolean respostaOpcao;

    @Schema(description = "Descrição da resposta (Para opção de múltipla escolha a resposta irá aparecer aqui)", example = "TEXTO")
    private String descricaoRespota;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getRespostaTextual() {
        return respostaTextual;
    }

    public void setRespostaTextual(String respostaTextual) {
        this.respostaTextual = respostaTextual;
    }

    public Boolean getRespostaOpcao() {
        return respostaOpcao;
    }

    public void setRespostaOpcao(Boolean respostaOpcao) {
        this.respostaOpcao = respostaOpcao;
    }

    public String getDescricaoRespota() {
        return descricaoRespota;
    }

    public void setDescricaoRespota(String descricaoRespota) {
        this.descricaoRespota = descricaoRespota;
    }
}
