package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoBotConversaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGymbotProDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class EnvelopeRespostaListConfiguracaoIntegracaoGymBotProDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ConfiguracaoIntegracaoGymbotProDTO> content;

    public List<ConfiguracaoIntegracaoGymbotProDTO> getContent() {
        return content;
    }

    public void setContent(List<ConfiguracaoIntegracaoGymbotProDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaConfiguracaoIntegracaoGymBotProDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

    public static final String requestBody = "[{" + EnvelopeRespostaConfiguracaoIntegracaoGymBotProDTO.atributos + "}]";

}
