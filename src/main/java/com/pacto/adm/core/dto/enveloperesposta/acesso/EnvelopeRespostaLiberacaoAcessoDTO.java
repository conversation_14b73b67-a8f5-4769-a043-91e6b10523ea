package com.pacto.adm.core.dto.enveloperesposta.acesso;

import com.pacto.adm.core.dto.LiberacaoAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.coletor.EnvelopeRespostaColetorDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaLiberacaoAcessoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private LiberacaoAcessoDTO content;

    public LiberacaoAcessoDTO getContent() {
        return content;
    }

    public void setContent(LiberacaoAcessoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 112, "
                    + "\"pessoa\": {" + EnvelopeRespostaPessoaDTO.atributos + "}, "
                    + "\"tipoLiberacao\": 1, "
                    + "\"sentido\": \"E\", "
                    + "\"localAcesso\": {" + EnvelopeRespostaLocalDeAcessoDTO.atributos + "}, "
                    + "\"coletor\": {" + EnvelopeRespostaColetorDTO.atributos + "}, "
                    + "\"usuario\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"dataHora\": \"2025-04-08T00:00:00Z\", "
                    + "\"justificativa\": \"Entrada\", "
                    + "\"dataHoraJustificativa\": \"2025-04-08T00:00:00Z\", "
                    + "\"usuarioJustificou\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"nomeGenerico\": \"ENTRADA DE PESSOA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
}
