package com.pacto.adm.core.dto.enveloperesposta.aluguel;

import com.pacto.adm.core.dto.AluguelArmarioDTO;
import com.pacto.adm.core.dto.enveloperesposta.vendaavulsa.EnvelopeRespostaVendaAvulsaDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaMovProdutoDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaAluguelArmarioDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private AluguelArmarioDTO content;

    public  AluguelArmarioDTO  getContent() {
        return content;
    }

    public void setContent( AluguelArmarioDTO  content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 109, "
                    + "\"dataCadastro\": \"2025-04-08T14:30:00Z\", "
                    + "\"valor\": 4.90, "
                    + "\"fimOriginal\": \"2025-04-09T14:30:00Z\", "
                    + "\"dataRenovacaoAutomatica\": \"2025-04-09T14:31:00Z\", "
                    + "\"contratoAssinado\": true, "
                    + "\"renovarAutomatico\": true, "
                    + "\"dataInicio\": \"2025-04-08T14:30:00Z\", "
                    + "\"chaveDevolvida\": false,"
                    + "\"armario\": {" + EnvelopeRespostaArmarioDTO.atributos + "}, "
                    + "\"cliente\": {" + EnvelopeRespostaClienteDTO.atributos + "}, "
                    + "\"responsavelCadastro\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"movProduto\": {" + EnvelopeRespostaMovProdutoDTO.atributos + "}, "
                    + "\"vendaAvulsa\": {" + EnvelopeRespostaVendaAvulsaDTO.atributos + "}";

    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
