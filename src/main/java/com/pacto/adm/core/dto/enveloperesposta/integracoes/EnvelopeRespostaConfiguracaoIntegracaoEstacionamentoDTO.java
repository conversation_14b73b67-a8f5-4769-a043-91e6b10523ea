package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoEstacionamentoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoEstacionamentoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoEstacionamentoDTO content;

    public ConfiguracaoIntegracaoEstacionamentoDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoEstacionamentoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"utilizaSistemaEstacionamento\": true, "
                    + "\"empresaConfigEstacionamento\": {" + EnvelopeRespostaEmpresaConfigEstacionamentoDTO.atributos + "}, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"utilizaSistemaEstacionamento\": true, "
                    + "\"empresaConfigEstacionamento\": {" + EnvelopeRespostaEmpresaConfigEstacionamentoDTO.atributos + "}, "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
