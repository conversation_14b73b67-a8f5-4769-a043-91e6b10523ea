package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
public class TabelaParceiroFidelidadeItemDTO {
    @Schema(description = "Código único identificador do item da tabela de fidelidade", example = "1")
    private Integer codigo;

    @Schema(description = "Valor inicial do intervalo para aplicação do multiplicador", example = "100.00")
    private Double valorInicio;

    @Schema(description = "Valor final do intervalo para aplicação do multiplicador", example = "500.00")
    private Double valorFim;

    @Schema(description = "Multiplicador utilizado para cálculo de pontos ou benefícios", example = "1.5")
    private Double multiplicador;

    @Schema(description = "Código da tabela de fidelidade à qual este item pertence", example = "10")
    private Integer tabelaParceiroFidelidade;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorInicio() {
        return valorInicio;
    }

    public void setValorInicio(Double valorInicio) {
        this.valorInicio = valorInicio;
    }

    public Double getValorFim() {
        return valorFim;
    }

    public void setValorFim(Double valorFim) {
        this.valorFim = valorFim;
    }

    public Double getMultiplicador() {
        return multiplicador;
    }

    public void setMultiplicador(Double multiplicador) {
        this.multiplicador = multiplicador;
    }

    public Integer getTabelaParceiroFidelidade() {
        return tabelaParceiroFidelidade;
    }

    public void setTabelaParceiroFidelidade(Integer tabelaParceiroFidelidade) {
        this.tabelaParceiroFidelidade = tabelaParceiroFidelidade;
    }
}
