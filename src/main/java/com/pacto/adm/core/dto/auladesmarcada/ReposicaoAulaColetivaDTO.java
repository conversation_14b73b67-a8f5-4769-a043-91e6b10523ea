package com.pacto.adm.core.dto.auladesmarcada;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Reposição Aula Coletiva", description = "Informações da reposição de uma aula coletiva")
public class ReposicaoAulaColetivaDTO {


    @Schema(description = "Código do contrato do cliente", example = "123")
    private Integer contrato;

    @Schema(description = "Motivo da reposição", example = "CONSULTA MÉDICA")
    private String motivo;

    @Schema(description = "Modalidade da reposição", example = "SPINNING")
    private String modalidade;

    @Schema(description = "Reposição", example = "Reposição")
    private String reposicao;

    @Schema(description = "Data", example = "2025-05-02")
    private String data;

    @Schema(description = "Data da reposição", example = "2025-05-05")
    private String dataReposta;

    @Schema(description = "Limite para reposição", example = "1")
    private String limiteParaResposicao;

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getReposicao() {
        return reposicao;
    }

    public void setReposicao(String reposicao) {
        this.reposicao = reposicao;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getLimiteParaResposicao() {
        return limiteParaResposicao;
    }

    public void setLimiteParaResposicao(String limiteParaResposicao) {
        this.limiteParaResposicao = limiteParaResposicao;
    }

    public String getDataReposta() {
        return dataReposta;
    }

    public void setDataReposta(String dataReposta) {
        this.dataReposta = dataReposta;
    }
}
