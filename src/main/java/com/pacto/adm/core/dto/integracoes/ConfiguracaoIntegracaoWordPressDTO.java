package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração WordPress", description = "Configurações para integração com formulários e comunicação via WordPress.")
public class ConfiguracaoIntegracaoWordPressDTO {

    @Schema(description = "Código identificador da configuração do WordPress.", example = "1001")
    private Integer codigo;

    @Schema(description = "Código da ação de objeção associada à integração com o WordPress.", example = "2")
    private Integer acaoObjecao;

    @Schema(description = "Indica se a integração com o WordPress está habilitada.", example = "true")
    private Boolean habilitada;

    @Schema(description = "Responsável padrão pela integração com o WordPress, contendo informações do usuário.")
    private UsuarioDTO responsavelPadrao;

    @Schema(description = "Hora limite configurada para o processamento da integração com o WordPress.", example = "17:30")
    private String horaLimite;

    @Schema(description = "Mensagem padrão exibida no formulário integrado ao WordPress.", example = "Obrigado pelo seu contato, responderemos em breve.")
    private String formularioPadraoMensagem;

    @Schema(description = "Corpo padrão da mensagem enviada via integração com o WordPress.", example = "Recebemos seu formulário e entraremos em contato.")
    private String corpoPadraoMensagem;

    @Schema(description = "Endereço de e-mail de destino configurado para recebimento de formulários do WordPress.", example = "<EMAIL>")
    private String emailDestino;

    @Schema(description = "Detalhes da empresa associada à configuração de integração com o WordPress.")
    private EmpresaDTO empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(Integer acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public Boolean getHabilitada() {
        return habilitada;
    }

    public void setHabilitada(Boolean habilitada) {
        this.habilitada = habilitada;
    }

    public UsuarioDTO getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(UsuarioDTO responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public String getFormularioPadraoMensagem() {
        return formularioPadraoMensagem;
    }

    public void setFormularioPadraoMensagem(String formularioPadraoMensagem) {
        this.formularioPadraoMensagem = formularioPadraoMensagem;
    }

    public String getCorpoPadraoMensagem() {
        return corpoPadraoMensagem;
    }

    public void setCorpoPadraoMensagem(String corpoPadraoMensagem) {
        this.corpoPadraoMensagem = corpoPadraoMensagem;
    }

    public String getEmailDestino() {
        return emailDestino;
    }

    public void setEmailDestino(String emailDestino) {
        this.emailDestino = emailDestino;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
