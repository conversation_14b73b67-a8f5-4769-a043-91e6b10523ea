package com.pacto.adm.core.dto.enveloperesposta.negociacao;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.convenio.EnvelopeRespostaConvenioDTO;
import com.pacto.adm.core.dto.enveloperesposta.desconto.EnvelopeRespostaDescontoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import com.pacto.adm.core.dto.enveloperesposta.negociacao.cartao.EnvelopeRespostaDiaCartaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.pacote.EnvelopeRespostaPacoteDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.duracao.EnvelopeRespostaDuracaoPlanoDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.horario.EnvelopeRespostaPlanoHorarioDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.modalidade.EnvelopeRespostaPlanoModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.produto.EnvelopeRespostaPlanoProdutoDTO;
import com.pacto.adm.core.dto.negociacao.NegociacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaNegociacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private NegociacaoDTO content;

    public NegociacaoDTO getContent() {
        return content;
    }

    public void setContent(NegociacaoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"plano\": 7, "
                    + "\"bolsa\": false, "
                    + "\"exibirEditarTurma\": true, "
                    + "\"exibirValorModalidade\": true, "
                    + "\"recorrencia\": true, "
                    + "\"usaArredondamento\": true, "
                    + "\"fecharNegociacaoSemAutorizacaoDCC\": true, "
                    + "\"vendaCreditoTreino\": true, "
                    + "\"creditoSessao\": false, "
                    + "\"creditoTreinoNaoCumulativo\": false, "
                    + "\"duracoes\": [{" + EnvelopeRespostaDuracaoPlanoDTO.atributos + "}], "
                    + "\"horarios\": [{" + EnvelopeRespostaPlanoHorarioDTO.atributos + "}], "
                    + "\"modalidades\": [{" + EnvelopeRespostaPlanoModalidadeDTO.atributos + "}], "
                    + "\"produtos\": [{" + EnvelopeRespostaPlanoProdutoDTO.atributos + "}], "
                    + "\"pacotes\": [{" + EnvelopeRespostaPacoteDTO.atributos + "}], "
                    + "\"convenios\": [{" + EnvelopeRespostaConvenioDTO.atributos + "}], "
                    + "\"descontos\": [{" + EnvelopeRespostaDescontoDTO.atributos + "}], "
                    + "\"isentarRematricula\": false, "
                    + "\"qtdDiasCobrarRematricula\": 30, "
                    + "\"duracaoSugerida\": 12, "
                    + "\"horarioSugerido\": 4, "
                    + "\"modalidadesSugeridas\": [1, 3, 5], "
                    + "\"pacotesSugeridos\": [10, 12], "
                    + "\"diasCartao\": [{" + EnvelopeRespostaDiaCartaoDTO.atributos + "}], "
                    + "\"diasProRata\": [{" + EnvelopeRespostaDiaCartaoDTO.atributos + "}], "
                    + "\"condicaoSugerida\": 2, "
                    + "\"diaSugerido\": 5, "
                    + "\"nrVezesParcelarAdesao\": 3, "
                    + "\"cobrarAdesaoSeparada\": false, "
                    + "\"proRataObrigatorio\": false, "
                    + "\"cobrarProdutoSeparado\": false, "
                    + "\"nrVezesparcelarProduto\": 2, "
                    + "\"contratoBaseado\": 150, "
                    + "\"deveGerarParcelasComValorDiferente\": true";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";
}
