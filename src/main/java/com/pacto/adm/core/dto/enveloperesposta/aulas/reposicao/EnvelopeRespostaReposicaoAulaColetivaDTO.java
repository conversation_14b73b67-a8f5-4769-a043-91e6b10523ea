package com.pacto.adm.core.dto.enveloperesposta.aulas.reposicao;

import com.pacto.adm.core.dto.auladesmarcada.ReposicaoAulaColetivaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaReposicaoAulaColetivaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ReposicaoAulaColetivaDTO content;

    public ReposicaoAulaColetivaDTO getContent() {
        return content;
    }

    public void setContent(ReposicaoAulaColetivaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"contrato\": 123, "
                    + "\"motivo\": \"CONSULTA MÉDICA\", "
                    + "\"modalidade\": \"SPINNING\", "
                    + "\"reposicao\": \"Reposição\", "
                    + "\"data\": \"2025-05-02\", "
                    + "\"dataReposta\": \"2025-05-05\", "
                    + "\"limiteParaResposicao\": \"1\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";



}
