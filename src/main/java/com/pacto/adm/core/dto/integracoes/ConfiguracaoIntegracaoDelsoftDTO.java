package com.pacto.adm.core.dto.integracoes;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Configuração Integração Delsoft", description = "Configurações para integração com o sistema Delsoft.")
public class ConfiguracaoIntegracaoDelsoftDTO {

    @Schema(description = "Indica se a integração com o sistema Delsoft está habilitada.", example = "true")
    private Boolean utilizaIntegracaoDelsoft;

    @Schema(description = "Host do sistema Delsoft para integração.", example = "delsoft.example.com")
    private String hostIntegracaoDelsoft;

    @Schema(description = "Porta utilizada para a integração com o sistema Delsoft.", example = "8080")
    private Integer portaIntegracaoDelsoft;

    @Schema(description = "Token de autenticação para a integração com o Delsoft.", example = "abcd1234token")
    private String tokenIntegracaoDelsoft;

    @Schema(description = "Nome da aplicação Delsoft integrada.", example = "AplicacaoDelsoft1")
    private String nomeAplicacaoDelsoft;

    @Schema(description = "Usuário da aplicação Delsoft para autenticação.", example = "adminDelsoft")
    private String usuarioAplicacaoDelsoft;

    @Schema(description = "Senha do usuário para autenticação na aplicação Delsoft.", example = "senhaDelsoft")
    private String senhaAplicacaoDelsoft;

    @Schema(description = "Plano associado à aplicação Delsoft.", example = "1")
    private Integer planoAplicacaoDelsoft;

    @Schema(description = "Dados da empresa vinculada à configuração.")
    private EmpresaDTO empresa;

    public Boolean getUtilizaIntegracaoDelsoft() {
        return utilizaIntegracaoDelsoft;
    }

    public void setUtilizaIntegracaoDelsoft(Boolean utilizaIntegracaoDelsoft) {
        this.utilizaIntegracaoDelsoft = utilizaIntegracaoDelsoft;
    }

    public String getHostIntegracaoDelsoft() {
        return hostIntegracaoDelsoft;
    }

    public void setHostIntegracaoDelsoft(String hostIntegracaoDelsoft) {
        this.hostIntegracaoDelsoft = hostIntegracaoDelsoft;
    }

    public Integer getPortaIntegracaoDelsoft() {
        return portaIntegracaoDelsoft;
    }

    public void setPortaIntegracaoDelsoft(Integer portaIntegracaoDelsoft) {
        this.portaIntegracaoDelsoft = portaIntegracaoDelsoft;
    }

    public String getTokenIntegracaoDelsoft() {
        return tokenIntegracaoDelsoft;
    }

    public void setTokenIntegracaoDelsoft(String tokenIntegracaoDelsoft) {
        this.tokenIntegracaoDelsoft = tokenIntegracaoDelsoft;
    }

    public String getNomeAplicacaoDelsoft() {
        return nomeAplicacaoDelsoft;
    }

    public void setNomeAplicacaoDelsoft(String nomeAplicacaoDelsoft) {
        this.nomeAplicacaoDelsoft = nomeAplicacaoDelsoft;
    }

    public String getUsuarioAplicacaoDelsoft() {
        return usuarioAplicacaoDelsoft;
    }

    public void setUsuarioAplicacaoDelsoft(String usuarioAplicacaoDelsoft) {
        this.usuarioAplicacaoDelsoft = usuarioAplicacaoDelsoft;
    }

    public String getSenhaAplicacaoDelsoft() {
        return senhaAplicacaoDelsoft;
    }

    public void setSenhaAplicacaoDelsoft(String senhaAplicacaoDelsoft) {
        this.senhaAplicacaoDelsoft = senhaAplicacaoDelsoft;
    }

    public Integer getPlanoAplicacaoDelsoft() {
        return planoAplicacaoDelsoft;
    }

    public void setPlanoAplicacaoDelsoft(Integer planoAplicacaoDelsoft) {
        this.planoAplicacaoDelsoft = planoAplicacaoDelsoft;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
