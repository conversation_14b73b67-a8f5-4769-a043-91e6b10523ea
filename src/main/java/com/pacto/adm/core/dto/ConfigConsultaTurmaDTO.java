package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pacto.adm.core.dto.nivelturma.NivelTurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "Configuração Consulta Turma", description = "Configurações para consulta de turmas.")
public class ConfigConsultaTurmaDTO {

    @Schema(description = "Lista de professores associados à consulta da turma, incluindo nome, código e outros dados.")
    private List<PessoaDTO> professores = new ArrayList<>();

    @Schema(description = "Lista de níveis das turmas disponíveis para seleção na consulta.")
    private List<NivelTurmaDTO> niveis = new ArrayList<>();

    public List<PessoaDTO> getProfessores() {
        return professores;
    }

    public void setProfessores(List<PessoaDTO> professores) {
        this.professores = professores;
    }

    public List<NivelTurmaDTO> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<NivelTurmaDTO> niveis) {
        this.niveis = niveis;
    }
}
