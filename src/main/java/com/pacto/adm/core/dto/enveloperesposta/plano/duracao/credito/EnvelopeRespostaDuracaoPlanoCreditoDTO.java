package com.pacto.adm.core.dto.enveloperesposta.plano.duracao.credito;

import com.pacto.adm.core.dto.negociacao.PlanoDuracaoCondicaoDTO;
import com.pacto.adm.core.dto.negociacao.PlanoDuracaoCreditoDTO;
import io.swagger.v3.oas.annotations.media.Schema;


public class EnvelopeRespostaDuracaoPlanoCreditoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PlanoDuracaoCreditoDTO content;

    public PlanoDuracaoCreditoDTO getContent() {
        return content;
    }

    public void setContent(PlanoDuracaoCreditoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"planoDuracao\": 12, "
                    + "\"numeroVezesSemana\": 3, "
                    + "\"quantidadeCreditoCompra\": 24, "
                    + "\"quantidadeCreditoMensal\": 8, "
                    + "\"tipoHorarioCreditoTreino\": 2, "
                    + "\"valorUnitario\": 25.00, "
                    + "\"livre\": true";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
