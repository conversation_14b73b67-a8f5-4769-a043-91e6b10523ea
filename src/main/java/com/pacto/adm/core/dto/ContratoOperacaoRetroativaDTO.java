package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Operação Retroativa de Contrato", description = "Informações detalhadas de uma operação retroativa realizada em um contrato")
public class ContratoOperacaoRetroativaDTO {
    @Schema(description = "Informações detalhadas do cliente associado à operação")
    private ClienteDTO cliente;

    @Schema(description = "Informações detalhadas do contrato onde a operação foi realizada")
    private ContratoDTO contrato;

    @Schema(description = "Informações da justificativa utilizada para a operação")
    private JustificativaOperacaoDTO justificativaOperacao;

    @Schema(description = "Informações detalhadas da operação realizada no contrato")
    private ContratoOperacaoDTO contratoOperacao;

    public ContratoOperacaoRetroativaDTO() {
    }

    public ContratoOperacaoRetroativaDTO(ClienteDTO cliente, ContratoDTO contrato, ContratoOperacaoDTO contratoOperacao, JustificativaOperacaoDTO justificativaOperacao) {
        this.cliente = cliente;
        this.contrato = contrato;
        this.justificativaOperacao = justificativaOperacao;
        this.contratoOperacao = contratoOperacao;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public JustificativaOperacaoDTO getJustificativaOperacao() {
        return justificativaOperacao;
    }

    public void setJustificativaOperacao(JustificativaOperacaoDTO justificativaOperacao) {
        this.justificativaOperacao = justificativaOperacao;
    }

    public ContratoOperacaoDTO getContratoOperacao() {
        return contratoOperacao;
    }

    public void setContratoOperacao(ContratoOperacaoDTO contratoOperacao) {
        this.contratoOperacao = contratoOperacao;
    }

}
