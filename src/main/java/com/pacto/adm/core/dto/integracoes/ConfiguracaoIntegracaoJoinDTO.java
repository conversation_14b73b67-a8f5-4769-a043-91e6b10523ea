package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração Join", description = "Configurações para integração com a plataforma Join para captação de leads e automação.")
public class ConfiguracaoIntegracaoJoinDTO {

    @Schema(description = "Código identificador da configuração do Join.", example = "1001")
    private Integer codigo;

    @Schema(description = "Código da ação de objeção associada à integração com o Join.", example = "2")
    private Integer acaoObjecao;

    @Schema(description = "Indica se a integração com o Join está habilitada.", example = "true")
    private Boolean habilitada;

    @Schema(description = "Responsável padrão pela integração com o Join, contendo informações do usuário.")
    private UsuarioDTO responsavelPadrao;

    @Schema(description = "Hora limite configurada para o processamento da integração com o Join.", example = "18:00")
    private String horaLimite;

    @Schema(description = "Detalhes da empresa associada à configuração de integração com o Join.")
    private EmpresaDTO empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(Integer acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public Boolean getHabilitada() {
        return habilitada;
    }

    public void setHabilitada(Boolean habilitada) {
        this.habilitada = habilitada;
    }

    public UsuarioDTO getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(UsuarioDTO responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
