package com.pacto.adm.core.dto.integracaomanychat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonIgnoreProperties
@Schema(name = "Cliente Simplificado", description = "Informações simplificadas do cliente")
public class ClienteSimplificadoDTO {

    @Schema(description = "Código da matrícula do cliente", example = "123")
    private Integer codigoMatricula;

    @Schema(description = "Nome do cliente", example = "Felipe Felcaciano")
    private String nome;

    @Schema(description = "Nome da unidade do cliente", example = "Academia Pacto Goiânia")
    private String nomeUnidade;

    @Schema(description = "Descrição da situação do cliente", example = "ATIVO")
    private String situacaoDescricao;

    @Schema(description = "Nome do plano do cliente", example = "ACADEMIA")
    private String nomePlano;

    @Schema(description = "Período restante do contrato", example = "20 dias")
    private String periodoRestanteContrato;

    @Schema(description = "Quantidade de contratos", example = "2")
    private String descricaoQtdContratos;

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSituacaoDescricao() {
        if (situacaoDescricao == null) {
            return "";
        }
        return situacaoDescricao;
    }

    public void setSituacaoDescricao(String situacaoDescricao) {
        this.situacaoDescricao = situacaoDescricao;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public String getPeriodoRestanteContrato() {
        return periodoRestanteContrato;
    }

    public void setPeriodoRestanteContrato(String periodoRestanteContrato) {
        this.periodoRestanteContrato = periodoRestanteContrato;
    }

    public String getDescricaoQtdContratos() {
        return descricaoQtdContratos;
    }

    public void setDescricaoQtdContratos(String descricaoQtdContratos) {
        this.descricaoQtdContratos = descricaoQtdContratos;
    }
}
