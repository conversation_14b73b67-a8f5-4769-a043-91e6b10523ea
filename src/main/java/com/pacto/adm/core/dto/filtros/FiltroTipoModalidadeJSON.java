package com.pacto.adm.core.dto.filtros;

import org.json.JSONException;
import org.json.JSONObject;

public class FiltroTipoModalidadeJSON {

    private String parametro;

    public FiltroTipoModalidadeJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }
}
