package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude
@Schema(description = "Informações da pessoa")
public class PessoaDTO {
    @Schema(description = "Código único identificador da pessoa.", example = "12345")
    private Integer codigo;

    @Schema(description = "Código do cliente associado à pessoa.", example = "67890")
    private Integer codCliente;

    @Schema(description = "Nome completo da pessoa.", example = "<PERSON>")
    private String nome;

    @Schema(description = "Chave de identificação da foto da pessoa no sistema de armazenamento.", example = "foto12345.jpg")
    private String fotoKey;

    @Schema(description = "URL completa para acessar a foto da pessoa.", example = "https://exemplo.com/fotos/foto12345.jpg")
    private String urlFoto;

    @Schema(description = "CPF (Cadastro de Pessoa Física) da pessoa.", example = "123.456.789-00")
    private String cpf;

    @Schema(description = "Categoria à qual a pessoa pertence.", example = "ALUNO")
    private String categoria;

    @Schema(description = "Situação atual da pessoa no sistema.", example = "ATIVO")
    private String situacao;

    @Schema(description = "Situação do contrato associado à pessoa.", example = "VIGENTE")
    private String situacaoContrato;

    @Schema(description = "Tipo da pessoa (ex.: PF para Pessoa Física, PJ para Pessoa Jurídica).", example = "PF")
    private String tipo;

    @Schema(description = "Tipo de colaborador, caso a pessoa seja um funcionário.", example = "PROFESSOR")
    private String tipoColaborador;

    @Schema(description = "Tipo de compra de crédito associada à pessoa.", example = "PLANO")
    private String tipoCompraCredito;

    @Schema(description = "Data de nascimento", example = "01/01/2001")
    private Date dataNascimento;

    public PessoaDTO() {
    }

    public PessoaDTO(String nome) {
        this.nome = nome;
    }

    public PessoaDTO(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public PessoaDTO(Integer codigo, String nome, String cpf) {
        this.codigo = codigo;
        this.nome = nome;
        this.cpf = cpf;
    }

    public PessoaDTO(String nome, String cpf, Date dataNascimento) {
        this.nome = nome;
        this.cpf = cpf;
        this.dataNascimento = dataNascimento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getCpf() { return cpf; }

    public void setCpf(String cpf) {this.cpf = cpf; }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(String tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }

    public String getTipoCompraCredito() {
        return tipoCompraCredito;
    }

    public void setTipoCompraCredito(String tipoCompraCredito) {
        this.tipoCompraCredito = tipoCompraCredito;
    }

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }
}
