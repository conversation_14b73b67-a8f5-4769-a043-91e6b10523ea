package com.pacto.adm.core.dto.enveloperesposta.acesso.perfilacesso;

import com.pacto.adm.core.dto.PerfilAcessoDTO;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Lista de Perfis de Acesso", description = "Representação das respostas contendo uma lista de perfis de acesso ao sistema")
public class EnvelopeRespostaPerfilAcessoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PerfilAcessoDTO content;

    public PerfilAcessoDTO getContent() {
        return content;
    }

    public void setContent(PerfilAcessoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"nome\": \"PROFESSOR\", "
                    + "\"tipo\": 1";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
