package com.pacto.adm.core.dto.integracoes;

import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Configuração de Integração BuzzLead", description = "Configurações para integração com o sistema BuzzLead para captação e gerenciamento de leads.")
public class ConfiguracaoIntegracaoBuzzLeadDTO {

    @Schema(description = "Código identificador da configuração do BuzzLead.", example = "1001")
    private Integer codigo;

    @Schema(description = "Indica se a integração com o BuzzLead está habilitada.", example = "true")
    private boolean habilitada;

    @Schema(description = "Código da ação de objeção associada à integração com o BuzzLead.", example = "3")
    private Integer acaoObjecao;

    @Schema(description = "Token utilizado para autenticação na plataforma BuzzLead.", example = "token-buzzlead-abc123")
    private String tokenBuzzLead;

    @Schema(description = "Token de acesso para o sistema Zw integrado ao BuzzLead.", example = "token-zw-xyz456")
    private String tokenAcessoZw;

    @Schema(description = "URL do WebHook para recebimento de dados do BuzzLead.", example = "https://www.exemplo.com/webhook-buzzlead")
    private String urlWebHook;

    @Schema(description = "Responsável padrão pela integração com o BuzzLead, contendo informações do usuário.")
    private UsuarioDTO responsavelPadrao;

    @Schema(description = "Hora limite configurada para o processamento da integração com o BuzzLead.", example = "18:00")
    private String horaLimite;

    @Schema(description = "Detalhes da empresa associada à configuração da integração com o BuzzLead.")
    private EmpresaDTO empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public Integer getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(Integer acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public String getTokenBuzzLead() {
        return tokenBuzzLead;
    }

    public void setTokenBuzzLead(String tokenBuzzLead) {
        this.tokenBuzzLead = tokenBuzzLead;
    }

    public String getTokenAcessoZw() {
        return tokenAcessoZw;
    }

    public void setTokenAcessoZw(String tokenAcessoZw) {
        this.tokenAcessoZw = tokenAcessoZw;
    }

    public String getUrlWebHook() {
        return urlWebHook;
    }

    public void setUrlWebHook(String urlWebHook) {
        this.urlWebHook = urlWebHook;
    }

    public UsuarioDTO getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(UsuarioDTO responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
