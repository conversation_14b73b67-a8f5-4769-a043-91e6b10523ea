package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Agenda de Horário da Turma", description = "Detalhes sobre os horários das aulas de uma turma, incluindo informações sobre a modalidade, ocupação, e professores.")
public class HorarioTurmaAgendaDTO {

    @Schema(description = "Código único identificador do horário da turma.", example = "123")
    private Integer codigo;

    @Schema(description = "Código da modalidade associada ao horário.", example = "1")
    private Integer modalidade;

    @Schema(description = "Ocupação atual do horário da turma, em termos de alunos matriculados.", example = "25")
    private Integer ocupacao;

    @Schema(description = "Capacidade máxima de alunos para este horário da turma.", example = "30")
    private Integer capacidade;

    @Schema(description = "Dia da semana em que o horário ocorre (ex: 'Segunda-feira').", example = "Segunda-feira")
    private String diaSemana;

    @Schema(description = "Descrição da turma (ex: 'Turma A').", example = "Turma A")
    private String turma;

    @Schema(description = "Hora de início da aula no formato HH:mm.", example = "08:00")
    private String inicio;

    @Schema(description = "Hora de término da aula no formato HH:mm.", example = "09:00")
    private String fim;

    @Schema(description = "Ambiente onde a aula será realizada.", example = "Sala 101")
    private String ambiente;

    @Schema(description = "Nome do professor responsável pela turma.", example = "João Silva")
    private String professor;

    @Schema(description = "Indica se a aula é coletiva. O valor padrão é 'false'.", example = "false")
    private boolean coletiva = false;

    @Schema(description = "Indica se as matrículas devem ser bloqueadas quando o limite de alunos for atingido. O valor padrão é 'false'.", example = "false")
    private boolean bloquearMatriculasAcimaLimite = false;

    @Schema(description = "Idade mínima dos alunos para participar da turma, em meses.", example = "72")
    private Integer idadeMinimaMeses;

    @Schema(description = "Idade máxima dos alunos para participar da turma, em meses.", example = "144")
    private Integer idadeMaximaMeses;

    @Schema(description = "Lista de níveis de alunos permitidos para essa turma.", example = "[1, 2, 3]")
    private List<Integer> niveis;

    @Schema(description = "Código do nível da modalidade no sistema MGB.", example = "1")
    private String nivelCodigoMgb;

    public List<Integer> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<Integer> niveis) {
        this.niveis = niveis;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getTurma() {
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public boolean isColetiva() {
        return coletiva;
    }

    public void setColetiva(boolean coletiva) {
        this.coletiva = coletiva;
    }

    public boolean isBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public String getNivelCodigoMgb() {
        return nivelCodigoMgb;
    }

    public void setNivelCodigoMgb(String nivelCodigoMgb) {
        this.nivelCodigoMgb = nivelCodigoMgb;
    }

}
