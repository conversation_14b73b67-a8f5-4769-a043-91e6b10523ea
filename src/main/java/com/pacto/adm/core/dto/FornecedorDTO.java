package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Fornecedor", description = "Informações dos fornecedores")
public class FornecedorDTO {

    @Schema(description = "Cõdigo único identificador do fornecedor", example = "33")
    private Integer codigo;

    @Schema(description = "Nome do fornecedor", example = "Fornecedor Pacto")
    private String nome;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
