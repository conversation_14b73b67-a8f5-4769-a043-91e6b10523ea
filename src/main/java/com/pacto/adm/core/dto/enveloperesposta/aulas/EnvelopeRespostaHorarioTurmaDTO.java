package com.pacto.adm.core.dto.enveloperesposta.aulas;

import com.pacto.adm.core.dto.HorarioTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.turma.EnvelopeRespostaNivelTurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaHorarioTurmaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private HorarioTurmaDTO content;

    public HorarioTurmaDTO getContent() {
        return content;
    }

    public void setContent(HorarioTurmaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 50, "
                    + "\"diaSemanaNumero\": 2, "
                    + "\"horaInicial\": \"08:00\", "
                    + "\"horaFinal\": \"09:00\", "
                    + "\"ambiente\": {" + EnvelopeRespostaAmbienteDTO.atributos + "}, "
                    + "\"professor\": {" + EnvelopeRespostaColaboradorDTO.atributos + "}, "
                    + "\"nivelTurma\": {" + EnvelopeRespostaNivelTurmaDTO.atributos + "}, "
                    + "\"diaSemana\": \"Segunda-feira\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
