package com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios;

import com.pacto.adm.core.dto.QuestionarioClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.dadosplano.EnvelopeRespostaVinculoDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaProdutoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaQuestionarioClienteDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private QuestionarioClienteDTO content;

    public QuestionarioClienteDTO getContent() {
        return content;
    }

    public void setContent(QuestionarioClienteDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 123, "
                    + "\"cliente\": 2, "
                    + "\"observacao\": \"Respondido antes do prazo\", "
                    + "\"tipoBV\": \"MA\", "
                    + "\"data\": \"2025-04-30T00:00:00.000Z\", "
                    + "\"questionarioPerguntaCliente\": [], "
                    + "\"ultimaAtualizacao\": \"2025-04-30T00:00:00.000Z\", "
                    + "\"evento\": {" + EnvelopeRespostaEventoDTO.atributos + "}, "
                    + "\"questionario\": {" + EnvelopeRespostaQuestionarioDTO.atributos + "}, "
                    + "\"consultor\": {" + EnvelopeRespostaColaboradorDTO.atributos + "}, "
                    + "\"freepass\": {" + EnvelopeRespostaProdutoDTO.atributos + "}, "
                    + "\"vinculos\": [{" + EnvelopeRespostaVinculoDTO.atributos + "}], "
                    + "\"responsavelFreepass\": {" + EnvelopeRespostaAuthDTO.atributos + "}, "
                    + "\"responsavelAlteracaoDataBv\": {" + EnvelopeRespostaAuthDTO.atributos + "}, "
                    + "\"preencherQuestionario\": false, "
                    + "\"possuiAgendamentosConsultorAtual\": true, "
                    + "\"codigoConsultorAntesAlteracao\": 23, "
                    + "\"alterarAgendamentoConsultorAtual\": false, "
                    + "\"origemSistema\": 1";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";



}
