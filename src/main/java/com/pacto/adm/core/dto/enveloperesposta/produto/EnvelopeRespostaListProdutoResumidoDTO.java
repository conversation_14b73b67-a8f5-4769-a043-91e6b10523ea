package com.pacto.adm.core.dto.enveloperesposta.produto;

import com.pacto.adm.core.dto.modalidade.ProdutoResumidoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class EnvelopeRespostaListProdutoResumidoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ProdutoResumidoDTO> content;

    public List<ProdutoResumidoDTO> getContent() {
        return content;
    }

    public void setContent(List<ProdutoResumidoDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaProdutoResumidoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
