package com.pacto.adm.core.dto.contrato.contratoduracao;

import io.swagger.v3.oas.annotations.media.Schema;

public class ContratoDuracaoDTO {
    @Schema(description = "Código único identificador da duração do contrato", example = "1")
    private Integer codigo;

    @Schema(description = "Valor mensal desejado para o contrato", example = "500.00")
    private Double valorDesejadoMensal;

    @Schema(description = "Valor desejado por parcela do contrato", example = "100.00")
    private Double valorDesejadoParcela;

    @Schema(description = "Valor total desejado para o contrato", example = "1200.00")
    private Double valorDesejado;

    @Schema(description = "Tipo de operação realizada no contrato (ex.: ADICIONAR, REMOVER)", example = "ADICIONAR")
    private String tipoOperacao;

    @Schema(description = "Tipo de valor utilizado no contrato (ex.: MENSAL, PARCELA)", example = "MENSAL")
    private String tipoValor;

    @Schema(description = "Valor específico definido para o contrato", example = "600.00")
    private Double valorEspecifico;

    @Schema(description = "Percentual de desconto aplicado ao contrato", example = "10.0")
    private Double percentualDesconto;

    @Schema(description = "Número máximo de parcelas permitidas na condição de pagamento", example = "12")
    private Integer nrMaximoParcelasCondPagamento;

    @Schema(description = "Número de meses de duração do contrato", example = "6")
    private Integer numeroMeses;

    @Schema(description = "Período de carência do contrato (em dias)", example = "30")
    private Integer carencia;

    @Schema(description = "Quantidade de dias extras adicionados ao contrato", example = "15")
    private Integer quantidadeDiasExtra;

    @Schema(description = "Indica se a carência foi alterada manualmente", example = "true")
    private Boolean carenciaAlterada;

    @Schema(description = "Detalhes do crédito de treino associado à duração do contrato")
    private ContratoDuracaoCreditoTreinoDTO contratoDuracaoCreditoTreino;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorDesejadoMensal() {
        return valorDesejadoMensal;
    }

    public void setValorDesejadoMensal(Double valorDesejadoMensal) {
        this.valorDesejadoMensal = valorDesejadoMensal;
    }

    public Double getValorDesejadoParcela() {
        return valorDesejadoParcela;
    }

    public void setValorDesejadoParcela(Double valorDesejadoParcela) {
        this.valorDesejadoParcela = valorDesejadoParcela;
    }

    public Double getValorDesejado() {
        return valorDesejado;
    }

    public void setValorDesejado(Double valorDesejado) {
        this.valorDesejado = valorDesejado;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getTipoValor() {
        return tipoValor;
    }

    public void setTipoValor(String tipoValor) {
        this.tipoValor = tipoValor;
    }

    public Double getValorEspecifico() {
        return valorEspecifico;
    }

    public void setValorEspecifico(Double valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public Double getPercentualDesconto() {
        return percentualDesconto;
    }

    public void setPercentualDesconto(Double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public Integer getNrMaximoParcelasCondPagamento() {
        return nrMaximoParcelasCondPagamento;
    }

    public void setNrMaximoParcelasCondPagamento(Integer nrMaximoParcelasCondPagamento) {
        this.nrMaximoParcelasCondPagamento = nrMaximoParcelasCondPagamento;
    }

    public Integer getNumeroMeses() {
        return numeroMeses;
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public Integer getCarencia() {
        return carencia;
    }

    public void setCarencia(Integer carencia) {
        this.carencia = carencia;
    }

    public Integer getQuantidadeDiasExtra() {
        return quantidadeDiasExtra;
    }

    public void setQuantidadeDiasExtra(Integer quantidadeDiasExtra) {
        this.quantidadeDiasExtra = quantidadeDiasExtra;
    }

    public Boolean getCarenciaAlterada() {
        return carenciaAlterada;
    }

    public void setCarenciaAlterada(Boolean carenciaAlterada) {
        this.carenciaAlterada = carenciaAlterada;
    }

    public ContratoDuracaoCreditoTreinoDTO getContratoDuracaoCreditoTreino() {
        return contratoDuracaoCreditoTreino;
    }

    public void setContratoDuracaoCreditoTreino(ContratoDuracaoCreditoTreinoDTO contratoDuracaoCreditoTreino) {
        this.contratoDuracaoCreditoTreino = contratoDuracaoCreditoTreino;
    }
}
