package com.pacto.adm.core.dto.filtros.metafinanceiraempresa;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class FiltroMetaFinanceiraEmpresaJSON {
    private String parametro;
    private Integer mes;
    private Integer ano;
    private List<Integer> empresas;

    public FiltroMetaFinanceiraEmpresaJSON(String filters) {
        if (StringUtils.hasText(filters)) {
            JSONObject jsonObject = new JSONObject(filters);
            this.parametro = jsonObject.optString("quicksearchValue");

            if (jsonObject.has("empresas")) {
                JSONArray empresas = jsonObject.optJSONArray("empresas");
                this.empresas = new ArrayList<>();
                for (int i = 0; i < empresas.length(); i++) {
                    this.empresas.add(empresas.getInt(i));
                }
            }

            if (jsonObject.has("mes")) {
                this.mes = jsonObject.getInt("mes");
            }

            if (jsonObject.has("ano")) {
                this.ano = jsonObject.getInt("ano");
            }

        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public List<Integer> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Integer> empresas) {
        this.empresas = empresas;
    }
}
