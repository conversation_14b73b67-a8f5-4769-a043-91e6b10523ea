package com.pacto.adm.core.dto.enveloperesposta.log;

import com.pacto.adm.core.dto.LogConciliadoraDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas das requisições envolvendo os logs das conciliadoras")
public class EnvelopeRespostaLogConciliadoraDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private LogConciliadoraDTO content;

    public LogConciliadoraDTO getContent() {
        return content;
    }

    public void setContent(LogConciliadoraDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 5, "
                    + "\"data\": \"2025/04/28T00:00:00Z\", "
                    + "\"sucesso\": false, "
                    + "\"resultado\": \"Transferência realizada\"";

    public final static String requestBody = "{" + atributos + "}";
    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
