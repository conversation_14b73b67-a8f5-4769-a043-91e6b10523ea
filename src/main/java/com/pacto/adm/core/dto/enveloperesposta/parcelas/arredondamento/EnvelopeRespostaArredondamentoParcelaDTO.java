package com.pacto.adm.core.dto.enveloperesposta.parcelas.arredondamento;

import com.pacto.adm.core.dto.negociacao.ArredondamentoParcelaDTO;
import com.pacto.adm.core.dto.negociacao.ParcelasEditarNegociacaoNovo;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaArredondamentoParcelaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ArredondamentoParcelaDTO content;

    public ArredondamentoParcelaDTO getContent() {
        return content;
    }

    public void setContent(ArredondamentoParcelaDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"valorEntrada\": 100.00, "
                    + "\"valorParcelas\": 150.00, "
                    + "\"selecionado\": false";

    public final static String requestBody = "{" +atributos + "}";

    public final static String resposta = "{"
            + "\"content\": {"
            + atributos
            + "}}";
}
