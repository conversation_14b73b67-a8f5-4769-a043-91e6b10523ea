package com.pacto.adm.core.dto.turma;

import com.pacto.adm.core.dto.modalidade.ModalidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Turma", description = "Informações sobre a turma, incluindo código, descrição e modalidade.")
public class TurmaDTO {

    @Schema(description = "Código único identificador da turma.", example = "80")
    private Integer codigo;

    @Schema(description = "Descrição da turma, como nome ou identificação especial.", example = "Turma de Yoga Matinal")
    private String descricao;

    @Schema(description = "Indica se a turma foi escolhida pelo aluno.", example = "true")
    private Boolean turmaEscolhida;

    @Schema(description = "Detalhes da modalidade associada à turma, incluindo nome e características.")
    private ModalidadeDTO modalidade;

    public TurmaDTO(Integer codigo, String descricao, Boolean turmaEscolhida) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.turmaEscolhida = turmaEscolhida;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getTurmaEscolhida() {
        return turmaEscolhida;
    }

    public void setTurmaEscolhida(Boolean turmaEscolhida) {
        this.turmaEscolhida = turmaEscolhida;
    }

    public ModalidadeDTO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeDTO modalidade) {
        this.modalidade = modalidade;
    }
}
