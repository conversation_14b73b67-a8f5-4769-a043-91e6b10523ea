package com.pacto.adm.core.dto;

import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.dao.interfaces.UsuarioDao;
import com.pacto.adm.core.entities.Usuario;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

@Schema(name = "Log de Acesso via Total Pass", description = "Informações do Log de acesso via Total Pass")
public class LogTotalPassDTO {

    @Schema(description = "Código único identificador do Log", example = "944")
    private Integer codigo;


    @Schema(description = "Código da pessoa vinculada", example = "32")
    private Integer pessoa;

    @Schema(description = "Data do registro (Timestamp)", example = "1756512000000")
    private Timestamp dataregistro;

    @Schema(description = "Uri do Acesso", example = "https://api.exemplo.com/v1/12345")
    private String uri;

    @Schema(description = "Chave da API", example = "api_key_7f3d9b1c-8a42-4b7e-a9e2-3f6b12e4c9ab\n")
    private String apikey;

    @Schema(description = "JSON", example = "\"{\\n\" +\n" +
            "              \"  \\\"pessoa\\\": 32,\\n\" +\n" +
            "              \"  \\\"empresa\\\": 1\\n\" +\n" +
            "              \"}\";\n")
    private String json;

    @Schema(description = "Resposta obtida", example = "Entrada realizada com sucesso!")
    private String resposta;

    @Schema(description = "Tempo da resposta", example = "1000")
    private Long tempoResposta;

    @Schema(description = "Tipo", example = "E")
    private String tipo;

    @Schema(description = "Código da empresa", example = "1")
    private Integer empresa;

    @Schema(description = "IP da máquina que realizo o acesso", example = "***********")
    private String ip;

    @Schema(description = "Código do usuário", example = "1")
    private Integer usuario;

    @Schema(description = "Origem do acesso", example = "PACTO ACADEMIA")
    private String origem;

    @Schema(description = "Status", example = "200")
    private String status;

    @Schema(description = "Informações do usuário")
    private UsuarioDTO usuarioDTO;

    public String getStatus() {
        if(resposta.equals("204")){
            status = "Liberado";
        } else{
            status = "Negado";
        }
        return status;
    }

    public void setStatus(String status) {
        status = status;
    }

    public void setDataRegistroInstant(Instant instant) {
        this.dataregistro = Timestamp.from(instant);
    }

}
