package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "<PERSON>a Cartão", description = "Informações sobre o dia de cobrança ou vencimento do cartão.")
public class DiaCartaoDTO {

    @Schema(description = "Código identificador do dia do cartão.", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição do dia do cartão, como dia do mês ou condição especial.", example = "Dia 10 de cada mês")
    private String descricao;


    public DiaCartaoDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
