package com.pacto.adm.core.dto.contratomodalidade;

import com.pacto.adm.core.dto.HorarioTurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "ContratoModalidadeHorarioTurma", description = "Informações sobre o horário da turma vinculada à modalidade do contrato, incluindo ocupação e desconto.")
public class ContratoModalidadeHorarioTurmaDTO {

    @Schema(description = "Código único identificador do horário da turma.", example = "40")
    private Integer codigo;

    @Schema(description = "Percentual de ocupação do horário da turma.", example = "75.0")
    private Double percOcupacao;

    @Schema(description = "Percentual de desconto aplicado ao horário da turma.", example = "10.0")
    private Double percDesconto;

    @Schema(description = "Detalhes do horário da turma, incluindo dia da semana e horário de início/fim.")
    private HorarioTurmaDTO horarioTurma;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getPercOcupacao() {
        return percOcupacao;
    }

    public void setPercOcupacao(Double percOcupacao) {
        this.percOcupacao = percOcupacao;
    }

    public Double getPercDesconto() {
        return percDesconto;
    }

    public void setPercDesconto(Double percDesconto) {
        this.percDesconto = percDesconto;
    }

    public HorarioTurmaDTO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaDTO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }
}
