package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração Integração Mentor Web", description = "Configurações para integração com a plataforma Mentor Web.")
public class ConfiguracaoIntegracaoMentorWebDTO {

    @Schema(description = "Indica se a integração está habilitada.", example = "true")
    private boolean habilitada;

    @Schema(description = "URL de integração com o Mentor Web.", example = "https://api.mentorweb.com/integracao")
    private String url;

    @Schema(description = "Serviço utilizado na integração com o Mentor Web.", example = "cadastroAluno")
    private String servico;

    @Schema(description = "Usuário para autenticação na plataforma Mentor Web.", example = "usuario_mentor")
    private String user = "";

    @Schema(description = "Senha para autenticação na plataforma Mentor Web.", example = "senha123")
    private String password = "";

    @Schema(description = "Dados da empresa vinculada à integração.")
    private EmpresaDTO empresa;

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getServico() {
        return servico;
    }

    public void setServico(String servico) {
        this.servico = servico;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
