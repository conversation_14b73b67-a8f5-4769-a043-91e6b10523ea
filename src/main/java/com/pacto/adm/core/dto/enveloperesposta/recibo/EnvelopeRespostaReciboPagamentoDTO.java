package com.pacto.adm.core.dto.enveloperesposta.recibo;

import com.pacto.adm.core.dto.ReciboPagamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pagamento.EnvelopeRespostaMovPagamentoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaReciboPagamentoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ReciboPagamentoDTO content;

    public ReciboPagamentoDTO getContent() {
        return content;
    }

    public void setContent(ReciboPagamentoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"valorTotal\": 299.99, "
                    + "\"nomePessoaPagador\": \"<PERSON>\", "
                    + "\"data\": \"2023-10-15\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"pagamentos\": [{" + EnvelopeRespostaMovPagamentoDTO.atributos + "}]";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
