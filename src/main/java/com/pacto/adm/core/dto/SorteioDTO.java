package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@JsonInclude
@Schema(name = "Sorteio", description = "Detalhes do sorteio e suas informações associadas.")
public class SorteioDTO {

    @Schema(description = "Código único identificador do sorteio.", example = "123")
    private Integer codigo;

    @Schema(description = "Nome do sorteio.", example = "Sorteio de Aniversário")
    private String nome;

    @Schema(description = "Data do sorteio.", example = "2025-06-15T10:00:00Z")
    private Date dataSorteio;

    @Schema(description = "Data associada ao sorteio, em formato de texto.", example = "15/06/2025")
    private String data;

    @Schema(description = "Observações adicionais sobre o sorteio.", example = "Sorteio válido para todos os clientes cadastrados.")
    private String observacoes;

    @Schema(description = "Detalhes do cliente associado ao sorteio.", example = "João Silva")
    private ClienteDTO cliente;

    @Schema(description = "Detalhes do usuário que gerenciou o sorteio.")
    private UsuarioDTO usuario;

    @Schema(description = "Nome do usuário associado ao sorteio.", example = "João Silva")
    private String nomeUsuario;

    @Schema(description = "Lista de parcelas associadas ao sorteio.", example = "[1, 2, 3, 4, 5]")
    private List<Integer> parcelas;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataSorteio() {
        return dataSorteio;
    }

    public void setDataSorteio(Date dataSorteio) {
        this.dataSorteio = dataSorteio;
    }

    public String getObservacoes() {
        return observacoes;
    }

    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public List<Integer> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<Integer> parcelas) {
        this.parcelas = parcelas;
    }
}
