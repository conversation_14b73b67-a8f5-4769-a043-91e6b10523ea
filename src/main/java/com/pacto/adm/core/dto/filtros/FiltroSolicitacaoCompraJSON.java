package com.pacto.adm.core.dto.filtros;

import com.pacto.adm.core.enumerador.SituacaoSolicitacaoCompraEnum;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class FiltroSolicitacaoCompraJSON {
    private String parametro;
    private String titulo;
    private Date dataSolicitacaoStart;
    private Date dataSolicitacaoEnd;
    private SituacaoSolicitacaoCompraEnum situacao;

    public FiltroSolicitacaoCompraJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
            if (filters.has("titulo")) {
                this.titulo = filters.getString("titulo");
            }
            if (filters.has("dataSolicitacao_start") && filters.has("dataSolicitacao_end")) {
                try {
                    this.dataSolicitacaoStart = Calendario.primeiraHoraDia(Uteis.getDate(filters.getString("dataSolicitacao_start"), "yyyy-MM-dd"));
                    this.dataSolicitacaoEnd = Calendario.ultimaHoraDia(Uteis.getDate(filters.getString("dataSolicitacao_end"), "yyyy-MM-dd"));
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            }
            if (filters.has("situacao")) {
                this.situacao = SituacaoSolicitacaoCompraEnum.valueOf(filters.getString("situacao"));
            }
        }
    }
}
