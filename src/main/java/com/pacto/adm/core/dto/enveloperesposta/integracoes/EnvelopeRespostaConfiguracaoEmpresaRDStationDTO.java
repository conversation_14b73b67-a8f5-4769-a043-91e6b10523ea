package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaRDStationDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoEmpresaRDStationDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoEmpresaRDStationDTO content;

    public ConfiguracaoEmpresaRDStationDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoEmpresaRDStationDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 12345, "
                    + "\"acaoObjecao\": 1, "
                    + "\"empresaUsaRd\": true, "
                    + "\"chavePublica\": \"chave-publica-xyz123\", "
                    + "\"chavePrivada\": \"chave-privada-abc456\", "
                    + "\"horaLimite\": \"18:00\", "
                    + "\"eventWeebHook\": \"https://api.exemplo.com/webhook\", "
                    + "\"responsavelPadrao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"apiV13Link\": \"https://api.rdstation.com.br/v13\", "
                    + "\"urlRdStation\": \"https://www.rdstation.com.br\", "
                    + "\"urlWebHookRd\": \"https://api.exemplo.com/webhook-rd\", "
                    + "\"configAtualizarAlunoRdStationMarketing\": true, "
                    + "\"accessTokenRdStationMarketing\": \"access-token-xyz123\", "
                    + "\"refreshTokenRdStationMarketing\": \"refresh-token-abc456\"";

    public static final String atributosSemEmpresa =
            "\"codigo\": 12345, "
                    + "\"acaoObjecao\": 1, "
                    + "\"empresaUsaRd\": true, "
                    + "\"chavePublica\": \"chave-publica-xyz123\", "
                    + "\"chavePrivada\": \"chave-privada-abc456\", "
                    + "\"horaLimite\": \"18:00\", "
                    + "\"eventWeebHook\": \"https://api.exemplo.com/webhook\", "
                    + "\"responsavelPadrao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\", "
                    + "\"apiV13Link\": \"https://api.rdstation.com.br/v13\", "
                    + "\"urlRdStation\": \"https://www.rdstation.com.br\", "
                    + "\"urlWebHookRd\": \"https://api.exemplo.com/webhook-rd\", "
                    + "\"configAtualizarAlunoRdStationMarketing\": true, "
                    + "\"accessTokenRdStationMarketing\": \"access-token-xyz123\", "
                    + "\"refreshTokenRdStationMarketing\": \"refresh-token-abc456\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
