package com.pacto.adm.core.dto.colaborador;

import com.pacto.adm.core.dto.PessoaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Colaborador Aniversariante", description = "Informações sobre colaboradores aniversariantes")
public class ColaboradorAniversarianteDTO {

    @Schema(description = "Código único identificador do colaborador", example = "123")
    private Integer codigo;

    @Schema(description = "Informações da pessoa associada ao colaborador")
    private PessoaDTO pessoa;

    @Schema(description = "Situação atual do colaborador. \n\n" +
            "**Valores disponíveis**\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- NA (INATIVO)\n", example = "AT")
    private String situacao;

    @Schema(description = "CPF do colaborador", example = "123.456.789-00")
    private String cpf;

    public ColaboradorAniversarianteDTO(Integer codigo, PessoaDTO pessoa, String situacao, String cpf) {
        this.codigo = codigo;
        this.pessoa = pessoa;
        this.situacao = situacao;
        this.cpf = cpf;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }
}
