package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Representação do log de registro de alterações no sistema")
public class LogDTO {

    @Schema(description = "Código único identificador do Log", example = "135")
    private Integer codigo;

    @Schema(description = "Nome da entidade no banco de dados.", example = "MODALIDADE")
    private String nomeEntidade;

    @Schema(description = "Nome descritivo da entidade no banco de dados", example = "Modalidadees")
    private String nomeEntidadeDescricao;

    @Schema(description = "Chave Primaria do Log", example = "5")
    private String chavePrimaria;

    @Schema(description = "Chave primária da entidade relacionada ao log", example = "1")
    private String chavePrimariaEntidadeSubordinada;

    @Schema(description = "Nome do campo que teve alteração", example = "nome")
    private String nomeCampo;

    @Schema(description = "Valor anterior do campo", example = "Academiaa.")
    private String valorCampoAnterior;

    @Schema(description = "Valor do campo depois de alterado", example = "Academia")
    private String valorCampoAlterado;

    @Schema(description = "Data hora que a alteração ocorreu", example = "2025-04-28T00:00:00Z")
    private Date dataAlteracao;

    @Schema(description = "Nome da pessoa/empresa que foi responsável pela alteração", example = "PACTO")
    private String responsavelAlteracao;

    @Schema(description = "Operação que foi realizada", example = "ALTERAÇÃO")
    private String operacao;

    @Schema(description = "Código da pessoa", example = "1")
    private Integer pessoa;

    @Schema(description = "Código do cliente", example = "1")
    private Integer cliente;

    @Schema(description = "Descrição detalhada do log", example = "Alteração no campo nome")
    private String descricao;

    // Campos da nova estrutura de log
    // Para manter a compatibilidade dos locais que utilizam a consulta por log antiga será mantido o mesmo
    @Schema(description = "Chave do Log", example = "5")
    private String chave;

    @Schema(description = "Usuário relacionado ao Log", example = "PACTO")
    private String usuario;

    @Schema(description = "Data hora que o log foi registrado", example = "28/04/2025 - 10:40")
    private String dia;

    @Schema(description = "Horário que o log foi registrado (Horas:Minutos:Segundos)", example = "10:40:06")
    private String hora;

    @Schema(description = "Identificador descritivo do log", example = "Modalidade")
    private String identificador;

    @Schema(description = "Alterações que foram realizadas")
    private List<LogAlteracoesDTO> alteracoes;

    @Schema(description = "Origem do log", example = "CAMPANHA")
    private String origem;
    private ClienteDTO clienteDTO;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public String getNomeEntidadeDescricao() {
        return nomeEntidadeDescricao;
    }

    public void setNomeEntidadeDescricao(String nomeEntidadeDescricao) {
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
    }

    public String getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public String getChavePrimariaEntidadeSubordinada() {
        return chavePrimariaEntidadeSubordinada;
    }

    public void setChavePrimariaEntidadeSubordinada(String chavePrimariaEntidadeSubordinada) {
        this.chavePrimariaEntidadeSubordinada = chavePrimariaEntidadeSubordinada;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }

    public void setNomeCampo(String nomeCampo) {
        this.nomeCampo = nomeCampo;
    }

    public String getValorCampoAnterior() {
        return valorCampoAnterior;
    }

    public void setValorCampoAnterior(String valorCampoAnterior) {
        this.valorCampoAnterior = valorCampoAnterior;
    }

    public String getValorCampoAlterado() {
        return valorCampoAlterado;
    }

    public void setValorCampoAlterado(String valorCampoAlterado) {
        this.valorCampoAlterado = valorCampoAlterado;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    public String getResponsavelAlteracao() {
        return responsavelAlteracao;
    }

    public void setResponsavelAlteracao(String responsavelAlteracao) {
        this.responsavelAlteracao = responsavelAlteracao;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public List<LogAlteracoesDTO> getAlteracoes() {
        return alteracoes;
    }

    public void setAlteracoes(List<LogAlteracoesDTO> alteracoes) {
        this.alteracoes = alteracoes;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    /**
     * Método usado no BI - Controle de operações de Exceção
     * para obter o nome do aluno que está no log
     * @return
     */
    public String getNomeExclusaoVisitantes() {
        //nome do aluno gravado no log - uso na tela de resumo para facilitar
        final String PREFIXO_NOME = "Nome = ";
        if (valorCampoAnterior != null && valorCampoAnterior.contains(PREFIXO_NOME)) {
            if (valorCampoAlterado != null) {
                return valorCampoAnterior.substring(valorCampoAlterado.indexOf(PREFIXO_NOME) + PREFIXO_NOME.length());
            }
        }

        if (valorCampoAlterado != null && valorCampoAlterado.contains(PREFIXO_NOME.replace("=", ":"))) {
            if (valorCampoAnterior != null) {
                return valorCampoAnterior.substring(valorCampoAnterior.indexOf(PREFIXO_NOME) + PREFIXO_NOME.length());
            }
        }

        if (valorCampoAlterado != null && !valorCampoAlterado.isEmpty()) {
            if (valorCampoAlterado.contains(PREFIXO_NOME)) {
                int indexNome = valorCampoAlterado.indexOf(PREFIXO_NOME) + 7;
                int indexAntesNome = valorCampoAlterado.indexOf("Data de Nasc. = ");
                if (indexNome >= 0 && indexAntesNome >= 0) {
                    return valorCampoAlterado.substring(indexNome, indexAntesNome);
                }
            } else if (valorCampoAlterado.contains("nome do aluno =")) {
                int indexNome = valorCampoAlterado.indexOf("nome do aluno =") + 16;
                if (valorCampoAlterado.contains("valor da parcela = ")) {
                    int indexAntesNome = valorCampoAlterado.indexOf("valor da parcela = ");
                    if (indexNome >= 0 && indexAntesNome >= 0) {
                        return valorCampoAlterado.substring(indexNome, indexAntesNome);
                    }
                } else {
                    return valorCampoAlterado.substring(indexNome);
                }
            }
        }

        return "";
    }

    public ClienteDTO getClienteDTO() {
        return clienteDTO;
    }

    public void setClienteDTO(ClienteDTO clienteDTO) {
        this.clienteDTO = clienteDTO;
    }

}
