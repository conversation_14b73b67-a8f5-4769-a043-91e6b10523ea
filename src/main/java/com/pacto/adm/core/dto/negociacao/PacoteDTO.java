package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Pacote", description = "Informações sobre um pacote de modalidades, incluindo valor, quantidade e configurações adicionais.")
public class PacoteDTO {

    @Schema(description = "Código identificador do pacote.", example = "1")
    private Integer codigo;

    @Schema(description = "Quantidade de modalidades incluídas no pacote.", example = "3")
    private Integer qtdeModalidades;

    @Schema(description = "Descrição do pacote.", example = "Pacote Fitness Avançado")
    private String descricao;

    @Schema(description = "Valor total do pacote.", example = "199.90")
    private Double valor;

    @Schema(description = "Indica se o pacote é um adicional ao plano principal.", example = "true")
    private Boolean adicional;

    @Schema(description = "Indica se o pacote é o padrão oferecido.", example = "false")
    private Boolean padrao;

    @Schema(description = "Indica se o pacote contém modalidades específicas.", example = "true")
    private Boolean modalidadesEspecificas;

    @Schema(description = "Lista de códigos das modalidades associadas ao pacote.", example = "[1, 2, 3]")
    private List<Integer> modalidades;

    public List<Integer> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<Integer> modalidades) {
        this.modalidades = modalidades;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Boolean getAdicional() {
        return adicional;
    }

    public void setAdicional(Boolean adicional) {
        this.adicional = adicional;
    }

    public Boolean getPadrao() {
        return padrao;
    }

    public void setPadrao(Boolean padrao) {
        this.padrao = padrao;
    }

    public Boolean getModalidadesEspecificas() {
        return modalidadesEspecificas;
    }

    public void setModalidadesEspecificas(Boolean modalidadesEspecificas) {
        this.modalidadesEspecificas = modalidadesEspecificas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getQtdeModalidades() {
        return qtdeModalidades;
    }

    public void setQtdeModalidades(Integer qtdeModalidades) {
        this.qtdeModalidades = qtdeModalidades;
    }
}
