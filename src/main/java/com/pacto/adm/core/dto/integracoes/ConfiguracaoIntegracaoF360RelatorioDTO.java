package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração F360 Relatório", description = "Configurações para integração com o sistema F360 para geração de relatórios.")
public class ConfiguracaoIntegracaoF360RelatorioDTO {

    @Schema(description = "Indica se a integração com o F360 para relatórios está habilitada.", example = "true")
    private boolean habilitada;

    @Schema(description = "Endereço do servidor FTP utilizado para a integração com o F360.", example = "ftp.exemplo.com")
    private String ftpServer;

    @Schema(description = "Porta do servidor FTP para a integração com o F360.", example = "21")
    private Integer ftpPort;

    @Schema(description = "Usuário para autenticação no servidor FTP da integração F360.", example = "usuario_ftp")
    private String user;

    @Schema(description = "Senha para autenticação no servidor FTP da integração F360.", example = "senha-ftp-123")
    private String password;

    @Schema(description = "Diretório no servidor FTP onde os relatórios do F360 são armazenados.", example = "/relatorios/f360/")
    private String dir;

    @Schema(description = "Indica se os relatórios são gerados quinzenalmente.", example = "true")
    private boolean quinzenal;

    @Schema(description = "Detalhes da empresa associada à configuração de integração F360 para relatórios.")
    private EmpresaDTO empresa;

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getFtpServer() {
        return ftpServer;
    }

    public void setFtpServer(String ftpServer) {
        this.ftpServer = ftpServer;
    }

    public Integer getFtpPort() {
        return ftpPort;
    }

    public void setFtpPort(Integer ftpPort) {
        this.ftpPort = ftpPort;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public boolean isQuinzenal() {
        return quinzenal;
    }

    public void setQuinzenal(boolean quinzenal) {
        this.quinzenal = quinzenal;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
