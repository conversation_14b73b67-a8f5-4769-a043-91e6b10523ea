package com.pacto.adm.core.dto.indicacao;

import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.indicacao.evento.EventoDTO;
import com.pacto.adm.core.dto.indicado.IndicadoDTO;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@Schema(name = "Indicacao", description = "Informações sobre a indicação, incluindo dados do cliente e colaborador responsável pela indicação.")
public class IndicacaoDTO {

    @Schema(description = "Código único identificador da indicação.", example = "5678")
    private Integer codigo;

    @Schema(description = "Data em que a indicação foi realizada.", example = "2023-06-15T00:00:00Z")
    private Date dia;

    @Schema(description = "Observações adicionais sobre a indicação.", example = "Indicação de cliente novo para planos A e B.")
    private String observacao;

    @Schema(description = "Origem do sistema que registrou a indicação. \n\n" +
            "**Valores disponíveis**\n" +
            "- 1 (ZillyonWeb)\n" +
            "- 2 (Agenda Web)\n" +
            "- 3 (Pacto Treino)\n" +
            "- 4 (App Treino)\n" +
            "- 5 (App Professor)\n" +
            "- 6 (Autoatendimento)\n" +
            "- 7 (Site Vendas)\n" +
            "- 8 (Buzz Lead)\n" +
            "- 9 (Vendas 2.0)\n" +
            "- 10 (App do consultor)\n" +
            "- 11 (Booking Gympass)\n" +
            "- 12 (Fila de espera)\n" +
            "- 13 (Importação API)\n" +
            "- 14 (Hubspot Lead)\n" +
            "- 15 (CRM Meta Diaria)\n" +
            "- 16 (Pacto Flow)\n" +
            "- 17 (Nova Tela de Negociação)\n" +
            "- 18 (Nova Tela de Caixa Aberto)\n" +
            "- 19 (Nova Tela de Cadastro Cliente)\n" +
            "- 20 (API Sistema Pacto)\n" +
            "- 21 (Conversas IA)\n" +
            "- 22 (ZW Boot)", example = "1")
    private OrigemSistemaEnum origemSistema;

    @Schema(description = "Cliente que realizou a indicação.")
    private ClienteDTO clienteQueIndicou;

    @Schema(description = "Colaborador responsável por realizar a indicação.")
    private ColaboradorDTO colaboradorQueIndicou;

    @Schema(description = "Colaborador responsável pela gestão da indicação.")
    private UsuarioDTO colaboradorResponsavel;

    @Schema(description = "Evento relacionado à indicação, caso haja.")
    private EventoDTO evento;

    @Schema(description = "Usuário responsável pelo cadastro da indicação no sistema.")
    private UsuarioDTO responsavelCadastro;

    @Schema(description = "Lista de indicados associados a esta indicação.")
    private List<IndicadoDTO> indicados;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public ClienteDTO getClienteQueIndicou() {
        return clienteQueIndicou;
    }

    public void setClienteQueIndicou(ClienteDTO clienteQueIndicou) {
        this.clienteQueIndicou = clienteQueIndicou;
    }

    public ColaboradorDTO getColaboradorQueIndicou() {
        return colaboradorQueIndicou;
    }

    public void setColaboradorQueIndicou(ColaboradorDTO colaboradorQueIndicou) {
        this.colaboradorQueIndicou = colaboradorQueIndicou;
    }

    public UsuarioDTO getColaboradorResponsavel() {
        return colaboradorResponsavel;
    }

    public void setColaboradorResponsavel(UsuarioDTO colaboradorResponsavel) {
        this.colaboradorResponsavel = colaboradorResponsavel;
    }

    public EventoDTO getEvento() {
        return evento;
    }

    public void setEvento(EventoDTO evento) {
        this.evento = evento;
    }

    public UsuarioDTO getResponsavelCadastro() {
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(UsuarioDTO responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public List<IndicadoDTO> getIndicados() {
        return indicados;
    }

    public void setIndicados(List<IndicadoDTO> indicados) {
        this.indicados = indicados;
    }
}
