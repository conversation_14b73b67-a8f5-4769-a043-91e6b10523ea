package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name =  "Questionário e Perguntas", description = "Informações dos questionários dos clientes")
public class QuestionarioPerguntaClienteDTO {

    @Schema(description = "Código único identificador", example = "1")
    private Integer codigo;
    @Schema(description = "Código identificador do questionário", example = "2")
    private Integer questionarioCliente;
    @Schema(description = "Perguntas do questionário")
    private PerguntaClienteDTO perguntaCliente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getQuestionarioCliente() {
        return questionarioCliente;
    }

    public void setQuestionarioCliente(Integer questionarioCliente) {
        this.questionarioCliente = questionarioCliente;
    }

    public PerguntaClienteDTO getPerguntaCliente() {
        return perguntaCliente;
    }

    public void setPerguntaCliente(PerguntaClienteDTO perguntaCliente) {
        this.perguntaCliente = perguntaCliente;
    }
}
