package com.pacto.adm.core.dto.enveloperesposta.contrato.config;

import com.pacto.adm.core.dto.negociacao.ConfigsAvancadasDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaConfigsAvancadasDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfigsAvancadasDTO content;

    public ConfigsAvancadasDTO getContent() {
        return content;
    }

    public void setContent(ConfigsAvancadasDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"cobrarMatricula\": true, "
                    + "\"cobrarProdutosSeparados\": false, "
                    + "\"diaPrimeiraParcela\": 1718054400000, "
                    + "\"diaProrata\": 10, "
                    + "\"escolherDiaPrimeiraParcela\": true, "
                    + "\"dividirProdutoParcela\": false, "
                    + "\"escolherDiaProrata\": true, "
                    + "\"vezesCobrarMatricula\": 1, "
                    + "\"vezesCobrarProdutosSeparados\": 2";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
