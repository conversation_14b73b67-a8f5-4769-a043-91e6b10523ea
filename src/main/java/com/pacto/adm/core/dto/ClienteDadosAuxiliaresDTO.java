package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Dados Auxiliares do Cliente", description = "Informações dos dados auxiliares do cliente")
public class ClienteDadosAuxiliaresDTO {

    @Schema(description = "Data de inclusão no SPC")
    private Long dataInclusaoSpc;

    @Schema(description = "Parcela que foi incluída no SPC")
    private List<MovParcelaDTO> parcelasSpc;

    public Long getDataInclusaoSpc() {
        return dataInclusaoSpc;
    }

    public void setDataInclusaoSpc(Long dataInclusaoSpc) {
        this.dataInclusaoSpc = dataInclusaoSpc;
    }

    public List<MovParcelaDTO> getParcelasSpc() {
        return parcelasSpc;
    }

    public void setParcelasSpc(List<MovParcelaDTO> parcelasSpc) {
        this.parcelasSpc = parcelasSpc;
    }
}
