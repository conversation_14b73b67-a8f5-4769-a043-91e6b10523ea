package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoWeHelpDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoWeHelpDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoWeHelpDTO content;

    public ConfiguracaoIntegracaoWeHelpDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoWeHelpDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"habilitada\": true, "
                    + "\"cpfCodigoInternoWeHelp\": false, "
                    + "\"chave\": \"chave-secreta-123\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";
    public static final String atributosSemEmpresa =
            "\"habilitada\": true, "
                    + "\"cpfCodigoInternoWeHelp\": false, "
                    + "\"chave\": \"chave-secreta-123\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
