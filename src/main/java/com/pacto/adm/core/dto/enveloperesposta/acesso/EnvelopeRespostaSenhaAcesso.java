package com.pacto.adm.core.dto.enveloperesposta.acesso;

import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Atestado", description = "Representação das respostas contendo as informações do atestado")
public class EnvelopeRespostaSenhaAcesso {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "abc1234")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public static final String atributos =
            "\"matricula\": 123, "
                    + "\"pessoa\": {" + EnvelopeRespostaPessoaDTO.atributos + "}, "
                    + "\"senhaAcesso\": \"abc1234\", "
                    + "\"confirmaSenhaAcesso\": \"abc1234\", "
                    + "\"habilitaSenhaAcesso\": false";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";



}
