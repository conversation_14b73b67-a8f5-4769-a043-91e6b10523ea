package com.pacto.adm.core.dto.enveloperesposta.indicacao;

import com.pacto.adm.core.dto.enveloperesposta.aulas.experimental.EnvelopeRespostaObjecaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.indicado.IndicadoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaIndicadoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private IndicadoDTO content;

    public IndicadoDTO getContent() {
        return content;
    }

    public void setContent(IndicadoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1234, "
                    + "\"cpf\": \"123.456.789-00\", "
                    + "\"dataLancamento\": \"2023-06-01T00:00:00Z\", "
                    + "\"email\": \"<EMAIL>\", "
                    + "\"lead\": true, "
                    + "\"nomeConsulta\": \"João da Silva\", "
                    + "\"nomeIndicado\": \"Maria Oliveira\", "
                    + "\"origemSistema\": 1, "
                    + "\"telefone\": \"(11) 987654321\", "
                    + "\"telefoneIndicado\": \"(11) 998765432\", "
                    + "\"cliente\": {" + EnvelopeRespostaClienteDTO.atributos + "}, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"indicacao\": {" + EnvelopeRespostaIndicacaoDTO.atributos + "}, "
                    + "\"objecao\": {" + EnvelopeRespostaObjecaoDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
