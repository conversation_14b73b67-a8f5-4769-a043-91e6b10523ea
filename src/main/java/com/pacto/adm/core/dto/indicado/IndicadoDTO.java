package com.pacto.adm.core.dto.indicado;

import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.indicacao.IndicacaoDTO;
import com.pacto.adm.core.dto.objecao.ObjecaoDTO;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Indicado", description = "Informações sobre os indicados, incluindo dados pessoais e relacionados à indicação.")
public class IndicadoDTO {

    @Schema(description = "Código único identificador do indicado.", example = "1234")
    private Integer codigo;

    @Schema(description = "CPF do indicado.", example = "123.456.789-00")
    private String cpf;

    @Schema(description = "Data de lançamento da indicação do indicado.", example = "2023-06-01T00:00:00Z")
    private Date dataLancamento;

    @Schema(description = "Email do indicado.", example = "<EMAIL>")
    private String email;

    @Schema(description = "Indica se o indicado é um lead.", example = "true")
    private Boolean lead;

    @Schema(description = "Nome utilizado para consulta do indicado.", example = "João da Silva")
    private String nomeConsulta;

    @Schema(description = "Nome do indicado.", example = "Maria Oliveira")
    private String nomeIndicado;

    @Schema(description = "Origem do sistema que registrou a indicação. \n\n" +
            "**Valores disponíveis**\n" +
            "- 1 (ZillyonWeb)\n" +
            "- 2 (Agenda Web)\n" +
            "- 3 (Pacto Treino)\n" +
            "- 4 (App Treino)\n" +
            "- 5 (App Professor)\n" +
            "- 6 (Autoatendimento)\n" +
            "- 7 (Site Vendas)\n" +
            "- 8 (Buzz Lead)\n" +
            "- 9 (Vendas 2.0)\n" +
            "- 10 (App do consultor)\n" +
            "- 11 (Booking Gympass)\n" +
            "- 12 (Fila de espera)\n" +
            "- 13 (Importação API)\n" +
            "- 14 (Hubspot Lead)\n" +
            "- 15 (CRM Meta Diaria)\n" +
            "- 16 (Pacto Flow)\n" +
            "- 17 (Nova Tela de Negociação)\n" +
            "- 18 (Nova Tela de Caixa Aberto)\n" +
            "- 19 (Nova Tela de Cadastro Cliente)\n" +
            "- 20 (API Sistema Pacto)\n" +
            "- 21 (Conversas IA)\n" +
            "- 22 (ZW Boot)", example = "1")
    private OrigemSistemaEnum origemSistema;

    @Schema(description = "Telefone de contato do indicado.", example = "(11) 987654321")
    private String telefone;

    @Schema(description = "Telefone adicional do indicado.", example = "(11) 998765432")
    private String telefoneIndicado;

    @Schema(description = "Informações do cliente relacionado ao indicado.")
    private ClienteDTO cliente;

    @Schema(description = "Informações da empresa associada ao indicado.")
    private EmpresaDTO empresa;

    @Schema(description = "Informações sobre a indicação do indicado.")
    private IndicacaoDTO indicacao;

    @Schema(description = "Informações sobre a objeção relacionada ao indicado, caso haja.")
    private ObjecaoDTO objecao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getLead() {
        return lead;
    }

    public void setLead(Boolean lead) {
        this.lead = lead;
    }

    public String getNomeConsulta() {
        return nomeConsulta;
    }

    public void setNomeConsulta(String nomeConsulta) {
        this.nomeConsulta = nomeConsulta;
    }

    public String getNomeIndicado() {
        return nomeIndicado;
    }

    public void setNomeIndicado(String nomeIndicado) {
        this.nomeIndicado = nomeIndicado;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getTelefoneIndicado() {
        return telefoneIndicado;
    }

    public void setTelefoneIndicado(String telefoneIndicado) {
        this.telefoneIndicado = telefoneIndicado;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public IndicacaoDTO getIndicacao() {
        return indicacao;
    }

    public void setIndicacao(IndicacaoDTO indicacao) {
        this.indicacao = indicacao;
    }

    public ObjecaoDTO getObjecao() {
        return objecao;
    }

    public void setObjecao(ObjecaoDTO objecao) {
        this.objecao = objecao;
    }
}
