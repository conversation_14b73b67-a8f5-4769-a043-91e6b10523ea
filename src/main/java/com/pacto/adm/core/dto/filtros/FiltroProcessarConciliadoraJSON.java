package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;
import java.util.Date;

public class FiltroProcessarConciliadoraJSON {

    Integer codigoEmpresa;
    Integer codigoRecibo;
    Date dataInicial;
    Date dataFinal;

    public FiltroProcessarConciliadoraJSON(JSONObject filters) {
        if (filters != null) {
            this.codigoEmpresa = filters.optInt("codigoEmpresa");
            this.codigoRecibo = filters.optInt("codigoRecibo");
            if (filters.optLong("dataInicial") > 0) {
                dataInicial = new Date(filters.optLong("dataInicial"));
            }
            if (filters.optLong("dataFinal") > 0) {
                dataFinal = new Date(filters.optLong("dataFinal"));
            }
         }
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public Integer getCodigoRecibo() {
        return codigoRecibo;
    }

    public void setCodigoRecibo(Integer codigoRecibo) {
        this.codigoRecibo = codigoRecibo;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }
}
