package com.pacto.adm.core.dto.enveloperesposta.cliente.negociacao.check;

import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaHorarioTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.restricao.EnvelopeRespostaClienteRestricaoDTO;
import com.pacto.adm.core.dto.negociacao.CheckNegociacaoDTO;
import com.pacto.adm.core.dto.negociacao.ClienteNegociacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaCheckNegociacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private CheckNegociacaoDTO content;

    public CheckNegociacaoDTO getContent() {
        return content;
    }

    public void setContent(CheckNegociacaoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"planoSugerido\": 5, " +
                    "\"creditosSugerido\": 100, " +
                    "\"planoRenovacao\": 6, " +
                    "\"planoBase\": 2, " +
                    "\"valorParcelasAberto\": 150.00, " +
                    "\"limiteDataRematricula\": 1650518400000, " +
                    "\"finalContratoAtivo\": 1672444800000, " +
                    "\"codigoContratoRenovacao\": 101, " +
                    "\"codigoContratoRematricula\": 102, " +
                    "\"permiteContratoConcomitante\": false, " +
                    "\"nivelAluno\": 3, " +
                    "\"convenioCobranca\": 2, " +
                    "\"tipoAutorizacaoCobranca\": \"PIX\", " +
                    "\"nrsCartao\": \"1234567890123456\", " +
                    "\"validadeCartao\": \"12/25\", " +
                    "\"horariosTurma\": [{" + EnvelopeRespostaHorarioTurmaDTO.atributos + "}], " +
                    "\"usaProdutos\": false, " +
                    "\"usaDesconto\": true, " +
                    "\"clienteRestricoes\": [{" + EnvelopeRespostaClienteRestricaoDTO.atributos +"}]";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
