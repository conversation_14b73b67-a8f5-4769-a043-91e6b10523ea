package com.pacto.adm.core.dto.enveloperesposta.avisointerno;

import com.pacto.adm.core.dto.AvisoInternoDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Resposta Lista de Avisos Internos", description = "Representação das respostas envolvendo lista de avisos internos")
public class EnvelopeRespostaListAvisoInternoDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<AvisoInternoDTO> content;

    public List<AvisoInternoDTO> getContent() {
        return content;
    }

    public void setContent(List<AvisoInternoDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaAvisoInternoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
