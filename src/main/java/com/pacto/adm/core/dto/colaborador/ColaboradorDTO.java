package com.pacto.adm.core.dto.colaborador;

import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Colaborador", description = "Informações do colaborador")
public class ColaboradorDTO {
    @Schema(description = "Código único identificador do colaborador", example = "1")
    private Integer codigo;

    @Schema(description = "Situação atual do colaborador. \n\n" +
            "**Valores disponíveis**\n" +
            "- AT (Ativo)\n" +
            "- IN (Inativo)\n" +
            "- NA (Inativo)\n", example = "AT")
    private String situacao;

    @Schema(description = "Detalhes da pessoa associada ao colaborador")
    private PessoaDTO pessoa;

    @Schema(description = "Detalhes da empresa associada ao colaborador")
    private EmpresaDTO empresa;

    @Schema(description = "Lista de tipos de colaborador associados ao colaborador")
    private List<TipoColaboradorDTO> tiposColaborador;

    public ColaboradorDTO() {
    }

    public ColaboradorDTO(Integer codigo, PessoaDTO pessoa) {
        this.codigo = codigo;
        this.pessoa = pessoa;
    }

    public ColaboradorDTO(Integer codigo, String situacao, PessoaDTO pessoa, EmpresaDTO empresa) {
        this.codigo = codigo;
        this.situacao = situacao;
        this.pessoa = pessoa;
        this.empresa = empresa;
    }

    public ColaboradorDTO(Integer codigo, String situacao, PessoaDTO pessoa) {
        this.codigo = codigo;
        this.situacao = situacao;
        this.pessoa = pessoa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getSituacao() {
        return situacao;
    }

    public String getSituacaoApresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("AT")) {
            return "Ativo";
        }
        if (situacao.equals("NA")) {
            return "Inativo";
        }
        if (situacao.equals("IN")) {
            return "Inativo";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public List<TipoColaboradorDTO> getTiposColaborador() {
        return tiposColaborador;
    }

    public void setTiposColaborador(List<TipoColaboradorDTO> tiposColaborador) {
        this.tiposColaborador = tiposColaborador;
    }
}
