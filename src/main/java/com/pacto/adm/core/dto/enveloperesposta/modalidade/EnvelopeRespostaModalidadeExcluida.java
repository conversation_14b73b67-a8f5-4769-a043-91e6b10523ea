package com.pacto.adm.core.dto.enveloperesposta.modalidade;

import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaArquivoDTO;
import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaProdutoSugeridoDTO;
import com.pacto.adm.core.dto.modalidade.ModalidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaModalidadeExcluida {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "Modalidade excluída!")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public final static String resposta = "{\"content\": \"Modalidade excluída!\"}";

}
