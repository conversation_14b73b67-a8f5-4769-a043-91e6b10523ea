package com.pacto.adm.core.dto.enveloperesposta.empresa;

public class EnvelopeRespostaParceiroFidelidadeDTO {

    public static final String atributos =
              "\"codigo\": 1, "
            + "\"tipoParceiro\": 2, "
            + "\"validarCliente\": true, "
            + "\"clientId\": \"client-id-12345\", "
            + "\"clientSecret\": \"client-secret-67890\", "
            + "\"clientIdRedemption\": \"redemption-client-id-12345\", "
            + "\"clientSecretRedemption\": \"redemption-client-secret-67890\", "
            + "\"codigoLoja\": \"LOJA-001\", "
            + "\"codigoMaquina\": \"MAQUINA-001\", "
            + "\"codigoOferta\": \"OFERTA-001\", "
            + "\"codigoResgate\": \"RESCATE-001\", "
            + "\"tags\": \"tag1, tag2, tag3\", "
            + "\"cpf\": \"123.456.789-00\", "
            + "\"token\": \"token-abcdef12345\", "
            + "\"dataExpiracaoToken\": \"2023-12-31T23:59:59Z\", "
            + "\"ambienteProducao\": true, "
            + "\"parcelaVencidaGeraPonto\": false, "
            + "\"empresa\": 10, "
            + "\"tabelas\": [{" + EnvelopeRespostaTabelaParceiroFidelidadeDTO.atributos + "}], "
            + "\"produtos\": [{" + EnvelopeRespostaProdutoParceiroFidelidadeDTO.atributos + "}]";


}
