package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração Spivi", description = "Configurações para integração com o sistema Spivi.")
public class ConfiguracaoIntegracaoSpiviDTO {

    @Schema(description = "Indica se a integração com o Spivi está habilitada.", example = "true")
    private boolean habilitada;

    @Schema(description = "Nome da fonte utilizada na integração com o Spivi.", example = "spivi-integration-source")
    private String sourceName;

    @Schema(description = "ID do site utilizado na integração com o Spivi.", example = "12345")
    private Integer siteId;

    @Schema(description = "Senha utilizada para autenticação na integração com o Spivi.", example = "spivi-password-xyz")
    private String spiviPassword;

    @Schema(description = "Detalhes da empresa associada à configuração de integração Spivi.")
    private EmpresaDTO empresa;

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public Integer getSiteId() {
        return siteId;
    }

    public void setSiteId(Integer siteId) {
        this.siteId = siteId;
    }

    public String getSpiviPassword() {
        return spiviPassword;
    }

    public void setSpiviPassword(String spiviPassword) {
        this.spiviPassword = spiviPassword;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
