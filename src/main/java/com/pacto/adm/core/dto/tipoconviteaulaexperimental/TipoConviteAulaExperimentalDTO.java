package com.pacto.adm.core.dto.tipoconviteaulaexperimental;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Tipo Convite Aula Experimental", description = "Detalhes do tipo de convite para aulas experimentais, incluindo regras e parâmetros de envio.")
public class TipoConviteAulaExperimentalDTO {

    @Schema(description = "Código único identificador do tipo de convite.", example = "1")
    private Integer codigo;

    @Schema(description = "Indica se o aluno pode enviar convites para aulas experimentais.", example = "true")
    private Boolean alunoPodeEnviarConvite;

    @Schema(description = "Indica se as aulas agendadas não podem ser em dias seguidos.", example = "false")
    private Boolean aulasAgendadasSemDiasSeguido;

    @Schema(description = "Indica se o colaborador pode enviar convites para aulas experimentais.", example = "true")
    private Boolean colaboradorPodeEnviarConvite;

    @Schema(description = "Data de lançamento do tipo de convite.", example = "2023-06-01T00:00:00Z")
    private Date dataLancamento;

    @Schema(description = "Descrição do tipo de convite.", example = "Convite padrão para aulas experimentais de musculação.")
    private String descricao;

    @Schema(description = "Quantidade de aulas experimentais permitidas por convite.", example = "1")
    private Integer quantidadeAulaExperimental;

    @Schema(description = "Quantidade de convites que o aluno pode enviar.", example = "3")
    private Integer quantidadeConviteAlunoPodeEnviar;

    @Schema(description = "Data inicial da vigência do tipo de convite.", example = "2023-06-01T00:00:00Z")
    private Date vigenciaInicial;

    @Schema(description = "Data final da vigência do tipo de convite.", example = "2023-12-31T00:00:00Z")
    private Date vigenciaFinal;

    @Schema(description = "Informações da empresa associada ao tipo de convite.")
    private EmpresaDTO empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAlunoPodeEnviarConvite() {
        return alunoPodeEnviarConvite;
    }

    public void setAlunoPodeEnviarConvite(Boolean alunoPodeEnviarConvite) {
        this.alunoPodeEnviarConvite = alunoPodeEnviarConvite;
    }

    public Boolean getAulasAgendadasSemDiasSeguido() {
        return aulasAgendadasSemDiasSeguido;
    }

    public void setAulasAgendadasSemDiasSeguido(Boolean aulasAgendadasSemDiasSeguido) {
        this.aulasAgendadasSemDiasSeguido = aulasAgendadasSemDiasSeguido;
    }

    public Boolean getColaboradorPodeEnviarConvite() {
        return colaboradorPodeEnviarConvite;
    }

    public void setColaboradorPodeEnviarConvite(Boolean colaboradorPodeEnviarConvite) {
        this.colaboradorPodeEnviarConvite = colaboradorPodeEnviarConvite;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getQuantidadeAulaExperimental() {
        return quantidadeAulaExperimental;
    }

    public void setQuantidadeAulaExperimental(Integer quantidadeAulaExperimental) {
        this.quantidadeAulaExperimental = quantidadeAulaExperimental;
    }

    public Integer getQuantidadeConviteAlunoPodeEnviar() {
        return quantidadeConviteAlunoPodeEnviar;
    }

    public void setQuantidadeConviteAlunoPodeEnviar(Integer quantidadeConviteAlunoPodeEnviar) {
        this.quantidadeConviteAlunoPodeEnviar = quantidadeConviteAlunoPodeEnviar;
    }

    public Date getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(Date vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
