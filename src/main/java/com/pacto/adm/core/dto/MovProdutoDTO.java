package com.pacto.adm.core.dto;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.recibodevolucao.ReciboDevolucaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Schema(name = "Movimentação de Produto", description = "Informações do produto ou serviço adquqirido por uma pessoa")
public class MovProdutoDTO {

    @Schema(description = "Código único identificador da compra de produto", example = "9")
    private Integer codigo;

    @Schema(description = "Contrato associado a compra de produto. Contém informações como código, tipo, vigência e situação.")
    private ContratoDTO contrato;

    @Schema(description = "Empresa associada a compra de produto. Inclui informações como nome fantasia, status e integrações habilitadas.")
    private EmpresaDTO empresa;

    @Schema(description = "Descrição do produto ou serviço adquirido pelo cliente.", example = "MATRÍCULA")
    private String descricao;

    @Schema(description = "Quantidade de unidades do produto ou serviço adquiridas.", example = "1")
    private Integer quantidade;

    @Schema(description = "Preço unitário do produto ou serviço.", example = "89.99")
    private BigDecimal precoUnitario;

    @Schema(description = "Valor total de desconto aplicado ao produto ou serviço.", example = "0.00")
    private BigDecimal valorDesconto;

    @Schema(description = "Valor total final do movimento de produto ou serviço, após aplicação de descontos.", example = "89.99")
    private BigDecimal totalFinal;

    @Schema(description = "Situação atual da venda (ex.: 'PG' para pago).", example = "PG")
    private String situacao;

    @Schema(description = "Data de lançamento da compra do produto ou serviço.", example = "2019-04-18T00:00:00Z")
    private Date dataLancamento;

    @Schema(description = "Data de início da vigência do produto ou serviço.", example = "2019-04-18T00:00:00Z")
    private Date dataInicioVigencia;

    @Schema(description = "Data de término da vigência do produto ou serviço.", example = "2019-05-18T00:00:00Z")
    private Date dataFinalVigencia;

    @Schema(description = "Detalhes do produto ou serviço associado a compra.")
    private ProdutoDTO produto;

    @Schema(description = "Lista de parcelas associadas ao pagamento a venda produto ou serviço.")
    private List<MovParcelaDTO> parcelas;

    @Schema(description = "Lista de recibos de pagamento associados a venda do produto ou serviço.")
    private List<ReciboPagamentoDTO> recibos;

    @Schema(description = "Recibo de devolução associado ao movimento de produto ou serviço, caso exista.")
    private ReciboDevolucaoDTO reciboDevolucao;

    @Schema(description = "Valor parcialmente pago no movimento de produto ou serviço, caso aplicável.", example = "0.00")
    private BigDecimal valorParcialmentePago;

    @Schema(description = "Indica se a compra é renovável automaticamente.", example = "false")
    private boolean renovavelAutomaticamente;

    @Schema(description = "Indica se a compra do produto ou serviço possui vigência definida.", example = "false")
    private boolean comVigencia;
    private VendaAvulsaDTO vendaAvulsa;
    private AulaAvulsaDiariaDTO aulaAvulsaDiaria;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public BigDecimal getPrecoUnitario() {
        return precoUnitario;
    }

    public void setPrecoUnitario(BigDecimal precoUnitario) {
        this.precoUnitario = precoUnitario;
    }

    public BigDecimal getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(BigDecimal valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataInicioVigencia() {
        return dataInicioVigencia;
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    public Date getDataFinalVigencia() {
        return dataFinalVigencia;
    }

    public void setDataFinalVigencia(Date dataFinalVigencia) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public ProdutoDTO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoDTO produto) {
        this.produto = produto;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public BigDecimal getTotalFinal() {
        return totalFinal;
    }

    public void setTotalFinal(BigDecimal totalFinal) {
        this.totalFinal = totalFinal;
    }

    public void setParcelas(List<MovParcelaDTO> parcelas) {
        this.parcelas = parcelas;
    }

    public List<MovParcelaDTO> getParcelas() {
        return parcelas;
    }

    public List<ReciboPagamentoDTO> getRecibos() {
        return recibos;
    }

    public void setRecibos(List<ReciboPagamentoDTO> recibos) {
        this.recibos = recibos;
    }

    public ReciboDevolucaoDTO getReciboDevolucao() {
        return reciboDevolucao;
    }

    public void setReciboDevolucao(ReciboDevolucaoDTO reciboDevolucao) {
        this.reciboDevolucao = reciboDevolucao;
    }

    public BigDecimal getValorParcialmentePago() {
        return valorParcialmentePago;
    }

    public void setValorParcialmentePago(BigDecimal valorParcialmentePago) {
        this.valorParcialmentePago = valorParcialmentePago;
    }


    public boolean isRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public boolean isComVigencia() {
        return comVigencia;
    }

    public void setComVigencia(boolean comVigencia) {
        this.comVigencia = comVigencia;
    }

    @Schema(description = "Indica se é permitido renovar o produto ou serviço.", example = "true")
    public boolean isPermiteRenovar() {
        return this.getProduto() != null &&
                this.getProduto().getTipoVigencia() != null &&
                !this.getProduto().getTipoVigencia().equals("VV") &&
                !this.getProduto().getTipoProduto().equals("TR") &&
                !this.getProduto().getTipoProduto().equals("DS");
    }

    @Schema(description = "Indica se é permitido editar o produto ou serviço.", example = "true")
    public boolean isPermiteEditar() {
        return this.getProduto() != null &&
                this.getProduto().getTipoVigencia() != null &&
                !this.getProduto().getTipoVigencia().equals("VV") &&
                !this.getProduto().getTipoProduto().equals("TR") &&
                !this.getProduto().getTipoProduto().equals("DS");
    }

    public VendaAvulsaDTO getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsaDTO vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public AulaAvulsaDiariaDTO getAulaAvulsaDiaria() {
        return aulaAvulsaDiaria;
    }

    public void setAulaAvulsaDiaria(AulaAvulsaDiariaDTO aulaAvulsaDiaria) {
        this.aulaAvulsaDiaria = aulaAvulsaDiaria;
    }
}
