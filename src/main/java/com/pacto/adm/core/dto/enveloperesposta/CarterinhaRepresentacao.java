package com.pacto.adm.core.dto.enveloperesposta;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "<PERSON><PERSON><PERSON>", description = "Representação das informações necessárias para enviar para o Servlet para impressão da carterinha")
public class CarterinhaRepresentacao {

    @Schema(example = "CARTERINHA")
    private String tipoOperacao;
    @Schema(example = "123456")
    private Integer codigoCliente;
    @Schema(example = "001")
    private Integer codigoEmpresa;
    @Schema(example = "abc123xyz")
    private String chave;

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }
}
