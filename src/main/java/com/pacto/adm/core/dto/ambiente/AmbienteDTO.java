package com.pacto.adm.core.dto.ambiente;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Ambiente", description = "Informações sobre o ambiente onde as aulas são realizadas, como código e descrição.")
public class AmbienteDTO {

    @Schema(description = "Código único identificador do ambiente.", example = "60")
    private Integer codigo;

    @Schema(description = "Descrição do ambiente, como nome ou localização.", example = "Sala 101 - Bloco A")
    private String descricao;

    public AmbienteDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
