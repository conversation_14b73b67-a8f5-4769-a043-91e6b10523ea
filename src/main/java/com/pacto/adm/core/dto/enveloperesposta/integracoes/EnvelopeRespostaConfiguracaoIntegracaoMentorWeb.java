package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoMentorWebDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoMentorWeb {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoMentorWebDTO content;

    public ConfiguracaoIntegracaoMentorWebDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoMentorWebDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"habilitada\": true, "
                    + "\"url\": \"https://api.mentorweb.com/integracao\", "
                    + "\"servico\": \"cadastroAluno\", "
                    + "\"user\": \"usuario_mentor\", "
                    + "\"password\": \"senha123\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"habilitada\": true, "
                    + "\"url\": \"https://api.mentorweb.com/integracao\", "
                    + "\"servico\": \"cadastroAluno\", "
                    + "\"user\": \"usuario_mentor\", "
                    + "\"password\": \"senha123\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";


    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
