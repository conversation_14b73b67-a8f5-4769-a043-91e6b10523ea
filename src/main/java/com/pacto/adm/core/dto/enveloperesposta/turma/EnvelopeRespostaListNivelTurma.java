package com.pacto.adm.core.dto.enveloperesposta.turma;

import com.pacto.adm.core.entities.contrato.NivelTurma;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListNivelTurma {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<NivelTurma> content;

    public List<NivelTurma> getContent() {
        return content;
    }

    public void setContent(List<NivelTurma> content) {
        this.content = content;
    }


    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaNivelTurma.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
