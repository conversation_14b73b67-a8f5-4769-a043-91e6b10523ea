package com.pacto.adm.core.dto.enveloperesposta.pagamento;

import com.pacto.adm.core.dto.MovParcelaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das resposta das requisições envolvendo as parcelas")
public class EnvelopeRespostaMovParcelaDTO  {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private MovParcelaDTO content;

    public MovParcelaDTO getContent() {
        return content;
    }

    public void setContent(MovParcelaDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"contrato\": 10, "
                    + "\"empresa\":{ "+ EnvelopeRespostaEmpresaDTO.atributos + "},"
                    + "\"descricao\": \"Parcela referente ao mês de Janeiro\", "
                    + "\"dataVencimento\": \"2023-01-31\", "
                    + "\"dataLancamento\": \"2023-01-01\", "
                    + "\"valor\": 199.99, "
                    + "\"situacao\": \"PG\", "
                    + "\"pessoa\": 5, "
                    + "\"situacaoDescricao\": \"Pago\", "
                    + "\"nrTentativas\": 2, "
                    + "\"dataPagamento\": \"2023-02-01\"";

    public static final String resposta = "{"
            + "  \"content\": {" + atributos + "}"
            + "}";
}
