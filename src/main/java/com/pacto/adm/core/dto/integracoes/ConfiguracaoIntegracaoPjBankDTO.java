package com.pacto.adm.core.dto.integracoes;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Configuração de Integração com PjBank", description = "Contém os dados necessários para integrar a empresa com o serviço financeiro PjBank.")
public class ConfiguracaoIntegracaoPjBankDTO {

    @Schema(description = "DDD do telefone registrado na integração com o PjBank.", example = "11")
    private Integer ddPjbank;

    @Schema(description = "Número do telefone registrado na integração com o PjBank.", example = "*********")
    private Integer fonePjbank;

    @Schema(description = "Código da conta vinculada à integração com o PjBank.", example = "123456")
    private Integer valueConta = 0;

    @Schema(description = "E-mail cadastrado para a integração com o PjBank.", example = "<EMAIL>")
    private String emailPjbank;

    @Schema(description = "Empresa associada à integração com o PjBank.")
    private EmpresaDTO empresa;

    public Integer getDdPjbank() {
        return ddPjbank;
    }

    public void setDdPjbank(Integer ddPjbank) {
        this.ddPjbank = ddPjbank;
    }

    public Integer getFonePjbank() {
        return fonePjbank;
    }

    public void setFonePjbank(Integer fonePjbank) {
        this.fonePjbank = fonePjbank;
    }

    public Integer getValueConta() {
        return valueConta;
    }

    public void setValueConta(Integer valueConta) {
        this.valueConta = valueConta;
    }

    public String getEmailPjbank() {
        return emailPjbank;
    }

    public void setEmailPjbank(String emailPjbank) {
        this.emailPjbank = emailPjbank;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
