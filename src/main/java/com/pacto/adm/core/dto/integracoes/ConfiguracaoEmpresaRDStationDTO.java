package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração Empresa RD Station", description = "Configurações de integração com o sistema RD Station para marketing e gestão de leads.")
public class ConfiguracaoEmpresaRDStationDTO {

    @Schema(description = "Código identificador da configuração da empresa no RD Station.", example = "12345")
    private Integer codigo;

    @Schema(description = "Código da ação de objeção relacionada à empresa no RD Station.", example = "1")
    private Integer acaoObjecao;

    @Schema(description = "Indica se a empresa utiliza o RD Station para marketing.", example = "true")
    private Boolean empresaUsaRd;

    @Schema(description = "Chave pública utilizada na integração com o RD Station.", example = "chave-publica-xyz123")
    private String chavePublica;

    @Schema(description = "Chave privada utilizada na integração com o RD Station.", example = "chave-privada-abc456")
    private String chavePrivada;

    @Schema(description = "Hora limite para a integração ou ação programada no RD Station.", example = "18:00")
    private String horaLimite;

    @Schema(description = "URL do evento Webhook para a integração com o RD Station.", example = "https://api.exemplo.com/webhook")
    private String eventWeebHook;

    @Schema(description = "Responsável padrão pela integração com o RD Station, com detalhes do usuário.")
    private UsuarioDTO responsavelPadrao;

    @Schema(description = "Detalhes da empresa associada à configuração de integração RD Station.")
    private EmpresaDTO empresa;

    @Schema(description = "Link da API V13 utilizada para integração com o RD Station.", example = "https://api.rdstation.com.br/v13")
    private String apiV13Link;

    @Schema(description = "URL base do RD Station para integração.", example = "https://www.rdstation.com.br")
    private String urlRdStation;

    @Schema(description = "URL do WebHook utilizado na integração com o RD Station.", example = "https://api.exemplo.com/webhook-rd")
    private String urlWebHookRd;

    @Schema(description = "Indica se a configuração de atualização do aluno no RD Station Marketing está habilitada.", example = "true")
    private Boolean configAtualizarAlunoRdStationMarketing;

    @Schema(description = "Token de acesso utilizado para a integração com o RD Station Marketing.", example = "access-token-xyz123")
    private String accessTokenRdStationMarketing;

    @Schema(description = "Token de atualização utilizado para a integração com o RD Station Marketing.", example = "refresh-token-abc456")
    private String refreshTokenRdStationMarketing;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(Integer acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public Boolean getEmpresaUsaRd() {
        return empresaUsaRd;
    }

    public void setEmpresaUsaRd(Boolean empresaUsaRd) {
        this.empresaUsaRd = empresaUsaRd;
    }

    public String getChavePublica() {
        return chavePublica;
    }

    public void setChavePublica(String chavePublica) {
        this.chavePublica = chavePublica;
    }

    public String getChavePrivada() {
        return chavePrivada;
    }

    public void setChavePrivada(String chavePrivada) {
        this.chavePrivada = chavePrivada;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public String getEventWeebHook() {
        return eventWeebHook;
    }

    public void setEventWeebHook(String eventWeebHook) {
        this.eventWeebHook = eventWeebHook;
    }

    public UsuarioDTO getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(UsuarioDTO responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public String getApiV13Link() {
        return apiV13Link;
    }

    public void setApiV13Link(String apiV13Link) {
        this.apiV13Link = apiV13Link;
    }

    public String getUrlRdStation() {
        return urlRdStation;
    }

    public void setUrlRdStation(String urlRdStation) {
        this.urlRdStation = urlRdStation;
    }

    public String getUrlWebHookRd() {
        return urlWebHookRd;
    }

    public void setUrlWebHookRd(String urlWebHookRd) {
        this.urlWebHookRd = urlWebHookRd;
    }

    public Boolean getConfigAtualizarAlunoRdStationMarketing() {
        return configAtualizarAlunoRdStationMarketing;
    }

    public void setConfigAtualizarAlunoRdStationMarketing(Boolean configAtualizarAlunoRdStationMarketing) {
        this.configAtualizarAlunoRdStationMarketing = configAtualizarAlunoRdStationMarketing;
    }

    public String getAccessTokenRdStationMarketing() {
        return accessTokenRdStationMarketing;
    }

    public void setAccessTokenRdStationMarketing(String accessTokenRdStationMarketing) {
        this.accessTokenRdStationMarketing = accessTokenRdStationMarketing;
    }

    public String getRefreshTokenRdStationMarketing() {
        return refreshTokenRdStationMarketing;
    }

    public void setRefreshTokenRdStationMarketing(String refreshTokenRdStationMarketing) {
        this.refreshTokenRdStationMarketing = refreshTokenRdStationMarketing;
    }
}
