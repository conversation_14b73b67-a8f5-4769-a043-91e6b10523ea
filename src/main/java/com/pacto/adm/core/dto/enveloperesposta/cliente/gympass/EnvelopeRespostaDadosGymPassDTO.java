package com.pacto.adm.core.dto.enveloperesposta.cliente.gympass;

import com.pacto.adm.core.dto.ClienteDadosGymPassDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Atestado", description = "Representação das respostas contendo as informações do atestado")
public class EnvelopeRespostaDadosGymPassDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteDadosGymPassDTO content;

    public ClienteDadosGymPassDTO getContent() {
        return content;
    }

    public void setContent(ClienteDadosGymPassDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"gymPassTypeNumber\": \"1231934\", "
                    + "\"gymPassUniqueToken\": \"Token-1239123091\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

    public final static String requestBody = "{" + atributos + "}";
}
