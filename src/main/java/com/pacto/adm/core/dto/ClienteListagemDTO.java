package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Listagem de Clientes", description = "Informaações de cada cliente presente em uma lista")
public class ClienteListagemDTO {

    @Schema(description = "Nome do cliente", example = "Renato Alves Cariani")
    private String nome;

    @Schema(description = "Código da matrícula do cliente", example = "001023")
    private String matricula;

    @Schema(description = "URL da foto do cliente", example = "www.sistemapacto.com.br/arquivos/renato-alves-cariani.png")
    private String urlFoto;

    @Schema(description = "Categoria de serviço o cliente está cadastrado", example = "Experiência Pacto")
    private String categoria;

    @Schema(description = "Data de início do contrato (Formato TimeStamp)", example = "1745971200000")
    private Long inicioContrato;

    @Schema(description = "Data de finalização do contrato (Formato TimeStamp)", example = "1756512000000")
    private Long fimContrato;

    @Schema(description = "Código identificador do cliente", example = "97")
    private Integer codigoCliente;

    @Schema(description = "Código identificador da empresa/academia", example = "1")
    private String empresa;

    @Schema(description = "Situação do cliente", example = "ATIVO")
    private String situacao;

    @Schema(description = "Situação do contrato do cliente com a academia", example = "ATIVO")
    private String situacaoContrato;

    @Schema(description = "Telefone do cliente", example = "(99)1234500000")
    private String telefone;

    @Schema(description = "Email do cliente", example = "<EMAIL>")
    private String email;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public Long getInicioContrato() {
        return inicioContrato;
    }

    public void setInicioContrato(Long inicioContrato) {
        this.inicioContrato = inicioContrato;
    }

    public Long getFimContrato() {
        return fimContrato;
    }

    public void setFimContrato(Long fimContrato) {
        this.fimContrato = fimContrato;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
