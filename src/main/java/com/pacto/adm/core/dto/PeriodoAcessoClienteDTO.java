package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
@Schema(name = "Período de Acesso Cliente", description = "Informações do período de acesso do cliente")
public class PeriodoAcessoClienteDTO {

    @Schema(description = "Código identificador do período de acesso do cliente", example = "7853")
    private Integer codigo;

    @Schema(description = "Data de acesso", example = "2025-04-28T00:00:00.000Z")
    private Date dataAcesso;

    @Schema(description = "Token de acesso", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")
    private String token;

    @Schema(description = "Indica se possui legenda de acesso", example = "false")
    private Boolean legenda;

    @Schema(description = "Valor do acesso", example = "29.00")
    private Double valor;

    @Schema(description = "Tipo do acesso", example = "E")
    private String tipoAcesso;

    @Schema(description = "Código do cliente na tabela pessoa", example = "123")
    private Integer pessoa;

    @Schema(description = "Código do contrato", example = "476")
    private Integer contrato;

    @Schema(description = "Data de início do acesso", example = "2025-04-28T15:30:32.000Z")
    private Date dataInicioAcesso;

    @Schema(description = "Data final do acesso", example = "2025-04-28T14:23:25.000Z")
    private Date dataFinalAcesso;

    @Schema(description = "Código do responsável pelo acesso", example = "1")
    private Integer responsavel;

    @Schema(description = "Data de lançamento do período de acesso", example = "2025-04-28T00:00:00.000Z")
    private Date dataLancamento;

    @Schema(description = "Indica se o acesso foi feito por Total Pass", example = "false")
    private Boolean tipototalpass;

    @Schema(description = "Token do GoGood", example = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9")
    private String tokenGogood;

    @Schema(description = "Código do produto de acesso", example = "1")
    private Integer produto;

    public String getTipoAcesso() {
        return tipoAcesso;
    }

    public void setTipoAcesso(String tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataAcesso() {
        return dataAcesso;
    }

    public void setDataAcesso(Date dataAcesso) {
        this.dataAcesso = dataAcesso;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Boolean getLegenda() {
        return legenda;
    }

    public void setLegenda(Boolean legenda) {
        this.legenda = legenda;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Date getDataInicioAcesso() {
        return dataInicioAcesso;
    }

    public void setDataInicioAcesso(Date dataInicioAcesso) {
        this.dataInicioAcesso = dataInicioAcesso;
    }

    public Date getDataFinalAcesso() {
        return dataFinalAcesso;
    }

    public void setDataFinalAcesso(Date dataFinalAcesso) {
        this.dataFinalAcesso = dataFinalAcesso;
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Boolean getTipototalpass() {
        return tipototalpass;
    }

    public void setTipototalpass(Boolean tipototalpass) {
        this.tipototalpass = tipototalpass;
    }

    public String getTokenGogood() {
        return tokenGogood;
    }

    public void setTokenGogood(String tokenGogood) {
        this.tokenGogood = tokenGogood;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }
}
