package com.pacto.adm.core.dto.enveloperesposta.cliente.observacao;

import com.pacto.adm.core.dto.ClienteObservacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Dados do Plano Cliente", description = "Representação das respostas das requisições que devolvem dados de um plano de um cliente")
public class EnvelopeRespostaClienteObservacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteObservacaoDTO content;

    public ClienteObservacaoDTO getContent() {
        return content;
    }

    public void setContent(ClienteObservacaoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 12303, "
                    + "\"observacao\": \"<strong>Cliente com dificuldades de usar aparelhos para treino de perna</strong>\", "
                    + "\"observacaoSemHTML\": \"Cliente com dificuldades de usar aparelhos para treino de perna\", "
                    + "\"dataCadastro\": \"2025-08-30T00:00:00.000Z\", "
                    + "\"cliente\": {" + EnvelopeRespostaClienteDTO.atributos + "}, "
                    + "\"usuario\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"importante\": false, "
                    + "\"tipo\": \"Saúde\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
