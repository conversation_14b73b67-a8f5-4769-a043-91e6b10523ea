package com.pacto.adm.core.dto.estornoobservacao;

import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(description = "Informações do estorno de observação")
public class EstornoObservacaoDTO {
    @Schema(description = "Código único identificador do estorno de observação", example = "1001")
    private Integer codigo;

    @Schema(description = "Justificativa informada para o estorno da observação", example = "Estorno solicitado pelo cliente devido a erro no lançamento")
    private String justificativa;

    @Schema(description = "Data e hora em que o estorno foi realizado", example = "2024-01-15T14:30:00Z")
    private Date dataEstorno;

    @Schema(description = "Informações da pessoa associada ao estorno")
    private PessoaDTO pessoa;

    @Schema(description = "Informações do contrato relacionado ao estorno")
    private ContratoDTO contrato;

    @Schema(description = "Nome do usuário responsável pelo estorno", example = "ADMINISTRADOR")
    private String usuarioResponsavel;

    @Schema(description = "Informações da empresa onde o estorno foi realizado")
    private EmpresaDTO empresa;

    @Schema(description = "Código identificador do cliente", example = "5678")
    private Integer codigoCliente;

    @Schema(description = "Matrícula do cliente na academia", example = "MAT2024001")
    private String matriculaCliente;

    @Schema(description = "Código da matrícula do cliente", example = "2024001")
    private Integer codigoMatriculaCliente;

    public EstornoObservacaoDTO(
            Integer codigo, String justificativa, Date dataEstorno, PessoaDTO pessoa, ContratoDTO contrato,
            String usuarioResponsavel, EmpresaDTO empresa, Integer codigoCliente, String matriculaCliente,
            Integer codigoMatriculaCliente
    ) {
        this.codigo = codigo;
        this.justificativa = justificativa;
        this.dataEstorno = dataEstorno;
        this.pessoa = pessoa;
        this.contrato = contrato;
        this.usuarioResponsavel = usuarioResponsavel;
        this.empresa = empresa;
        this.codigoCliente = codigoCliente;
        this.matriculaCliente = matriculaCliente;
        this.codigoMatriculaCliente = codigoMatriculaCliente;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getDataEstorno() {
        return dataEstorno;
    }

    public void setDataEstorno(Date dataEstorno) {
        this.dataEstorno = dataEstorno;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public String getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(String usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public Integer getCodigoMatriculaCliente() {
        return codigoMatriculaCliente;
    }

    public void setCodigoMatriculaCliente(Integer codigoMatriculaCliente) {
        this.codigoMatriculaCliente = codigoMatriculaCliente;
    }
}
