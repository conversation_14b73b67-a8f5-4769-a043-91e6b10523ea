package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.contrato.contratoduracao.ContratoDuracaoDTO;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaContratoDuracaoTreinoDTO extends RepresentacaoPaginadorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ContratoDuracaoDTO content;

    public ContratoDuracaoDTO getContent() {
        return content;
    }

    public void setContent(ContratoDuracaoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"tipoHorario\": \"MATUTINO\", "
                    + "\"numeroVezesSemana\": 3, "
                    + "\"quantidadeCreditoCompra\": 30, "
                    + "\"quantidadeCreditoDisponivel\": 25, "
                    + "\"valorUnitario\": 50.00, "
                    + "\"creditoTreinoNaoCumulativo\": true, "
                    + "\"quantidadeCreditoMensal\": 10, "
                    + "\"dataUltimoCreditoMensal\": \"2023-10-15\"";


    public final static String resposta = "{"
            + "\"content\": ";
}
