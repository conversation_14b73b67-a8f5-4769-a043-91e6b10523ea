package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaBitrix24DTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoEmpresaBitrix24DTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoEmpresaBitrix24DTO content;

    public ConfiguracaoEmpresaBitrix24DTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoEmpresaBitrix24DTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1001, "
                    + "\"responsavelPadrao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"empresa\": \"ACADEMIA FORTE\", "
                    + "\"url\": \"https://empresa.bitrix24.com\", "
                    + "\"acaoobjecao\": 2, "
                    + "\"acao\": \"Registrar novo lead automático\", "
                    + "\"urlWebHookBitrix\": \"https://empresa.bitrix24.com/webhook\", "
                    + "\"habilitada\": true";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
