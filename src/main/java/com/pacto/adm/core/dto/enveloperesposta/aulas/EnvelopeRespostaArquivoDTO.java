package com.pacto.adm.core.dto.enveloperesposta.aulas;

import com.pacto.adm.core.dto.ArquivoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaArquivoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ArquivoDTO content;

    public ArquivoDTO getContent() {
        return content;
    }

    public void setContent(ArquivoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 193, "
                    + "\"tipo\": \"IMAGEM\", "
                    + "\"nome\": \"atestado\", "
                    + "\"extensao\": \"png\", "
                    + "\"pessoa\": 1029, "
                    + "\"dados\": \"Dados da imagem\", "
                    + "\"observacao\": \"Atestado médico\", "
                    + "\"fotoKey\": \"fotokey_1239084\", "
                    + "\"urlFull\": \"www.pactosolucoes.com.br/arquivos/atestadomedico.png\", "
                    + "\"dataRegistro\": 1744122600000, "
                    + "\"solicitacaoCompra\": 1";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
