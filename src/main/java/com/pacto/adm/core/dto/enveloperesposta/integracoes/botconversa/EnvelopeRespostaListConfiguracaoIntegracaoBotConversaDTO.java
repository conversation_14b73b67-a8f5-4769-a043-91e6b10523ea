package com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoBotConversaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class EnvelopeRespostaListConfiguracaoIntegracaoBotConversaDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ConfiguracaoIntegracaoBotConversaDTO> content;

    public List<ConfiguracaoIntegracaoBotConversaDTO> getContent() {
        return content;
    }

    public void setContent(List<ConfiguracaoIntegracaoBotConversaDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaConfiguracaoIntegracaoBotConversaDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

    public static final String requestBody = "[{" + EnvelopeRespostaConfiguracaoIntegracaoBotConversaDTO.atributos + "}]";

}
