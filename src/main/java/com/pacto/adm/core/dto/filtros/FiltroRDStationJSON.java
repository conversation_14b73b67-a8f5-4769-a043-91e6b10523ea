package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

public class FiltroRDStationJSON {
    private String parametro;
    private Integer codigoEmpresa;
    private String chaveEmpresa;

    public FiltroRDStationJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quickSearchValue").toUpperCase();
            this.codigoEmpresa = filters.optInt("codigoEmpresa");
            this.chaveEmpresa = filters.optString("chaveEmpresa");
        }
    }

    public String getParametro() { return parametro; }

    public void setParametro(String parametro) { this.parametro = parametro; }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getChaveEmpresa() {
        return chaveEmpresa;
    }

    public void setChaveEmpresa(String chaveEmpresa) {
        this.chaveEmpresa = chaveEmpresa;
    }
}
