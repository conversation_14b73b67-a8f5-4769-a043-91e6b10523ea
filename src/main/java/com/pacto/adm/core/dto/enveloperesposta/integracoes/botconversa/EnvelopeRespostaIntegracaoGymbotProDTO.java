package com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa;

import com.pacto.adm.core.dto.integracoes.IntegracaoGymbotProDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaIntegracaoGymbotProDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private IntegracaoGymbotProDTO content;

    public IntegracaoGymbotProDTO getContent() {
        return content;
    }

    public void setContent(IntegracaoGymbotProDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"numero\": \"456789\", "
                    + "\"nome\": \"Gymbot Pro\", "
                    + "\"idEmpresa\": \"1\", "
                    + "\"idCliente\": \"123\", "
                    + "\"urlWebhookGenericoGymbot\": \"https://www.exemplo.com/webhook-gymbot\", "
                    + "\"nomeUsuario\": \"gymbot.academia\", "
                    + "\"idUsuario\": \"3\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
