package com.pacto.adm.core.dto.enveloperesposta.vacina;

import com.pacto.adm.core.dto.ComprovanteVacinaDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Resposta Informações Cliente Colaborador", description = "Representação das respostas contendo as informações do cliente visualizadas por um colaborador")
public class EnvelopeRespostaListComprovanteVacinaDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ComprovanteVacinaDTO> content;

    public List<ComprovanteVacinaDTO> getContent() {
        return content;
    }

    public void setContent(List<ComprovanteVacinaDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaComprovanteVacinaDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
