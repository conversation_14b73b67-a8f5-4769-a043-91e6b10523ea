package com.pacto.adm.core.dto;

import java.math.BigDecimal;
import java.util.Date;

public class AplicacaoDescontoDTO {

    private String descricaoProduto;
    private String nomeUsRespAlteracao;
    private String matriculaCliente;
    private String nomeCliente;
    private Integer codigoContrato;
    private Date dataLancamento;
    private BigDecimal valorDesconto;
    private String descricaoConvenio;
    private String moeda;

    public AplicacaoDescontoDTO(String descricaoProduto, String nomeUsRespAlteracao, String matriculaCliente, String nomeCliente, Integer codigoContrato, Date dataLancamento, BigDecimal valorDesconto, String descricaoConvenio, String moeda) {
        this.descricaoProduto = descricaoProduto;
        this.nomeUsRespAlteracao = nomeUsRespAlteracao;
        this.matriculaCliente = matriculaCliente;
        this.nomeCliente = nomeCliente;
        this.codigoContrato = codigoContrato;
        this.dataLancamento = dataLancamento;
        this.valorDesconto = valorDesconto;
        this.descricaoConvenio = descricaoConvenio;
        this.moeda = moeda;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public String getNomeUsRespAlteracao() {
        return nomeUsRespAlteracao;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public BigDecimal getValorDesconto() {
        return valorDesconto;
    }

    public String getDescricaoConvenio() {
        return descricaoConvenio;
    }

    public String getMoeda() {
        return moeda;
    }
}
