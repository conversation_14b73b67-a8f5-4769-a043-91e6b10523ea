package com.pacto.adm.core.dto.enveloperesposta.log;

import com.pacto.adm.core.dto.LogDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaLogDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private LogDTO content;

    public LogDTO getContent() {
        return content;
    }

    public void setContent(LogDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 135, "
                    + "\"nomeEntidade\": \"modaliade\", "
                    + "\"nomeEntidadeDescricao\": \"Modalidade\", "
                    + "\"chavePrimaria\": \"5\", "
                    + "\"chavePrimariaEntidadeSubordinada\": \"1\", "
                    + "\"nomeCampo\": \"nome\", "
                    + "\"valorCampoAnterior\": \"Academiaa\", "
                    + "\"valorCampoAlterado\": \"Academia\", "
                    + "\"dataAlteracao\": \"2025-04-28T00:00:00Z\", "
                    + "\"responsavelAlteracao\": \"PACTO\", "
                    + "\"operacao\": \"ALTERAÇÃO\", "
                    + "\"pessoa\": 1, "
                    + "\"cliente\": 1, "
                    + "\"descricao\": \"Alteração no campo nome\", "
                    + "\"chave\": \"5\", "
                    + "\"usuario\": \"PACTO\", "
                    + "\"dia\": \"28/04/2025 - 10:40\", "
                    + "\"hora\": \"10:40:06\", "
                    + "\"identificador\": \"nome\", "
                    + "\"alteracoes\": [{"
                    + EnvelopeRespostaLogAlteracoesDTO.atributos
                    + "}], "
                    + "\"origem\": \"ZW\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";




}
