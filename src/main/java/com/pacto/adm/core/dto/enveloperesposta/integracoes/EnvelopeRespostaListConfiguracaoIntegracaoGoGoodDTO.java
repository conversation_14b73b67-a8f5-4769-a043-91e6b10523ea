package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGoGoodDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class EnvelopeRespostaListConfiguracaoIntegracaoGoGoodDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ConfiguracaoIntegracaoGoGoodDTO> content;

    public List<ConfiguracaoIntegracaoGoGoodDTO> getContent() {
        return content;
    }

    public void setContent(List<ConfiguracaoIntegracaoGoGoodDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaConfiguracaoIntegracaoGoGoodDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
