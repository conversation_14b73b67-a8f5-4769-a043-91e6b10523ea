package com.pacto.adm.core.dto.enveloperesposta.statushttp;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Resposta com mensagem específica para o front-end")
public class ErrorFront {

    @Schema(description = "Identificador da mensagem para o front-end", example = "alerta_saldo_insuficiente")
    private String messageID;

    @Schema(description = "Mensagem que deve ser exibida ao usuário", example = "Seu saldo é insuficiente para completar a operação.")
    private String messageValue;

    public String getMessageID() {
        return messageID;
    }

    public void setMessageID(String messageID) {
        this.messageID = messageID;
    }

    public String getMessageValue() {
        return messageValue;
    }

    public void setMessageValue(String messageValue) {
        this.messageValue = messageValue;
    }
}
