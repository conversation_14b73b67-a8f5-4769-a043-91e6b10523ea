package com.pacto.adm.core.dto.enveloperesposta.empresa;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaEmpresaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private EmpresaDTO content;

    public EmpresaDTO getContent() {
        return content;
    }

    public void setContent(EmpresaDTO content) {
        this.content = content;
    }


    public static final String atributos =
             "\"codigo\": 1, "
            + "\"nome\": \"ACADEMIA PACTO\", "
            + "\"ativa\": true, "
            + "\"setor\": \"Academia\", "
            + "\"estado\": {" + EnvelopeRespostaEstado.atributos + "},"
            + "\"cidade\": {" + EnvelopeRespostaCidade.atributos + "},"
            + "\"trabalharComPontuacao\": false, "
            + "\"usarNfse\": false, "
            + "\"usarNfce\": false, "
            + "\"integracaoMyWellneHabilitada\": false, "
            + "\"integracaoMyWellnessEnviarVinculos\": true, "
            + "\"integracaoMyWellnessEnviarGrupos\": true, "
            + "\"integracaoMyWellnessFacilityUrl\": \"https://mywellness.example.com\", "
            + "\"integracaMyWellneApiKey\": \"api-key-12345\", "
            + "\"integracaoMyWellnessUser\": \"usuario-mywellness\", "
            + "\"integracaoMyWellnessPassword\": \"senha-mywellness\", "
            + "\"nrDiasVigenciaMyWellnessGymPass\": 30, "
            + "\"tipoVigenciaMyWellnessGympass\": 1, "
            + "\"integracaoMentorWebHabilitada\": false, "
            + "\"integracaoMentorWebUrl\": \"https://mentorweb.example.com\", "
            + "\"integracaoMentorWebServico\": \"servico-mentorweb\", "
            + "\"integracaoMentorWebUser\": \"usuario-mentorweb\", "
            + "\"integracaoMentorWebPassword\": \"senha-mentorweb\", "
            + "\"utilizaSistemaEstacionamento\": false, "
            + "\"empresaConfigEstacionamento\": {" + EnvelopeRespostaEmpresaConfigEstacionamentoDTO.atributos + "},"
            + "\"usarParceiroFidelidade\": false, "
            + "\"parceiroFidelidade\": {"+ EnvelopeRespostaParceiroFidelidadeDTO.atributos + "},"
            + "\"notificarWebhook\": false, "
            + "\"urlWebhookNotificar\": \"https://webhook.example.com/notify\", "
            + "\"integracaoAmigoFitHabilitada\": false, "
            + "\"nomeUsuarioAmigoFit\": \"usuario-amigofit\", "
            + "\"senhaUsuarioAmigoFit\": \"senha-amigofit\", "
            + "\"cpfCodigoInternoWeHelp\": false, "
            + "\"tokenBuzzLead\": \"token-buzzlead-12345\", "
            + "\"tokenSMS\": \"token-sms-67890\", "
            + "\"tokenSMSShortCode\": \"shortcode-12345\", "
            + "\"integracaoF360RelFatHabilitada\": false, "
            + "\"integracaoF360FtpServer\": \"ftp.f360.example.com\", "
            + "\"integracaoF360FtpPort\": 21, "
            + "\"integracaoF360User\": \"usuario-f360\", "
            + "\"integracaoF360Password\": \"senha-f360\", "
            + "\"integracaoF360Dir\": \"/arquivos/f360\", "
            + "\"integracaoF360Quinzenal\": false, "
            + "\"usarConciliadora\": false, "
            + "\"empresaConciliadora\": \"Conciliadora Exemplo\", "
            + "\"senhaConciliadora\": \"senha-conciliadora\", "
            + "\"codigoGymPass\": \"gym-pass-12345\", "
            + "\"tokenApiGymPass\": \"api-token-gympass\", "
            + "\"integracaoSpiviHabilitada\": false, "
            + "\"integracaoSpiviSourceName\": \"Fonte Exemplo\", "
            + "\"integracaoSpiviSiteID\": 12345, "
            + "\"integracaoSpiviPassword\": \"senha-spivi\", "
            + "\"usaIntegracoesCrm\": false, "
            + "\"carenciaRenovacao\": 15, "
            + "\"usarGestaoCreditosPersonal\": false, "
            + "\"tipoGestaoNfse\": 1, "
            + "\"cnpj\": \"12.345.678/0001-90\", "
            + "\"razaoSocial\": \"RAZÃO SOCIAL DA EMPRESA\", "
            + "\"email\": \"<EMAIL>\", "
            + "\"arredondamento\": 2, "
            + "\"habilitarCadastroEmpresaSesi\": false, "
            + "\"utilizaGestaoClientesComRestricoes\": false, "
            + "\"integracaoNuvemshopNomeApp\": \"App Exemplo\", "
            + "\"integracaoNuvemshopEmail\": \"<EMAIL>\", "
            + "\"integracaoNuvemshopTokenAcesso\": \"token-nuvemshop-12345\", "
            + "\"integracaoNuvemshopStoreId\": \"store-id-67890\", "
            + "\"integracaoNuvemshopHabilitada\": false, "
            + "\"permiteContratosConcomintante\": false, "
            + "\"pontuarApenasCampanhasAtivas\": false, "
            + "\"utilizarPactoPrint\": false, "
            + "\"bvObrigatorio\": false, "
            + "\"integracaoManyChatHabilitada\": false, "
            + "\"integracaoManyChatTokenApi\": \"\", "
            + "\"nomeCurto\": \"NOME CURTO DA EMPRESA\"";


    public final static String requestBody = "{" +atributos + "}";

    public final static String resposta = "{"
            + "\"content\": {"
            + atributos
            + "}}";
}
