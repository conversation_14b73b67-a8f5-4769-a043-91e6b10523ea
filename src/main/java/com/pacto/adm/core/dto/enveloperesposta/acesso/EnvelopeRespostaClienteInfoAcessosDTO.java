package com.pacto.adm.core.dto.enveloperesposta.acesso;

import com.pacto.adm.core.dto.ClienteInfoAcessosDTO;
import com.pacto.adm.core.dto.ColetorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaClienteInfoAcessosDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteInfoAcessosDTO content;

    public ClienteInfoAcessosDTO getContent() {
        return content;
    }

    public void setContent(ClienteInfoAcessosDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"dataUltimoAcesso\": \"2025-05-07T00:00:00\", "
                    + "\"mediaUltimasSemanas\": 4.0, "
                    + "\"mediaUltimosMeses\": 16.0";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
