package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.CidadeDTO;
import com.pacto.adm.core.dto.empresa.EstadoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Informações do Coletor de Acessos", name = "Coletor de Acessos")
public class ColetorDTO {

    @Schema(description = "Código do local de acesso do coletor", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição do coletor de acesso", example = "PACTO GYM | ENTRADA | ***********")
    private String descricao;


    public ColetorDTO() {
    }

    public ColetorDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
