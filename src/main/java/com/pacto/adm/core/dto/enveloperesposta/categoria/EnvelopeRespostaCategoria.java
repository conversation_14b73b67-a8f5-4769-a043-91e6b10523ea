package com.pacto.adm.core.dto.enveloperesposta.categoria;

import com.pacto.adm.core.entities.Categoria;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaCategoria  {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private Categoria content;

    public Categoria getContent() {
        return content;
    }

    public void setContent(Categoria content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 3, "
                    + "\"nome\": \"Experiência Pacto\", "
                    + "\"tipoCategoria\": \"AL\", "
                    + "\"nrconvitepermitido\": 100, "
                    + "\"tipocategoriaclube\": 1, "
                    + "\"tipobloqueioinadimplencia\": 1, "
                    + "\"produtopadrao\": 1, "
                    + "\"nomeexterno\": \"Experiência Pacto\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
