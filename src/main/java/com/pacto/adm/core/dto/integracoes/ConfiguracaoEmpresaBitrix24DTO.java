package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Configuração Empresa Bitrix24", description = "Configurações de integração com o sistema Bitrix24 para automação de processos e CRM.")
public class ConfiguracaoEmpresaBitrix24DTO {

    @Schema(description = "Código identificador da configuração da empresa no Bitrix24.", example = "1001")
    private Integer codigo;

    @Schema(description = "Responsável padrão pela integração com o Bitrix24, contendo informações do usuário.")
    private UsuarioDTO responsavelPadrao;

    @Schema(description = "Nome da empresa associada à configuração do Bitrix24.", example = "ACADEMIA FORTE")
    private String empresa;

    @Schema(description = "URL de integração com o Bitrix24.", example = "https://empresa.bitrix24.com")
    private String url;

    @Schema(description = "Código da ação de objeção configurada no Bitrix24.", example = "2")
    private Integer acaoobjecao;

    @Schema(description = "Descrição da ação configurada no Bitrix24.", example = "Registrar novo lead automático")
    private String acao;

    @Schema(description = "URL do WebHook utilizado para integração com o Bitrix24.", example = "https://empresa.bitrix24.com/webhook")
    private String urlWebHookBitrix;

    @Schema(description = "Indica se a integração com o Bitrix24 está habilitada.", example = "true")
    private boolean habilitada;

    // Outros campos da tabela...

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getAcaoobjecao() {
        return acaoobjecao;
    }

    public void setAcaoobjecao(Integer acaoobjecao) {
        this.acaoobjecao = acaoobjecao;
    }

    public String getAcao() {
        return acao;
    }

    public void setAcao(String acao) {
        this.acao = acao;
    }

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public UsuarioDTO getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(UsuarioDTO responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }


    public String getUrlWebHookBitrix() {
        return urlWebHookBitrix;
    }

    public void setUrlWebHookBitrix(String urlWebHookBitrix) {
        this.urlWebHookBitrix = urlWebHookBitrix;
    }

    // Adicione getter e setter para outros campos da tabela...
}
