package com.pacto.adm.core.dto.movpagamento;

import com.pacto.adm.core.dto.MovPagamentoDTO;

import java.math.BigDecimal;
import java.util.List;

public class MovPagamentoTotaisDTO {

    public BigDecimal valorTotalPagamentos;
    public List<MovPagamentoDTO> movPagamentos;

    public BigDecimal getValorTotalPagamentos() {
        if (valorTotalPagamentos == null) {
            this.valorTotalPagamentos = BigDecimal.ZERO;
        }
        return valorTotalPagamentos;
    }

    public void setValorTotalPagamentos(BigDecimal valorTotalPagamentos) {
        this.valorTotalPagamentos = valorTotalPagamentos;
    }

    public List<MovPagamentoDTO> getMovPagamentos() {
        return movPagamentos;
    }

    public void setMovPagamentos(List<MovPagamentoDTO> movPagamentos) {
        this.movPagamentos = movPagamentos;
    }
}
