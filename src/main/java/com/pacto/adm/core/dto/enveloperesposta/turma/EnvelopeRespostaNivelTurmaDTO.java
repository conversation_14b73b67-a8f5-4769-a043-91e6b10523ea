package com.pacto.adm.core.dto.enveloperesposta.turma;

import com.pacto.adm.core.dto.nivelturma.NivelTurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaNivelTurmaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private NivelTurmaDTO content;

    public NivelTurmaDTO getContent() {
        return content;
    }

    public void setContent(NivelTurmaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 70, "
                    + "\"descricao\": \"Iniciante\", "
                    + "\"codigoNivelMgb\": \"NIV-001\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
