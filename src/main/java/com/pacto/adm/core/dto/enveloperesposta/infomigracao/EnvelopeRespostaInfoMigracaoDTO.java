package com.pacto.adm.core.dto.enveloperesposta.infomigracao;

import com.pacto.adm.core.dto.InfoMigracaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaInfoMigracaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private InfoMigracaoDTO content;

    public InfoMigracaoDTO getContent() {
        return content;
    }

    public void setContent(InfoMigracaoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"tipoInfo\": 1, "
                    + "\"usuario\": 2, "
                    + "\"info\": \"Negociação de planos\", "
                    + "\"origem\": \"1\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
