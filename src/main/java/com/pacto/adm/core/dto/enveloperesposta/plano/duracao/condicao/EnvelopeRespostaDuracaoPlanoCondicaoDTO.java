package com.pacto.adm.core.dto.enveloperesposta.plano.duracao.condicao;

import com.pacto.adm.core.dto.negociacao.PlanoDuracaoCondicaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;


public class EnvelopeRespostaDuracaoPlanoCondicaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PlanoDuracaoCondicaoDTO content;

    public PlanoDuracaoCondicaoDTO getContent() {
        return content;
    }

    public void setContent(PlanoDuracaoCondicaoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"nrParcelas\": 6, "
                    + "\"descricao\": \"Parcelado em 6 vezes\", "
                    + "\"tipoConvenio\": 3, "
                    + "\"geradoAutomaticoPlanoRecorrente\": false";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
