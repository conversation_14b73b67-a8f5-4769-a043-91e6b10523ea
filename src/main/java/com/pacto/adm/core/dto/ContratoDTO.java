package com.pacto.adm.core.dto;

import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.contrato.ContratoRecorrenciaDTO;
import com.pacto.adm.core.dto.contrato.contratoduracao.ContratoDuracaoDTO;
import com.pacto.adm.core.dto.conveniodesconto.ConvenioDescontoDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.grupodesconto.GrupoDescontoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Transient;
import java.util.Date;
@Schema(name = "Contrato", description = "Informações do contrato")
public class ContratoDTO {

    @Schema(description = "Código único identificador do contrato.", example = "3")
    private Integer codigo;

    @Schema(description = "Tipo do contrato", example = "MENSAL")
    private String tipo;

    @Schema(description = "Data de início da vigência do contrato.", example = "2019-04-18T00:00:00Z")
    private Date vigenciaDe;

    @Schema(description = "Data de término da vigência do contrato.", example = "2020-04-18T00:00:00Z")
    private Date vigenciaAte;

    @Schema(description = "Nova data de término da vigência ajustada, caso haja alterações.", example = "2020-05-18T00:00:00Z")
    private Date vigenciaAteAjustada;

    @Schema(description = "Situação atual do contrato", example = "ATIVO")
    private String situacao;

    private String situacaoContrato;

    @Schema(description = "Código da pessoa associada ao contrato.", example = "12345")
    private Integer pessoa;

    @Schema(description = "Detalhes da pessoa associada ao contrato, incluindo nome, CPF e outros dados.")
    private PessoaDTO pessoaDTO;

    @Schema(description = "Detalhes da pessoa original do contrato, caso tenha sido transferido.")
    private PessoaDTO pessoaOriginal;

    @Schema(description = "Indica se o contrato é uma venda de crédito para treino.", example = "false")
    private boolean vendaCreditoTreino;

    @Schema(description = "Indica se o contrato permite renovação automática.", example = "true")
    private boolean permiteRenovacaoAutomatica;

    @Schema(description = "Indica se o contrato está no regime de recorrência.", example = "true")
    private Boolean regimeRecorrencia;

    @Schema(description = "Código do contrato baseado em uma renovação anterior.", example = "2")
    private Integer contratoBaseadoRenovacao;

    @Schema(description = "Valor base para cálculos do contrato.", example = "100.00")
    private Double valorBaseCalculo;

    @Schema(description = "Valor total do contrato.", example = "200.00")
    private Double valor;

    @Schema(description = "Valor pago no contrato até o momento.", example = "150.00")
    private Double valorPago;

    @Schema(description = "Descrição do plano associado ao contrato.", example = "PLANO BÁSICO")
    private String descricaoPlano;

    @Schema(description = "Nome do responsável pelo lançamento do contrato.", example = "João Silva")
    private String responsavelLancamento;

    @Schema(description = "Código do do responsável pelo lançamento do contrato.", example = "1")
    private Integer codUsuarioResponsavelLancamento;

    @Schema(description = "Data do lançamento do contrato.", example = "2019-04-18T00:00:00Z")
    private Date dataLancamento;

    @Schema(description = "Data da última alteração manual no contrato.", example = "2019-05-20T00:00:00Z")
    private Date dataAlteracaoManual;

    @Schema(description = "Data da realização da renovação do contrato.", example = "2020-04-18T00:00:00Z")
    private Date dataRenovarRealizada;

    @Schema(description = "Nome da empresa associada ao contrato.", example = "ACADEMIA FORTE")
    private String nomeEmpresa;

    @Schema(description = "Detalhes da empresa associada ao contrato, incluindo código e status.")
    private EmpresaDTO empresa;

    @Schema(description = "Condição de pagamento do contrato", example = "PARCELADO")
    private String condicaoDePagamento;

    @Schema(description = "Duração do contrato", example = "12 MESES")
    private String duracao;

    @Schema(description = "Tipo do contrato.", example = "ANUAL")
    private String tipoContrato;

    @Schema(description = "Nome do consultor responsável pelo contrato.", example = "Maria Souza")
    private String nomeConsultorReponsavel;

    @Schema(description = "Descrição do horário relacionado ao contrato.", example = "SEG-SEX 06:00 - 22:00")
    private String descricaoHorario;

    @Schema(description = "Indica se o contrato deve ser renovado automaticamente.", example = "true")
    private Boolean renovarContrato;

    @Schema(description = "Indica se o contrato deve ser rematriculado.", example = "false")
    private Boolean rematricularContrato;

    @Schema(description = "Indica se não é permitida a renovação/rematrícula de contratos anteriores.", example = "false")
    private Boolean naoPermitirRenovacaoRematriculaDeContratoAnteriores;

    @Schema(description = "Código do contrato responsável pela renovação da matrícula.", example = "1")
    private Integer contratoResponsavelRenovacaoMatricula;

    @Schema(description = "Código do contrato responsável pela rematrícula.", example = "2")
    private Integer contratoResponsavelRematriculaMatricula;

    @Schema(description = "Observações adicionais sobre o contrato.", example = "Contrato inclui desconto de 10%.")
    private String observacao;

    @Schema(description = "Detalhes da recorrência do contrato, caso seja recorrente.")
    private ContratoRecorrenciaDTO contratoRecorrencia;

    @Schema(description = "Detalhes do plano associado ao contrato, incluindo benefícios e restrições.")
    private PlanoDTO plano;

    @Schema(description = "Origem do contrato, indicando o sistema ou processo de criação.", example = "1")
    private Integer origemContrato;

    @Schema(description = "Origem do sistema onde o contrato foi gerado.", example = "2")
    private Integer origemSistema;

    @Schema(description = "Detalhes do consultor responsável pelo contrato, incluindo nome e código.")
    private ColaboradorDTO consultorResponsavel;

    @Schema(description = "Detalhes do convênio de desconto associado ao contrato.")
    private ConvenioDescontoDTO convenioDesconto;

    @Schema(description = "Detalhes do grupo de desconto associado ao contrato.")
    private GrupoDescontoDTO grupoDesconto;

    @Schema(description = "Detalhes da duração do contrato, incluindo prazos e condições.")
    private ContratoDuracaoDTO contratoDuracao;

    @Schema(description = "Código do contrato baseado em uma rematrícula anterior.", example = "3")
    private Integer contratoBaseadoRematricula;

    @Schema(description = "Indica se é permitido criar um novo contrato de outra empresa.", example = "false")
    private Boolean permiteNovoContratoDeOutraEmpresa;
    private ClienteDTO cliente;
    private UsuarioDTO responsavelDataBase;
    private String nomeModalidades;
    private String emailsCliente;
    private String telefonesCliente;
    private Boolean contratoRenovavel;
    private Boolean temAutorizacaoCobranca;
    private String situacaoContratoSW;

    public ContratoDTO() {
    }

    public ContratoDTO(Integer codigo) {
        this.codigo = codigo;
    }

    public ContratoDTO(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public ContratoDTO(Integer codigo, String situacaoContrato) {
        this.codigo = codigo;
        this.situacaoContrato = situacaoContrato;
    }

    public String getCondicaoDePagamento() {
        return condicaoDePagamento;
    }

    public void setCondicaoDePagamento(String condicaoDePagamento) {
        this.condicaoDePagamento = condicaoDePagamento;
    }

    public String getDescricaoHorario() {
        return descricaoHorario;
    }

    public void setDescricaoHorario(String descricaoHorario) {
        this.descricaoHorario = descricaoHorario;
    }

    public String getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(String responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Integer getCodUsuarioResponsavelLancamento() {
        return codUsuarioResponsavelLancamento;
    }

    public void setCodUsuarioResponsavelLancamento(Integer codUsuarioResponsavelLancamento) {
        this.codUsuarioResponsavelLancamento = codUsuarioResponsavelLancamento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getDuracao() {
        return duracao;
    }

    public void setDuracao(String duracao) {
        this.duracao = duracao;
    }

    public String getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(String tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public String getNomeConsultorReponsavel() {
        return nomeConsultorReponsavel;
    }

    public void setNomeConsultorReponsavel(String nomeConsultorReponsavel) {
        this.nomeConsultorReponsavel = nomeConsultorReponsavel;
    }

    public String getDescricaoPlano() {
        return descricaoPlano;
    }

    public void setDescricaoPlano(String descricaoPlano) {
        this.descricaoPlano = descricaoPlano;
    }

    public Double getValorPago() {
        return valorPago;
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = valorPago;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Date getVigenciaDe() {
        return vigenciaDe;
    }

    public void setVigenciaDe(Date vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public Date getVigenciaAteAjustada() {
        return vigenciaAteAjustada;
    }

    public void setVigenciaAteAjustada(Date vigenciaAteAjustada) {
        this.vigenciaAteAjustada = vigenciaAteAjustada;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public boolean isVendaCreditoTreino() {
        return vendaCreditoTreino;
    }

    public void setVendaCreditoTreino(boolean vendaCreditoTreino) {
        this.vendaCreditoTreino = vendaCreditoTreino;
    }

    public boolean isPermiteRenovacaoAutomatica() {
        return permiteRenovacaoAutomatica;
    }

    public void setPermiteRenovacaoAutomatica(boolean permiteRenovacaoAutomatica) {
        this.permiteRenovacaoAutomatica = permiteRenovacaoAutomatica;
    }

    public Double getValorBaseCalculo() {
        return valorBaseCalculo;
    }

    public void setValorBaseCalculo(Double valorBaseCalculo) {
        this.valorBaseCalculo = valorBaseCalculo;
    }

    public Boolean getRenovarContrato() {
        return renovarContrato;
    }

    public void setRenovarContrato(Boolean renovarContrato) {
        this.renovarContrato = renovarContrato;
    }

    public Boolean getRematricularContrato() {
        return rematricularContrato;
    }

    public void setRematricularContrato(Boolean rematricularContrato) {
        this.rematricularContrato = rematricularContrato;
    }

    public Boolean getNaoPermitirRenovacaoRematriculaDeContratoAnteriores() {
        if (naoPermitirRenovacaoRematriculaDeContratoAnteriores == null){
            naoPermitirRenovacaoRematriculaDeContratoAnteriores = false;
        }
        return naoPermitirRenovacaoRematriculaDeContratoAnteriores;
    }

    public void setNaoPermitirRenovacaoRematriculaDeContratoAnteriores(Boolean naoPermitirRenovacaoRematriculaDeContratoAnteriores) {
        this.naoPermitirRenovacaoRematriculaDeContratoAnteriores = naoPermitirRenovacaoRematriculaDeContratoAnteriores;
    }

    public Integer getContratoResponsavelRenovacaoMatricula() {
        return contratoResponsavelRenovacaoMatricula;
    }

    public void setContratoResponsavelRenovacaoMatricula(Integer contratoResponsavelRenovacaoMatricula) {
        this.contratoResponsavelRenovacaoMatricula = contratoResponsavelRenovacaoMatricula;
    }

    public Integer getContratoResponsavelRematriculaMatricula() {
        return contratoResponsavelRematriculaMatricula;
    }

    public void setContratoResponsavelRematriculaMatricula(Integer contratoResponsavelRematriculaMatricula) {
        this.contratoResponsavelRematriculaMatricula = contratoResponsavelRematriculaMatricula;
    }

    public Date getDataRenovarRealizada() {
        return dataRenovarRealizada;
    }

    public void setDataRenovarRealizada(Date dataRenovarRealizada) {
        this.dataRenovarRealizada = dataRenovarRealizada;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public PessoaDTO getPessoaDTO() {
        return pessoaDTO;
    }

    public void setPessoaDTO(PessoaDTO pessoaDTO) {
        this.pessoaDTO = pessoaDTO;
    }

    public PessoaDTO getPessoaOriginal() {
        return pessoaOriginal;
    }

    public void setPessoaOriginal(PessoaDTO pessoaOriginal) {
        this.pessoaOriginal = pessoaOriginal;
    }

    public ContratoRecorrenciaDTO getContratoRecorrencia() {
        return contratoRecorrencia;
    }

    public void setContratoRecorrencia(ContratoRecorrenciaDTO contratoRecorrencia) {
        this.contratoRecorrencia = contratoRecorrencia;
    }

    public PlanoDTO getPlano() {
        return plano;
    }

    public void setPlano(PlanoDTO plano) {
        this.plano = plano;
    }

    public Integer getOrigemContrato() {
        return origemContrato;
    }

    public void setOrigemContrato(Integer origemContrato) {
        this.origemContrato = origemContrato;
    }

    public ColaboradorDTO getConsultorResponsavel() {
        return consultorResponsavel;
    }

    public void setConsultorResponsavel(ColaboradorDTO consultorResponsavel) {
        this.consultorResponsavel = consultorResponsavel;
    }

    public ConvenioDescontoDTO getConvenioDesconto() {
        return convenioDesconto;
    }

    public void setConvenioDesconto(ConvenioDescontoDTO convenioDesconto) {
        this.convenioDesconto = convenioDesconto;
    }

    public GrupoDescontoDTO getGrupoDesconto() {
        return grupoDesconto;
    }

    public void setGrupoDesconto(GrupoDescontoDTO grupoDesconto) {
        this.grupoDesconto = grupoDesconto;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Date getDataAlteracaoManual() {
        return dataAlteracaoManual;
    }

    public void setDataAlteracaoManual(Date dataAlteracaoManual) {
        this.dataAlteracaoManual = dataAlteracaoManual;
    }

    public void setContratoDuracao(ContratoDuracaoDTO contratoDuracao) {
        this.contratoDuracao = contratoDuracao;
    }

    public ContratoDuracaoDTO getContratoDuracao() {
        return contratoDuracao;
    }

    public Integer getContratoBaseadoRematricula() {
        return contratoBaseadoRematricula;
    }

    public void setContratoBaseadoRematricula(Integer contratoBaseadoRematricula) {
        this.contratoBaseadoRematricula = contratoBaseadoRematricula;
    }

    public Integer getContratoBaseadoRenovacao() {
        return contratoBaseadoRenovacao;
    }

    public void setContratoBaseadoRenovacao(Integer contratoBaseadoRenovacao) {
        this.contratoBaseadoRenovacao = contratoBaseadoRenovacao;
    }

    public Boolean getPermiteNovoContratoDeOutraEmpresa() {
        return permiteNovoContratoDeOutraEmpresa;
    }

    public void setPermiteNovoContratoDeOutraEmpresa(Boolean permiteNovoContratoDeOutraEmpresa) {
        this.permiteNovoContratoDeOutraEmpresa = permiteNovoContratoDeOutraEmpresa;
    }

    public Date getVigenciaAte() {
        return vigenciaAte;
    }

    public void setVigenciaAte(Date vigenciaAte) {
        this.vigenciaAte = vigenciaAte;
    }

    public Boolean getRegimeRecorrencia() {
        return regimeRecorrencia;
    }

    public void setRegimeRecorrencia(Boolean regimeRecorrencia) {
        this.regimeRecorrencia = regimeRecorrencia;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public UsuarioDTO getResponsavelDataBase() {
        return responsavelDataBase;
    }

    public void setResponsavelDataBase(UsuarioDTO responsavelDataBase) {
        this.responsavelDataBase = responsavelDataBase;
    }

    public String getEmailsCliente() {
        return emailsCliente;
    }

    public void setEmailsCliente(String emailsCliente) {
        this.emailsCliente = emailsCliente;
    }

    public String getTelefonesCliente() {
        return telefonesCliente;
    }

    public void setTelefonesCliente(String telefonesCliente) {
        this.telefonesCliente = telefonesCliente;
    }

    public Boolean getContratoRenovavel() {
        return contratoRenovavel;
    }

    public void setContratoRenovavel(Boolean contratoRenovavel) {
        this.contratoRenovavel = contratoRenovavel;
    }

    public Boolean getTemAutorizacaoCobranca() {
        return temAutorizacaoCobranca;
    }

    public void setTemAutorizacaoCobranca(Boolean temAutorizacaoCobranca) {
        this.temAutorizacaoCobranca = temAutorizacaoCobranca;
    }

    public String getSituacaoContratoSW() {
        return situacaoContratoSW;
    }

    public void setSituacaoContratoSW(String situacaoContratoSW) {
        this.situacaoContratoSW = situacaoContratoSW;
    }

    public String getNomeModalidades() {
        return nomeModalidades;
    }

    public void setNomeModalidades(String nomeModalidades) {
        this.nomeModalidades = nomeModalidades;
    }
}
