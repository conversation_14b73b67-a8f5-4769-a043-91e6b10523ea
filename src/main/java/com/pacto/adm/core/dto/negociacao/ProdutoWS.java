/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.pacto.adm.core.dto.negociacao;


import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Produto", description = "Informações sobre um produto associado à negociação.")
public class ProdutoWS {

    @Schema(description = "Código identificador do produto.", example = "101")
    private Integer codigo;

    @Schema(description = "Descrição do produto.", example = "Kit Treinamento Funcional")
    private String descricao;

    @Schema(description = "Valor do produto.", example = "149.90")
    private Double valor;

    @Schema(description = "Número de dias de vigência do produto.", example = "30")
    private Integer nrDiasVigencia;

    @Schema(description = "Tipo do produto.", example = "Equipamento")
    private String tipoProduto;

    @Schema(description = "Data prevista para cobrança do produto (em formato texto).", example = "2025-06-01")
    private String dataCobranca = "";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getNrDiasVigencia() {
        return nrDiasVigencia;
    }

    public void setNrDiasVigencia(Integer nrDiasVigencia) {
        this.nrDiasVigencia = nrDiasVigencia;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public String getDataCobranca() {
        return dataCobranca;
    }

    public void setDataCobranca(String dataCobranca) {
        this.dataCobranca = dataCobranca;
    }
}
