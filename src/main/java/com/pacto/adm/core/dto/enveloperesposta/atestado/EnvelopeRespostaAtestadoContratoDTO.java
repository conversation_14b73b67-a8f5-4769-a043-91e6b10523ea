package com.pacto.adm.core.dto.enveloperesposta.atestado;

import com.pacto.adm.core.dto.AtestadoContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoOperacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaJustificativaOperacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Atestado", description = "Representação das respostas contendo as informações do atestado")
public class EnvelopeRespostaAtestadoContratoDTO {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações encontradas")
    private AtestadoContratoDTO content;

    public AtestadoContratoDTO getContent() {
        return content;
    }

    public void setContent(AtestadoContratoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"contrato\": {" + EnvelopeRespostaContratoDTO.atributos + "}, "
                    + "\"empresa\": 1234, "
                    + "\"dataInicio\": \"2025-04-08T14:30:00Z\", "
                    + "\"dataTermino\": \"2025-04-08T14:30:00Z\", "
                    + "\"dataInicioRetorno\": \"2025-04-08T14:30:00Z\", "
                    + "\"dataTerminoRetorno\": \"2025-04-08T14:30:00Z\", "
                    + "\"dataRegistro\": \"2025-04-08T14:30:00Z\", "
                    + "\"justificativaOperacao\": {" + EnvelopeRespostaJustificativaOperacaoDTO.atributos + "}, "
                    + "\"nrDias\": 2, "
                    + "\"nrDiasAtestado\": 2, "
                    + "\"nrDiasASomar\": 0, "
                    + "\"observacao\": \"ATESTADO VINCULADO AO CONTRATO\", "
                    + "\"responsavelOperacao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"apresentarPeriodoAtestado\": true, "
                    + "\"qtdDiasAtestadoMaiorQueContrato\": true, "
                    + "\"contratoVencido\": false, "
                    + "\"contratoOperacao\": {" + EnvelopeRespostaContratoOperacaoDTO.atributos + "}, "
                    + "\"chaveArquivo\": \"1234\", "
                    + "\"nomeArquivo\": \"contrato\", "
                    + "\"formatoArquivo\": \"PDF\", "
                    + "\"dadosArquivo\": \"Contrato\"";

    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
