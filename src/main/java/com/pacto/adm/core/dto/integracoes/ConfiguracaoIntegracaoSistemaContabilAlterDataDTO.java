package com.pacto.adm.core.dto.integracoes;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Configuração de Integração com Sistema Contábil AlterData", description = "Configurações para exportação de dados para o sistema contábil AlterData.")
public class ConfiguracaoIntegracaoSistemaContabilAlterDataDTO {

    @Schema(description = "Indica se a exportação de dados para o sistema AlterData está habilitada.", example = "true")
    private boolean habilitarExportacaoAlterData;

    public ConfiguracaoIntegracaoSistemaContabilAlterDataDTO() {
        this.habilitarExportacaoAlterData = false;
    }

    public boolean isHabilitarExportacaoAlterData() {
        return habilitarExportacaoAlterData;
    }

    public void setHabilitarExportacaoAlterData(boolean habilitarExportacaoAlterData) {
        this.habilitarExportacaoAlterData = habilitarExportacaoAlterData;
    }
}
