package com.pacto.adm.core.dto.enveloperesposta.acesso.perfilacesso;

import com.pacto.adm.core.dto.PerfilAcessoDTO;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Respostas Lista de Perfis de Acesso", description = "Representação das respostas contendo uma lista de perfis de acesso ao sistema")
public class EnvelopeRespostaListPerfilAcessoDTO extends RepresentacaoPaginadorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<PerfilAcessoDTO> content;

    public  List<PerfilAcessoDTO>  getContent() {
        return content;
    }

    public void setContent( List<PerfilAcessoDTO>  content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaPerfilAcessoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
