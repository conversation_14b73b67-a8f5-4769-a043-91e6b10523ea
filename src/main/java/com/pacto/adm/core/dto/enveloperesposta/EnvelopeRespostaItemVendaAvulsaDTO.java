package com.pacto.adm.core.dto.enveloperesposta;

import com.pacto.adm.core.dto.enveloperesposta.pacotepersonal.EnvelopeRespostaPacotePersonalDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaItemVendaAvulsaDTO {
    public static final String atributos =
            "\"codigoProduto\": 398, "
                    + "\"qtd\": 2, "
                    + "\"pontos\": 10, "
                    + "\"descricaoProduto\": \"Creatina MonoHidratada - 250 Gramas - Marca: Pacto\", "
                    + "\"precoProduto\": 59.90, "
                    + "\"descontoManual\": 9.90, "
                    + "\"descontoPadrao\": 10, "
                    + "\"valorParcial\": 10.00, "
                    + "\"pacoteEscolhido\": {" + EnvelopeRespostaPacotePersonalDTO.atributos + "}";
}
