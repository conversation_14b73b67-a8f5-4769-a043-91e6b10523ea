package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.ContratoDependenteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaContratoDependenteDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ContratoDependenteDTO content;

    public ContratoDependenteDTO getContent() {
        return content;
    }

    public void setContent(ContratoDependenteDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 123, "
                    + "\"dataInicio\": \"2025-05-07T00:00:00\", "
                    + "\"dataFinal\": \"2025-05-07T00:00:00\", "
                    + "\"dataFinalAjustada\": \"2025-05-07T00:00:00\", "
                    + "\"posicaoDependente\": 1, "
                    + "\"titular\": 12345, "
                    + "\"cliente\": {" + EnvelopeRespostaClienteDTO.atributos + "}, "
                    + "\"contrato\": {" + EnvelopeRespostaContratoDTO.atributos + "}";


    public final static String resposta = "{ \"content\": {"+ atributos + "}}";
}
