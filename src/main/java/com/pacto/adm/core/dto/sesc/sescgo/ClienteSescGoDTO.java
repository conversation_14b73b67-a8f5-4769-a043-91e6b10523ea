package com.pacto.adm.core.dto.sesc.sescgo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ClienteSescGoDTO {

    @JsonProperty("sqmatric")
    private Integer sqMatric;

    @JsonProperty("cduop")
    private Integer cdUop;

    @JsonProperty("cduoo")
    private Integer cdUoo;

    @JsonProperty("cdclassif")
    private String cdClassif;

    @JsonProperty("nudv")
    private Integer nuDv;

    @JsonProperty("nucgccei")
    private String nuCgcCei;

    @JsonProperty("cdcategori")
    private Integer cdCategori;

    @JsonProperty("cdnivel")
    private Integer cdNivel;

    @JsonProperty("sqtitulmat")
    private Integer sqTitulMat;

    @JsonProperty("cduotitul")
    private Integer cdUoTitul;

    @JsonProperty("stmatric")
    private Integer stMatric;

    @JsonProperty("dtinscri")
    private LocalDateTime dtInscri;

    @JsonProperty("dtadmissao")
    private LocalDateTime dtAdmissao;

    @JsonProperty("cdmatriant")
    private Integer cdMatriAnt;

    @JsonProperty("dtvencto")
    private LocalDateTime dtVencto;

    @JsonProperty("nmcliente")
    private String nmCliente;

    @JsonProperty("nmsocial")
    private String nmSocial;

    @JsonProperty("dtnascimen")
    private LocalDateTime dtNascimen;

    @JsonProperty("nmpai")
    private String nmPai;

    @JsonProperty("cdsexo")
    private String cdSexo;

    @JsonProperty("nmmae")
    private String nmMae;

    @JsonProperty("cdestcivil")
    private Integer cdEstCivil;

    @JsonProperty("vbestudant")
    private Integer vbEstudant;

    @JsonProperty("nuultserie")
    private Integer nuUltSerie;

    @JsonProperty("dsnatural")
    private String dsNatural;

    @JsonProperty("dsnacional")
    private String dsNacional;

    @JsonProperty("nudepend")
    private Integer nuDepend;

    @JsonProperty("nuctps")
    private String nuCtps;

    @JsonProperty("nureggeral")
    private String nuRegGeral;

    @JsonProperty("vlrenda")
    private BigDecimal vlRenda;

    @JsonProperty("nucpf")
    private String nuCpf;

    @JsonProperty("nupispasep")
    private String nuPisPasep;

    @JsonProperty("dscargo")
    private String dsCargo;

    @JsonProperty("dtemirg")
    private LocalDateTime dtEmissaoRg;

    @JsonProperty("idorgemirg")
    private String idOrgEmissorRg;

    @JsonProperty("dsparentsc")
    private String dsParentesco;

    @JsonProperty("dtatu")
    private LocalDateTime dtAtualizacao;

    @JsonProperty("dddCelular")
    private String dddCelular;

    @JsonProperty("celular")
    private String celular;

    @JsonProperty("dddTelefone")
    private String dddTelefone;

    @JsonProperty("telefone")
    private String telefone;

    @JsonProperty("email")
    private String email;

    @JsonProperty("nomeresponsavel")
    private String nomeResponsavel;

    @JsonProperty("cpfresponsavel")
    private String cpfResponsavel;

    @JsonProperty("vlrendafam")
    private BigDecimal vlRendaFamiliar;

    @JsonProperty("foto")
    private String foto;

    @JsonProperty("dsclassif")
    private String dsClassif;

    @JsonProperty("formataVencimento")
    private Integer formataVencimento;

    @JsonProperty("formataMatriculaSemMascara")
    private String formataMatriculaSemMascara;

    @JsonProperty("formataMatriculaComMascara")
    private String formataMatriculaComMascara;

    @JsonProperty("formataCategoria")
    private String formataCategoria;

    @JsonProperty("validade")
    private String validade;
}
