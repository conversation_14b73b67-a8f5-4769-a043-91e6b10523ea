package com.pacto.adm.core.dto.enveloperesposta.fornecedor;

import com.pacto.adm.core.dto.FornecedorDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaFornecedorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private FornecedorDTO content;

    public FornecedorDTO getContent() {
        return content;
    }

    public void setContent(FornecedorDTO content) {
        this.content = content;
    }


    public static final String atributos =
             "\"codigo\": 33, "
            + "\"nome\": \"Fornecedor Pacto\"";

    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{"
            + "\"content\": {"
            + atributos
            + "}}";
}
