package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(description = "Informações de biometria")
public class BiometriaDTO {

    @Schema(description = "Indica se a biometria digital da pessoa está cadastrada", example = "true")
    private Boolean digital;

    @Schema(description = "Indica se a biometria facial da pessoa está cadastrada", example = "false")
    private Boolean facial;

    public BiometriaDTO() {
    }

    public Boolean getDigital() {
        return digital;
    }

    public void setDigital(Boolean digital) {
        this.digital = digital;
    }

    public Boolean getFacial() {
        return facial;
    }

    public void setFacial(Boolean facial) {
        this.facial = facial;
    }
}
