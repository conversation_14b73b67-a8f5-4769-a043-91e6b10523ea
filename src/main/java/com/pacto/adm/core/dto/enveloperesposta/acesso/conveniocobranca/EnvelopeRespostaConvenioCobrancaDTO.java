package com.pacto.adm.core.dto.enveloperesposta.acesso.conveniocobranca;

import com.pacto.adm.core.dto.conveniocobranca.ConvenioCobrancaDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaClienteLocalDeAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaLiberacaoAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaLocalDeAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.coletor.EnvelopeRespostaColetorDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaConvenioCobrancaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConvenioCobrancaDTO content;

    public ConvenioCobrancaDTO getContent() {
        return content;
    }

    public void setContent(ConvenioCobrancaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 3, "
                    + "\"empresas\": {" + EnvelopeRespostaConvenioCobrancaEmpresaDTO.atributos + "}, "
                    + "\"descricao\": \"DCC VINDI\", ";

    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
