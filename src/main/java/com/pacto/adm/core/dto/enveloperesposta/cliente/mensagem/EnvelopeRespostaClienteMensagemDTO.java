package com.pacto.adm.core.dto.enveloperesposta.cliente.mensagem;

import com.pacto.adm.core.dto.ClienteMensagemDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Dados do Plano Cliente", description = "Representação das respostas das requisições que devolvem dados de um plano de um cliente")
public class EnvelopeRespostaClienteMensagemDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteMensagemDTO content;

    public ClienteMensagemDTO getContent() {
        return content;
    }

    public void setContent(ClienteMensagemDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 165535, "
                    + "\"tipoMensagem\": \"AA\", "
                    + "\"mensagem\": \"<strong>Academia fechada para o feriado de 1 de maio</strong>\", "
                    + "\"mensagemSemHTML\": \"Academia fechada para o feriado 1 de maio\", "
                    + "\"cliente\": {" + EnvelopeRespostaClienteDTO.atributos + "}, "
                    + "\"usuario\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"bloqueio\": false, "
                    + "\"desabilitado\": false, "
                    + "\"dataRegistro\": \"2025-08-30T00:00:00.000Z\", "
                    + "\"dataAtualizacao\": \"2025-08-30T00:00:00.000Z\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
