package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(
        name = "Cliente Informações de Acesso",
        description = "Informações e estatísticas de acesso à academia pelo cliente"
)
public class ClienteInfoAcessosDTO {

    @Schema(description = "Data do último acesso à academia", example = "2025-05-07T00:00:00")
    private Date dataUltimoAcesso;
    @Schema(description = "Média de acessos das últimas quatro semanas", example = "4")
    private Double mediaUltimasSemanas;
    @Schema(description = "Média de acessos dos últimos quatro meses", example = "16")
    private Double mediaUltimosMeses;

}
