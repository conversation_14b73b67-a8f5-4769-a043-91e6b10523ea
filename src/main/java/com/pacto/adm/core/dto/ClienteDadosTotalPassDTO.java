package com.pacto.adm.core.dto;


import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ClienteDadosTotalPass", description = "Informações resumidas sobre os dados do cliente no sistema TotalPass.")
public class ClienteDadosTotalPassDTO {

    @Schema(description = "Código da empresa associada ao cliente.", example = "1")
    private Integer empresa;

    @Schema(description = "CPF do cliente.", example = "123.456.789-00")
    private String cpf;

    @Schema(description = "Código único da pessoa no sistema.", example = "3001")
    private Integer pessoa;

    @Schema(description = "Número de matrícula do cliente.", example = "3221")
    private String matricula;
}
