package com.pacto.adm.core.dto.enveloperesposta.contrato;

public class EnvelopeRespostaContratoRecorrenciaDTO {
    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"anuidadeNaParcela\": true, "
                    + "\"cancelamentoProporcional\": false, "
                    + "\"cancelamentoPropocionalSomenteRenovacao\": true, "
                    + "\"dataInutilizada\": \"2023-10-01T00:00:00Z\", "
                    + "\"diasBloqueioAcesso\": 30, "
                    + "\"diasCancelamentoAutomatico\": 60, "
                    + "\"diaVencimentoAnuidade\": 15, "
                    + "\"diaVencimentoCartao\": 5, "
                    + "\"fidelidade\": 12, "
                    + "\"mesVencimentoAnuidade\": 12, "
                    + "\"numeroCartao\": \"**** **** **** 1234\", "
                    + "\"parcelaAnuidade\": 3, "
                    + "\"parcelarAnuidade\": true, "
                    + "\"pessoa\": {}, "
                    + "\"qtdDiasCobrarAnuidadeTotal\": 365, "
                    + "\"qtdDiasCobrarProximaParcela\": 30, "
                    + "\"renovavelAutomaticamente\": true, "
                    + "\"ultimaTransacaoAprovada\": \"TXN-123456789\", "
                    + "\"valorAnuidade\": 500.00, "
                    + "\"valorMensal\": 100.00";

}
