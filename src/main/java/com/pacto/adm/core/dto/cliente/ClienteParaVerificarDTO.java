package com.pacto.adm.core.dto.cliente;

import com.pacto.adm.core.dto.PessoaDTO;

import java.util.Date;

public class ClienteParaVerificarDTO {

    private Integer codigoCliente;
    private String matricula;
    private Date verificadoEm;
    private Boolean contratoRenovado;
    private String usuarioVerificacao;
    private PessoaDTO pessoa;

    public ClienteParaVerificarDTO(
            Integer codigoCliente, String matricula, Date verificadoEm, Boolean contratoRenovado,
            String usuarioVerificacao, PessoaDTO pessoa
    ) {
        this.codigoCliente = codigoCliente;
        this.matricula = matricula;
        this.verificadoEm = verificadoEm;
        this.contratoRenovado = contratoRenovado;
        this.usuarioVerificacao = usuarioVerificacao;
        this.pessoa = pessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Date getVerificadoEm() {
        return verificadoEm;
    }

    public void setVerificadoEm(Date verificadoEm) {
        this.verificadoEm = verificadoEm;
    }

    public Boolean getContratoRenovado() {
        return contratoRenovado;
    }

    public void setContratoRenovado(Boolean contratoRenovado) {
        this.contratoRenovado = contratoRenovado;
    }

    public String getUsuarioVerificacao() {
        return usuarioVerificacao;
    }

    public void setUsuarioVerificacao(String usuarioVerificacao) {
        this.usuarioVerificacao = usuarioVerificacao;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }
}
