package com.pacto.adm.core.dto.planoconta;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Plano de Conta", description = "Informações do plano de conta")
public class PlanoContaDTO {

    @Schema(description = "Código único identificador do plano de conta", example = "1")
    private Integer codigo;

    @Schema(description = "Nome do plano de conta", example = "Despesas Operacionais")
    private String nome;

    @Schema(description = "Indica se o plano de conta é interno da empresa", example = "true")
    private Boolean insideltv;

    public PlanoContaDTO(Integer codigo, String nome, Boolean insideltv) {
        this.codigo = codigo;
        this.nome = nome;
        this.insideltv = insideltv;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getInsideltv() {
        return insideltv;
    }

    public void setInsideltv(Boolean insideltv) {
        this.insideltv = insideltv;
    }
}
