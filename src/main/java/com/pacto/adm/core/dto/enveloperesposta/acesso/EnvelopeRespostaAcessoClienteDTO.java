package com.pacto.adm.core.dto.enveloperesposta.acesso;

import com.pacto.adm.core.dto.AcessoClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.coletor.EnvelopeRespostaColetorDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaAcessoClienteDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private AcessoClienteDTO content;

    public AcessoClienteDTO getContent() {
        return content;
    }

    public void setContent(AcessoClienteDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 3, "
                    + "\"cliente\": {" + EnvelopeRespostaClienteLocalDeAcessoDTO.atributos + "}, "
                    + "\"sentido\": \"E\", "
                    + "\"situacao\": \"RV_LIBACESSOAUTORIZADO\", "
                    + "\"localAcesso\": {" + EnvelopeRespostaLocalDeAcessoDTO.atributos + "}, "
                    + "\"coletor\": {" + EnvelopeRespostaColetorDTO.atributos + "}, "
                    + "\"usuario\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"dtHrEntrada\": \"2025-04-08 14:29:29.0\", "
                    + "\"dtHrSaida\": \"2025-04-08 15:36:25.0\", "
                    + "\"meioIdentificacaoEntrada\": 2, "
                    + "\"meioIdentificacaoSaida\": 2, "
                    + "\"dataRegistro\": \"2025-04-08T00:00:00Z\", "
                    + "\"pessoa\": {" + EnvelopeRespostaPessoaDTO.atributos + "}, "
                    + "\"matricula\": 3, "
                    + "\"dataDeAcesso\": \"2025-04-08\", "
                    + "\"dataSaida\": \"2025-04-08\", "
                    + "\"horaEntradaRegistroAcesso\": \"14:29\", "
                    + "\"horaSaidaRegistroAcesso\": \"15:36\", "
                    + "\"registrarSaida\": true, "
                    + "\"ticket\": \"TICKET\", "
                    + "\"liberacaoAcesso\": {" + EnvelopeRespostaLiberacaoAcessoDTO.atributos + "}";

    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
