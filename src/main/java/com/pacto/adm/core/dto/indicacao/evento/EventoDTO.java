package com.pacto.adm.core.dto.indicacao.evento;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Evento", description = "Informações do evento")
public class EventoDTO {

    @Schema(description = "Código único identificador do evento", example = "2")
    private Integer codigo;

    @Schema(description = "Descrição do evento", example = "Aula de Spinning")
    private String descricao;

    @Schema(description = "Status do evento", example = "REALIZADO")
    private String status;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
