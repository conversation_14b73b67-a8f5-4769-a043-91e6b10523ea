package com.pacto.adm.core.dto;

import com.pacto.adm.core.entities.Vinculo;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Vínculo", description = "Informações dos colabores que foram vínculados a um contrato")
public class VinculoDTO {

    @Schema(description = "Código único identificador do vínculo", example = "65")
    private Integer codigo;

    @Schema(description = "Nome do colaborador", example = "Amanda Silva Reis")
    private String colaborador;

    @Schema(description = "Tipo do vínculo", example = "CO")
    private String tipoVinculo;

    @Schema(description = "Código do colaborador", example = "82")
    private Integer codigoColaborador;

    public VinculoDTO() {
    }

    public VinculoDTO(Vinculo vinculo) {
        this.codigo = vinculo.getCodigo();
        if (vinculo.getColaborador() != null) {
            this.codigoColaborador = vinculo.getColaborador().getCodigo();
            this.colaborador = vinculo.getColaborador().getPessoa().getNome();
        }
        if (vinculo.getTipoVinculo() != null) {
            this.tipoVinculo = vinculo.getTipoVinculo();
        }
    }

    public String getColaborador() {
        return colaborador;
    }

    public void setColaborador(String colaborador) {
        this.colaborador = colaborador;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public String getTipoVinculo() {
        return tipoVinculo;
    }

    public void setTipoVinculo(String tipoVinculo) {
        this.tipoVinculo = tipoVinculo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}
