package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração Conciliadora", description = "Configurações para integração com o sistema Conciliadora.")
public class ConfiguracaoIntegracaoConciliadoraDTO {

    @Schema(description = "Indica se a integração com a conciliadora está ativada.", example = "false")
    private boolean usarConciliadora;

    @Schema(description = "Nome da empresa associada à integração com a conciliadora.", example = "Empresa XYZ")
    private String empresaConciliadora;

    @Schema(description = "Senha para autenticação na integração com a conciliadora.", example = "senha-conciliadora-123")
    private String senhaConciliadora;

    @Schema(description = "Detalhes da empresa associada à configuração de integração com a conciliadora.")
    private EmpresaDTO empresa;

    public boolean isUsarConciliadora() {
        return usarConciliadora;
    }

    public void setUsarConciliadora(boolean usarConciliadora) {
        this.usarConciliadora = usarConciliadora;
    }

    public String getEmpresaConciliadora() {
        return empresaConciliadora;
    }

    public void setEmpresaConciliadora(String empresaConciliadora) {
        this.empresaConciliadora = empresaConciliadora;
    }

    public String getSenhaConciliadora() {
        return senhaConciliadora;
    }

    public void setSenhaConciliadora(String senhaConciliadora) {
        this.senhaConciliadora = senhaConciliadora;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
