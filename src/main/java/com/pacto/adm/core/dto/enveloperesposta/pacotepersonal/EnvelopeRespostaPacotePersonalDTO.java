package com.pacto.adm.core.dto.enveloperesposta.pacotepersonal;

import com.pacto.adm.core.dto.PacotePersonalDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaPacotePersonalDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PacotePersonalDTO content;

    public PacotePersonalDTO getContent() {
        return content;
    }

    public void setContent(PacotePersonalDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 120, "
                    + "\"produto\": 398, "
                    + "\"quantidade\": 1, "
                    + "\"valorPosPago\": 0.0, "
                    + "\"valorPrePago\": 100.00";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
