package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Telefone", description = "Informações do telefone")
public class TelefoneDTO {


    @Schema(description = "Tipo do telefone", example = "Celular")
    private String tipo;

    @Schema(description = "Número do telefone", example = "(99)123450000")
    private String numero;

    @Schema(description = "Número do WhatsApp", example = "(99)123450000")
    private boolean whatsapp;

    @Schema(description = "Código único identificador do telefone", example = "5631")
    private Integer codigo;

    @Schema(description = "Descrição do telefone", example = "Telefone do Cariri")
    private String descricao;

    @Schema(description = "DDI do telefone", example = "55")
    private String ddi;

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public boolean isWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(boolean whatsapp) {
        this.whatsapp = whatsapp;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDdi() {
        return ddi;
    }

    public void setDdi(String ddi) {
        this.ddi = ddi;
    }
}
