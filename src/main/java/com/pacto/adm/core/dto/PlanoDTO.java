package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

@JsonInclude
@Schema(name = "Plano", description = "Informações do plano")
public class PlanoDTO {

    @Schema(description = "Código único identificador do plano", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição detalhada do plano", example = "Plano Básico Mensal")
    private String descricao;

    @Schema(description = "Quantidade de compartilhamentos permitidos no plano", example = "5")
    private Integer quantidadeCompartilhamentos;

    @Schema(description = "Indica se a venda do plano é restrita por categorias", example = "true")
    private Boolean restringeVendaPorCategoria;

    @Schema(description = "Lista de códigos de categorias associadas ao plano", example = "[1, 2, 3]")
    private List<Integer> categorias = new ArrayList<>();
    @Schema(description = "Indica se deve bloquear a recompra do plano", example = "false")
    private Boolean bloquearRecompra;
    @Schema(description = "Indica se deve permitir a transferência de crédito", example = "false")
    private Boolean permitirTransferenciaDeCredito;

    public Boolean getRestringeVendaPorCategoria() {
        return restringeVendaPorCategoria;
    }

    public void setRestringeVendaPorCategoria(Boolean restringeVendaPorCategoria) {
        this.restringeVendaPorCategoria = restringeVendaPorCategoria;
    }

    public List<Integer> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<Integer> categorias) {
        this.categorias = categorias;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getQuantidadeCompartilhamentos() {
        return quantidadeCompartilhamentos;
    }

    public void setQuantidadeCompartilhamentos(Integer quantidadeCompartilhamentos) {
        this.quantidadeCompartilhamentos = quantidadeCompartilhamentos;
    }
    public Boolean getBloquearRecompra() {
        return bloquearRecompra;
    }

    public void setBloquearRecompra(Boolean bloquearRecompra) {
        this.bloquearRecompra = bloquearRecompra;
    }

    public Boolean getPermitirTransferenciaDeCredito() {
        return permitirTransferenciaDeCredito;
    }

    public void setPermitirTransferenciaDeCredito(Boolean permitirTransferenciaDeCredito) {
        this.permitirTransferenciaDeCredito = permitirTransferenciaDeCredito;
    }
}
