package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGenericaLeadsDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoGenericaLeadsDTO content;

    public ConfiguracaoIntegracaoGenericaLeadsDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoGenericaLeadsDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1001, "
                    + "\"acaoObjecao\": 2, "
                    + "\"habilitada\": true, "
                    + "\"responsavelPadrao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"horaLimite\": \"19:00\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"urlWebhookGenerico\": \"https://www.exemplo.com/webhook-leads\"";

    public static final String atributosSemEmpresa =
            "\"codigo\": 1001, "
                    + "\"acaoObjecao\": 2, "
                    + "\"habilitada\": true, "
                    + "\"responsavelPadrao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"horaLimite\": \"19:00\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\","
                    + "\"urlWebhookGenerico\": \"https://www.exemplo.com/webhook-leads\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
