package com.pacto.adm.core.dto.enveloperesposta.aluguel;

import com.pacto.adm.core.dto.AluguelArmarioDTO;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaListAluguelArmarioDTO extends RepresentacaoPaginadorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<AluguelArmarioDTO> content;

    public  List<AluguelArmarioDTO>  getContent() {
        return content;
    }

    public void setContent( List<AluguelArmarioDTO>  content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaAluguelArmarioDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
