package com.pacto.adm.core.dto.enveloperesposta.acesso.freepass;

import com.pacto.adm.core.dto.AcessoClienteDTO;
import com.pacto.adm.core.dto.FreepassDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaClienteLocalDeAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaLiberacaoAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaLocalDeAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.coletor.EnvelopeRespostaColetorDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaFreePassDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private FreepassDTO content;

    public FreepassDTO getContent() {
        return content;
    }

    public void setContent(FreepassDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"cliente\": 23, "
                    + "\"produto\": 2, "
                    + "\"dataInicio\": \"2025-05-09\", "
                    + "\"nrDias\": 1, "
                    + "\"responsavel\": 1, "
                    + "\"validarExisteFreepass\": true";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
