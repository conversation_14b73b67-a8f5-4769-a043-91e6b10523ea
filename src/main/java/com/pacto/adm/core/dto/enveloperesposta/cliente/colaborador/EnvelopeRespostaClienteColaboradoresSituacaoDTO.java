package com.pacto.adm.core.dto.enveloperesposta.cliente.colaborador;

import com.pacto.adm.core.dto.ClienteColaboradoresSituacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Informações Cliente Colaborador", description = "Representação das respostas contendo as informações do cliente visualizadas por um colaborador")
public class EnvelopeRespostaClienteColaboradoresSituacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteColaboradoresSituacaoDTO content;

    public ClienteColaboradoresSituacaoDTO getContent() {
        return content;
    }

    public void setContent(ClienteColaboradoresSituacaoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"tipo\": \"A\", "
                    + "\"situacao\": \"ATIVO\", "
                    + "\"situacaoContrato\": \"ATIVO\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
