package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoFogueteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoFogueteDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoFogueteDTO content;

    public ConfiguracaoIntegracaoFogueteDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoFogueteDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"habilitada\": true, "
                    + "\"tokenApi\": \"foguete-token-123\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"produto\": 101";

    public static final String atributosSemEmpresa =
            "\"codigo\": 1, "
                    + "\"habilitada\": true, "
                    + "\"tokenApi\": \"foguete-token-123\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\", "
                    + "\"produto\": 101";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
