package com.pacto.adm.core.dto.enveloperesposta.contrato.config;

import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoDuracaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoRecorrenciaDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaConvenioDescontoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaGrupoDescontoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.parcelas.arredondamento.EnvelopeRespostaArredondamentoParcelaDTO;
import com.pacto.adm.core.dto.enveloperesposta.parcelas.editarnegociacao.EnvelopeRespostaParcelasEditarNegociacaoNovoDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.EnvelopeRespostaPlanoDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.duracao.credito.EnvelopeRespostaDuracaoPlanoCreditoDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.modalidade.EnvelopeRespostaPlanoModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.produto.EnvelopeRespostaPlanoProdutoDTO;
import com.pacto.adm.core.dto.negociacao.ConfigsContratoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaConfigsContratoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfigsContratoDTO content;

    public ConfigsContratoDTO getContent() {
        return content;
    }

    public void setContent(ConfigsContratoDTO content) {
        this.content = content;
    }
    public static final String atributos =
            "\"contratoBase\": 1024, "
                    + "\"empresa\": 1, "
                    + "\"usuario\": 123, "
                    + "\"usuarioAutorizouDesconto\": 456, "
                    + "\"usuarioDataBase\": 789, "
                    + "\"plano\": 201, "
                    + "\"cliente\": 345, "
                    + "\"duracao\": 6, "
                    + "\"condicao\": 2, "
                    + "\"horario\": 15, "
                    + "\"codigoConvenio\": 30, "
                    + "\"vencimentoCartao\": 10, "
                    + "\"convenioDesconto\": 40, "
                    + "\"descontoExtraValor\": 50.00, "
                    + "\"descontoExtraPercentual\": 10.5, "
                    + "\"observacao\": \"Contrato da Academia\", "
                    + "\"cupom\": \"PROMO2025\", "
                    + "\"dataLancamento\": 1715472000000, "
                    + "\"diaPrimeiraParcela\": 1718054400000, "
                    + "\"tipoContrato\": \"Mensal\", "
                    + "\"inicio\": 1715472000000, "
                    + "\"gerarLink\": true, "
                    + "\"arredondar\": true, "
                    + "\"arredondamento\": {" + EnvelopeRespostaArredondamentoParcelaDTO.atributos + "}, "
                    + "\"pacotes\": [{" + EnvelopeRespostaPlanoModalidadeDTO.atributos + "}], "
                    + "\"modalidades\": [{" + EnvelopeRespostaPlanoModalidadeDTO.atributos + "}], "
                    + "\"produtos\": [{" + EnvelopeRespostaPlanoProdutoDTO.atributos + "}], "
                    + "\"configuracoesAvancadas\": {" + EnvelopeRespostaConfigsAvancadasDTO.atributos + "}, "
                    + "\"credito\": {" + EnvelopeRespostaDuracaoPlanoCreditoDTO.atributos + "}, "
                    + "\"parcelas\": [{" + EnvelopeRespostaParcelasEditarNegociacaoNovoDTO.atributos + "}], "
                    + "\"origemSistema\": 1, "
                    + "\"descontoRenovacaoAntecipada\": false, "
                    + "\"alterouDataInicioContrato\": true, "
                    + "\"alterouTipoContrato\": false";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
