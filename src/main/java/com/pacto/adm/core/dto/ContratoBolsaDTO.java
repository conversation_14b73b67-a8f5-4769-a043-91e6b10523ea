package com.pacto.adm.core.dto;

import java.util.Date;

public class ContratoBolsaDTO {

    private Date dataLancamento;
    private String situacaoContrato;
    private ClienteDTO cliente;
    private UsuarioDTO responsavelContrato;

    public ContratoBolsaDTO(Date dataLancamento, String situacaoContrato, ClienteDTO cliente, UsuarioDTO responsavelContrato) {
        this.dataLancamento = dataLancamento;
        this.situacaoContrato = situacaoContrato;
        this.cliente = cliente;
        this.responsavelContrato = responsavelContrato;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public UsuarioDTO getResponsavelContrato() {
        return responsavelContrato;
    }
}
