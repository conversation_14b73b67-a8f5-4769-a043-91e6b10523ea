package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.EnvelopeRespostaPlanoDTO;
import com.pacto.adm.core.dto.grupodesconto.GrupoDescontoDTO;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaGrupoDescontoDTO extends RepresentacaoPaginadorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private GrupoDescontoDTO content;

    public GrupoDescontoDTO getContent() {
        return content;
    }

    public void setContent(GrupoDescontoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"descricao\": \"Desconto Familiar\", "
                    + "\"percentualDescontoGrupo\": 10.0, "
                    + "\"quantidadeMinimaAluno\": 2, "
                    + "\"situacaoAluno\": \"SUBSTITUIR\", "
                    + "\"tipo\": \"FAMILIA\", "
                    + "\"tipoDesconto\": \"PERCENTUAL\", "
                    + "\"valorDescontoGrupo\": 50.00";


    public final static String resposta = "{"
            + "\"content\": ";
}
