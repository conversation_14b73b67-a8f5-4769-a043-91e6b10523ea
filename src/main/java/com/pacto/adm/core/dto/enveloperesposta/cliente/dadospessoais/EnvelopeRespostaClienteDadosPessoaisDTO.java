package com.pacto.adm.core.dto.enveloperesposta.cliente.dadospessoais;

import com.pacto.adm.core.dto.ClienteDadosPessoaisDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Dados Pessoas Clientes", description = "Representação das respostas envolvendo dados pessoais de clientes")
public class EnvelopeRespostaClienteDadosPessoaisDTO {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações encontradas")
    private ClienteDadosPessoaisDTO content;

    public ClienteDadosPessoaisDTO getContent() {
        return content;
    }

    public void setContent(ClienteDadosPessoaisDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"nome\": \"Renato Alves Cariani\", "
                    + "\"matricula\": \"001023\", "
                    + "\"urlFoto\": \"www.sistemapacto.com.br/arquivos/renato-alves-cariani.png\", "
                    + "\"nascimento\": 104803200000, "
                    + "\"idade\": 52, "
                    + "\"sexo\": \"M\", "
                    + "\"genero\": \"M\", "
                    + "\"codigoPessoa\": 393, "
                    + "\"codigoCliente\": 97, "
                    + "\"empresa\":  {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"situacao\": \"ATIVO\", "
                    + "\"situacaoContrato\": \"ATIVO\", "
                    + "\"telefones\": [\"(99) 123459999\"], "
                    + "\"emails\": [\"<EMAIL>\", \"<EMAIL>\"], "
                    + "\"parqPositivo\": false, "
                    + "\"dataMatricula\": 1745971200000, "
                    + "\"dataCadastro\": 1745971200000, "
                    + "\"gympass\": false, "
                    + "\"gogood\": false, "
                    + "\"totalpass\": false, "
                    + "\"freepass\": false, "
                    + "\"objecao\": \"Nenhuma\", "
                    + "\"cpf\": \"123.456.789-10\", "
                    + "\"riscoChurn\": 10, "
                    + "\"riscoChurnLancamento\": \"5\", "
                    + "\"sugestaoGPT\": \"Oferecer descontos para os produtos da academia\", "
                    + "\"sesc\": false, "
                    + "\"nomeSocial\": \"Renato Alves Cariri\", "
                    + "\"matriculaSesc\": \"099321\", "
                    + "\"renda\": 1200.00, "
                    + "\"dataValidadeCarteirinha\": 1745808000000, "
                    + "\"categoria\": \"EXPERIÊNCIA PACTO\", "
                    + "\"possuiIdVindi\": true, "
                    + "\"idVindi\": 13321";

    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
