package com.pacto.adm.core.dto.enveloperesposta.atestado;

import com.pacto.adm.core.dto.AtestadoDTO;
import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaArquivoDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaMovProdutoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Atestado", description = "Representação das respostas contendo as informações do atestado")
public class EnvelopeRespostaAtestadoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private AtestadoDTO content;

    public AtestadoDTO getContent() {
        return content;
    }

    public void setContent(AtestadoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1209, "
                    + "\"parqPositivo\": false, "
                    + "\"observacao\": \"22\", "
                    + "\"avaliacaoFisicaTW\": 123, "
                    + "\"arquivo\": {" + EnvelopeRespostaArquivoDTO.atributos + "}, "
                    + "\"movProduto\": {" + EnvelopeRespostaMovProdutoDTO.atributos + "}, "
                    + "\"data\": \"2025-04-08T14:30:00Z\", "
                    + "\"urlArquivo\": \"www.sistemapacto.com.br/arquivos/atestado.png\"";

    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
