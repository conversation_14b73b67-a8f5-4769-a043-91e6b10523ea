package com.pacto.adm.core.dto.enveloperesposta.avisointerno;

import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Aviso Inativado", description = "Representação das respostas quando um aviso é inativado")
public class EnvelopeRespostaAvisoInativado {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "content: Aviso inativado com sucesso!")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public static final String resposta = "{\"content\": \"Aviso inativado com sucesso!\"} ";
}
