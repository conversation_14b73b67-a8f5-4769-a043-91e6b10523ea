package com.pacto.adm.core.dto.enveloperesposta.evento;

import com.pacto.adm.core.dto.indicacao.evento.EventoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListEventoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<EventoDTO> content;

    public List<EventoDTO> getContent() {
        return content;
    }

    public void setContent(List<EventoDTO> content) {
        this.content = content;
    }


    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaEventoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
