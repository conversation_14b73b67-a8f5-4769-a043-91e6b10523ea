package com.pacto.adm.core.dto.filtros.bi;

import org.json.JSONObject;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class FiltroBIDTO {

    private Integer empresa;
    private Date inicio;
    private Date fim;
    private List<Integer> colaboradores;
    private String quickSearchValue;

    public FiltroBIDTO(String filters) {
        try {
            if (filters != null) {
                JSONObject filtersJson = new JSONObject(filters);
                this.quickSearchValue = filtersJson.optString("quickSearchValue");
                this.empresa = filtersJson.optInt("empresa");
                if (filtersJson.has("inicio")) {
                    String inicio = filtersJson.optString("inicio");
                    if (StringUtils.hasText(inicio)) {
                        this.inicio = Date.from(Instant.parse(inicio));
                    }
                }
                if (filtersJson.has("fim")) {
                    String fim = filtersJson.optString("fim");
                    if (StringUtils.hasText(fim)) {
                        this.fim = Date.from(Instant.parse(fim));
                    }
                }
                if (filtersJson.has("colaboradores")) {
                    this.colaboradores = filtersJson.optJSONArray("colaboradores").toList().stream().map(c -> (Integer) c).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("Erro ao processar filtros JSON (FiltroBIDTO): " + e.getMessage(), e);
        }

    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public List<Integer> getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(List<Integer> colaboradores) {
        this.colaboradores = colaboradores;
    }

    public String getQuickSearchValue() {
        return quickSearchValue;
    }

    public void setQuickSearchValue(String quickSearchValue) {
        this.quickSearchValue = quickSearchValue;
    }
}
