package com.pacto.adm.core.dto.movparcela;

import com.pacto.adm.core.dto.ClienteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

@Schema(name = "Parcela em Atraso", description = "Informações sobre parcelas em atraso de clientes")
public class ParcelaEmAtrasoDTO {

    @Schema(description = "Informações do cliente que possui a parcela em atraso")
    private ClienteDTO cliente;

    @Schema(description = "Nome do plano associado à parcela em atraso", example = "Plano Mensal Academia")
    private String nomePlano;

    @Schema(description = "Código identificador do contrato", example = "12345")
    private Integer codContrato;

    @Schema(description = "Data de início do contrato", example = "2024-01-15")
    private Date dataInicio;

    @Schema(description = "Data de fim do contrato", example = "2024-12-15")
    private Date dataFim;

    @Schema(description = "Duração do contrato em meses", example = "12")
    private Integer duracaoContrato;

    @Schema(description = "Nome das modalidades incluídas no plano", example = "Musculação, Natação")
    private String nomeModalidades;

    @Schema(description = "Quantidade total de parcelas do contrato", example = "12")
    private BigInteger qtdParcelas;

    @Schema(description = "Valor total em aberto das parcelas em atraso", example = "299.90")
    private BigDecimal valorEmAberto;

    public ParcelaEmAtrasoDTO(
            ClienteDTO cliente, String nomePlano, Integer codContrato, Date dataInicio, Date dataFim,
            Integer duracaoContrato, String nomeModalidades, BigInteger qtdParcelas, BigDecimal valorEmAberto
    ) {
        this.cliente = cliente;
        this.nomePlano = nomePlano;
        this.codContrato = codContrato;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.duracaoContrato = duracaoContrato;
        this.nomeModalidades = nomeModalidades;
        this.qtdParcelas = qtdParcelas;
        this.valorEmAberto = valorEmAberto;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Integer getCodContrato() {
        return codContrato;
    }

    public void setCodContrato(Integer codContrato) {
        this.codContrato = codContrato;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Integer getDuracaoContrato() {
        return duracaoContrato;
    }

    public void setDuracaoContrato(Integer duracaoContrato) {
        this.duracaoContrato = duracaoContrato;
    }

    public String getNomeModalidades() {
        return nomeModalidades;
    }

    public void setNomeModalidades(String nomeModalidades) {
        this.nomeModalidades = nomeModalidades;
    }

    public BigInteger getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(BigInteger qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public BigDecimal getValorEmAberto() {
        return valorEmAberto;
    }

    public void setValorEmAberto(BigDecimal valorEmAberto) {
        this.valorEmAberto = valorEmAberto;
    }
}
