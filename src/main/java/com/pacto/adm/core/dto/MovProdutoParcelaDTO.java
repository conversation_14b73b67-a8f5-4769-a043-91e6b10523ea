package com.pacto.adm.core.dto;

import java.math.BigDecimal;

public class MovProdutoParcelaDTO {

    private Integer codigo;
    private ReciboPagamentoDTO reciboPagamento;
    private MovParcelaDTO movParcela;
    private MovProdutoDTO movProduto;
    private BigDecimal valorPago;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ReciboPagamentoDTO getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamentoDTO reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public MovParcelaDTO getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcelaDTO movParcela) {
        this.movParcela = movParcela;
    }

    public MovProdutoDTO getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProdutoDTO movProduto) {
        this.movProduto = movProduto;
    }

    public BigDecimal getValorPago() {
        return valorPago;
    }

    public void setValorPago(BigDecimal valorPago) {
        this.valorPago = valorPago;
    }
}
