package com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios;

import com.pacto.adm.core.dto.indicacao.evento.EventoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaEventoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private EventoDTO content;

    public EventoDTO getContent() {
        return content;
    }

    public void setContent(EventoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 2, "
                    + "\"descricao\": \"Aula de Spinning\", "
                    + "\"status\": \"REALIZADO\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";



}
