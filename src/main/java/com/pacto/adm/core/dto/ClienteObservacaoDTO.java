package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Observação do Cliente", description = "Informações das observações dos clientes")
public class ClienteObservacaoDTO {

    @Schema(description = "Código único identificador da observação", example = "12303")
    private Integer codigo;

    @Schema(description = "Observação do cliente", example = "<strong>Cliente com dificuldades de usar aparelhos para treino de perna</strong>")
    private String observacao;

    @Schema(description = "Observação do cliente sem o HTML", example = "Cliente com dificuldades de usar aparelhos para treino de perna")
    private String observacaoSemHTML;

    @Schema(description = "Data do cadastro da observação", example = "2025-08-30T00:00:00.000Z\n")
    private Date dataCadastro;

    @Schema(description = "Cliente da observação")
    private ClienteDTO cliente;

    @Schema(description = "Usuário que cadastrou a observação")
    private UsuarioDTO usuario;

    @Schema(description = "Indica se a observação é importante", example = "false")
    private Boolean importante = false;

    @Schema(description = "Tipo da observação", example = "Saúde")
    private String tipo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getObservacaoSemHTML() {
        return observacaoSemHTML;
    }

    public void setObservacaoSemHTML(String observacaoSemHTML) {
        this.observacaoSemHTML = observacaoSemHTML;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}
