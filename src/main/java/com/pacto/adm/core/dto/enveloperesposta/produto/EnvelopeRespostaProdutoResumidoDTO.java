package com.pacto.adm.core.dto.enveloperesposta.produto;

import com.pacto.adm.core.dto.modalidade.ProdutoResumidoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaProdutoResumidoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ProdutoResumidoDTO content;

    public ProdutoResumidoDTO getContent() {
        return content;
    }

    public void setContent(ProdutoResumidoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 130, "
                    + "\"descricao\": \"Camiseta Oficial Academia\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
