package com.pacto.adm.core.dto;

import com.pacto.adm.core.enumerador.TiposMensagensEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "Mensagem Cliente", description = "Informações contidas nas mensagens para os clientes")
public class ClienteMensagemDTO {
    @Schema(description = "Código único identificador da mensagem", example = "165535")
    private Integer codigo;

    @Schema(description = "Tipo da mensagem. Valores disponíveis: " +
            "NENHUM, AA (Aviso Consultor)" +
            "AC (Catraca), AM (Aviso Médico), BP (Boletim), DI (Dados Incompletos), PA (Parcela Atraso), RI (Risco)" +
            "OC (Objetivo Curto), OB (Observação), OP (Observação Cliente), PV (Produto Vencido), CV (Cartão de crédito vencido)" +
            "PV (Aluguel de ármario vencido e que não foi devolvido a chave)",
            example = "AA", implementation = TiposMensagensEnum.class)
    private String tipoMensagem;

    @Schema(description = "Mensagem", example = "<strong>Academia fechada para o feriado de 1 de maio</strong>")
    private String mensagem;

    @Schema(description = "Mensagem sem o HTML (Caso ela tenha HTML embutido)", example = "Academia fechada para o feriado 1 de maio")
    private String mensagemSemHTML;

    @Schema(description = "Cliente que recebeu a mensagem")
    private ClienteDTO cliente;

    @Schema(description = "Usuário que fez o envio da mensagem")
    private UsuarioDTO usuario;

    @Schema(description = "Informa se o cliente está bloqueado", example = "false")
    private Boolean bloqueio;

    @Schema(description = "Informa se o cliente está desabilitado", example = "false")
    private Boolean desabilitado;

    @Schema(description = "Data do registro da mensagem", example = "2025-08-30T00:00:00.000Z\n")
    private Date dataRegistro;

    @Schema(description = "Data de atualização da mensagem", example = "2025-08-30T00:00:00.000Z\n")
    private Date dataAtualizacao;

}
