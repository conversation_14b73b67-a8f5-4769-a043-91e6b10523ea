package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

public class FiltroUsuarioJSON {
    private String parametro;
    private Integer codigo;
    private Integer codigoEmpresa;

    public FiltroUsuarioJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
            this.codigo = filters.optInt("codigo");
            this.codigoEmpresa = filters.optInt("codigoEmpresa");
        }
    }

    public String getParametro() { return parametro; }

    public void setParametro(String parametro) { this.parametro = parametro; }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }
}
