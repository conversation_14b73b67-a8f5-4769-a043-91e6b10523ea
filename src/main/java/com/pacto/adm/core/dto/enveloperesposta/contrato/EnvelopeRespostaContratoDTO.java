package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.EnvelopeRespostaPlanoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaContratoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ContratoDTO content;

    public ContratoDTO getContent() {
        return content;
    }

    public void setContent(ContratoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 3, "
                    + "\"tipo\": \"MENSAL\", "
                    + "\"vigenciaDe\": \"2019-04-18T00:00:00Z\", "
                    + "\"vigenciaAte\": \"2020-04-18T00:00:00Z\", "
                    + "\"vigenciaAteAjustada\": \"2020-05-18T00:00:00Z\", "
                    + "\"situacao\": \"ATIVO\", "
                    + "\"pessoa\": 12345, "
                    + "\"pessoaDTO\":{" + EnvelopeRespostaPessoaDTO.atributos + "}, "
                    + "\"pessoaOriginal\":{" + EnvelopeRespostaPessoaDTO.atributos + "}, "
                    + "\"vendaCreditoTreino\": false, "
                    + "\"permiteRenovacaoAutomatica\": true, "
                    + "\"regimeRecorrencia\": true, "
                    + "\"contratoBaseadoRenovacao\": 2, "
                    + "\"valorBaseCalculo\": 100.00, "
                    + "\"valor\": 200.00, "
                    + "\"valorPago\": 150.00, "
                    + "\"descricaoPlano\": \"PLANO BÁSICO\", "
                    + "\"responsavelLancamento\": \"João Silva\", "
                    + "\"dataLancamento\": \"2019-04-18T00:00:00Z\", "
                    + "\"dataAlteracaoManual\": \"2019-05-20T00:00:00Z\", "
                    + "\"dataRenovarRealizada\": \"2020-04-18T00:00:00Z\", "
                    + "\"nomeEmpresa\": \"ACADEMIA FORTE\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "},"
                    + "\"condicaoDePagamento\": \"PARCELADO\", "
                    + "\"duracao\": \"12 MESES\", "
                    + "\"tipoContrato\": \"ANUAL\", "
                    + "\"nomeConsultorReponsavel\": \"Maria Souza\", "
                    + "\"descricaoHorario\": \"SEG-SEX 06:00 - 22:00\", "
                    + "\"renovarContrato\": true, "
                    + "\"rematricularContrato\": false, "
                    + "\"naoPermitirRenovacaoRematriculaDeContratoAnteriores\": false, "
                    + "\"contratoResponsavelRenovacaoMatricula\": 1, "
                    + "\"contratoResponsavelRematriculaMatricula\": 2, "
                    + "\"observacao\": \"Contrato inclui desconto de 10%.\", "
                    + "\"contratoRecorrencia\": {"+ EnvelopeRespostaContratoRecorrenciaDTO.atributos +"}, "
                    + "\"plano\": {" + EnvelopeRespostaPlanoDTO.atributos + "},"
                    + "\"origemContrato\": 1, "
                    + "\"origemSistema\": 2, "
                    + "\"consultorResponsavel\":{" + EnvelopeRespostaColaboradorDTO.atributos + "},"
                    + "\"convenioDesconto\": {" + EnvelopeRespostaConvenioDescontoDTO.atributos + "}, "
                    + "\"grupoDesconto\": {" + EnvelopeRespostaGrupoDescontoDTO.atributos + "}, "
                    + "\"contratoDuracao\": { "+ EnvelopeRespostaContratoDuracaoDTO.atributos + "},"
                    + "\"contratoBaseadoRematricula\": 3, "
                    + "\"permiteNovoContratoDeOutraEmpresa\": false";


    public final static String resposta = "{ \"content\": {"+ atributos + "}}";
}
