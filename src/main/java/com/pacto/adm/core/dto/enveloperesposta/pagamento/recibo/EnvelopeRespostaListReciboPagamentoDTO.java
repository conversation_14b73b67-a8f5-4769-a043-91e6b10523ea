package com.pacto.adm.core.dto.enveloperesposta.pagamento.recibo;

import com.pacto.adm.core.dto.ReciboPagamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pagamento.EnvelopeRespostaMovPagamentoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListReciboPagamentoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ReciboPagamentoDTO> content;

    public List<ReciboPagamentoDTO> getContent() {
        return content;
    }

    public void setContent(List<ReciboPagamentoDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaReciboPagamentoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
