package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.contrato.contratoduracao.ContratoDuracaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.EnvelopeRespostaPlanoDTO;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import com.pacto.adm.core.entities.contrato.ContratoDuracao;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaContratoDuracaoDTO extends RepresentacaoPaginadorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ContratoDuracaoDTO content;

    public ContratoDuracaoDTO getContent() {
        return content;
    }

    public void setContent(ContratoDuracaoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"valorDesejadoMensal\": 500.00, "
                    + "\"valorDesejadoParcela\": 100.00, "
                    + "\"valorDesejado\": 1200.00, "
                    + "\"tipoOperacao\": \"ADICIONAR\", "
                    + "\"tipoValor\": \"MENSAL\", "
                    + "\"valorEspecifico\": 600.00, "
                    + "\"percentualDesconto\": 10.0, "
                    + "\"nrMaximoParcelasCondPagamento\": 12, "
                    + "\"numeroMeses\": 6, "
                    + "\"carencia\": 30, "
                    + "\"quantidadeDiasExtra\": 15, "
                    + "\"carenciaAlterada\": true, "
                    + "\"contratoDuracaoCreditoTreino\": { "+ EnvelopeRespostaContratoDuracaoTreinoDTO.atributos + "}";


    public final static String resposta = "{"
            + "\"content\": ";
}
