package com.pacto.adm.core.dto.negociacao;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pacto.adm.core.dto.grupodesconto.GrupoDescontoDTO;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.ArrayList;
import java.util.List;
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "Simulado", description = "Informações completas de um simulado de negociação de contrato, incluindo valores, datas, descontos e modalidades.")
public class SimuladoDTO {

    @Schema(description = "Código identificador do simulado.", example = "1001")
    private int codigo;

    @Schema(description = "Código do cliente associado ao simulado.", example = "123")
    private int codigoCliente;

    @Schema(description = "Data de início do contrato simulada (timestamp).", example = "1622505600000")
    private Long inicio;

    @Schema(description = "Data de fim do contrato simulada (timestamp).", example = "1654041600000")
    private Long fim;

    @Schema(description = "Data de lançamento do contrato (timestamp).", example = "1622505600000")
    private Long lancamento;

    @Schema(description = "Valor final da negociação após todos os cálculos.", example = "1200.00")
    private double valorFinal;

    @Schema(description = "Valor base utilizado na negociação.", example = "1000.00")
    private double valorBase;

    @Schema(description = "Valor do plano associado à negociação.", example = "950.00")
    private double valorPlano;

    @Schema(description = "Valor total de acréscimos aplicados.", example = "50.00")
    private double valorAcrescimos;

    @Schema(description = "Total de descontos aplicados por convênio.", example = "30.00")
    private double descontosConvenio;

    @Schema(description = "Total de descontos extras concedidos.", example = "20.00")
    private double descontosExtra;

    @Schema(description = "Total geral de descontos aplicados.", example = "50.00")
    private double descontos;

    @Schema(description = "Indica se o contrato foi enviado.", example = "true")
    private boolean contratoEnviado;

    @Schema(description = "Indica se o recibo foi enviado.", example = "false")
    private boolean reciboEnviado;

    @Schema(description = "Situação atual do contrato.", example = "EM ANDAMENTO")
    private String situacaoContrato;

    @Schema(description = "Data de lançamento em formato texto.", example = "2025-05-01")
    private String dataLancamento;

    @Schema(description = "Data de início da vigência do contrato (formato texto).", example = "2025-05-01")
    private String vigenciaDe;

    @Schema(description = "Data de vigência ajustada do contrato (formato texto).", example = "2026-05-01")
    private String vigenciaAteAjustada;

    @Schema(description = "Situação geral da simulação.", example = "SIMULADO")
    private String situacao;

    @Schema(description = "Nome do plano envolvido na negociação.", example = "PLANO PREMIUM")
    private String nomePlano;

    @Schema(description = "Lista de modalidades vinculadas ao contrato simulado.")
    private List<ContratoModalidadeWS> modalidades;

    @Schema(description = "Valores de cada modalidade vinculada à simulação.")
    private List<ValorModalidadeDTO> valoresModalidades;

    @Schema(description = "Produtos associados à simulação.")
    private List<ProdutoWS> produtos;

    @Schema(description = "Valores das parcelas ajustadas com arredondamento.")
    private List<ArredondamentoParcelaDTO> valoresArredondados;

    @Schema(description = "Lista de parcelas da negociação simulada.")
    private List<ParcelasEditarNegociacaoNovo> parcelas;

    @Schema(description = "Valor mensal do contrato simulado.", example = "100.00")
    private double valorMensal;

    @Schema(description = "Valor total da anuidade no contrato simulado.", example = "1200.00")
    private double valorAnuidade;

    @Schema(description = "Dia de vencimento da anuidade.", example = "10")
    private int diaVencimentoAnuidade;

    @Schema(description = "Mês de vencimento da anuidade.", example = "Dezembro")
    private String mesVencimentoAnuidade;

    @Schema(description = "Valor da adesão no contrato.", example = "100.00")
    private double valorAdesao;

    @Schema(description = "Valor total do contrato.", example = "1350.00")
    private double valorContrato;

    @Schema(description = "Valor da matrícula.", example = "200.00")
    private double valorMatricula;

    @Schema(description = "Valor proporcional (pro rata) calculado.", example = "33.33")
    private double valorProRata;

    @Schema(description = "Valor da primeira parcela.", example = "150.00")
    private double valorPrimeiraParcela;

    @Schema(description = "Código do plano relacionado ao simulado.", example = "5")
    private int codigoPlano;

    @Schema(description = "Número máximo de vezes que a adesão pode ser parcelada.", example = "3")
    private int maxVezesParcelarAdesao;

    @Schema(description = "Mensagem de validação associada à simulação.", example = "Simulação válida.")
    private String msgValidacao;

    @Schema(description = "Número total de meses do plano simulado.", example = "12")
    private Integer numeroMeses;

    @Schema(description = "Indica se o plano envolve a modalidade CrossFit.", example = "false")
    private boolean crossfit;

    @Schema(description = "Situação subordinada ao contrato, caso aplicável.", example = "RENOVACAO")
    private String situacaoSubordinada;

    @Schema(description = "Indica se é permitido marcar aula com o contrato simulado.", example = "true")
    private boolean permiteMarcarAula;

    @Schema(description = "Indica se o contrato é uma venda de crédito para treino.", example = "false")
    private boolean vendaCreditoTreino;

    @Schema(description = "Número de parcelas definidas na negociação.", example = "6")
    private Integer nrParcelas;

    @Schema(description = "Descrição do horário vinculado à simulação.", example = "SEG-SEX 06:00 - 22:00")
    private String horarioDescricao;

    @Schema(description = "Indica se o contrato permite renovação.", example = "true")
    private boolean permiteRenovar;

    @Schema(description = "Indica se o contrato está em modo de simulação.", example = "true")
    private boolean simulacao;

    @Schema(description = "Indica se há bolsa associada ao contrato.", example = "false")
    private boolean bolsa;

    @Schema(description = "Número máximo de vezes que o produto pode ser parcelado.", example = "2")
    private int maxVezesParcelarProduto;

    @Schema(description = "Descrição da cobrança da primeira parcela.", example = "Cobrança integral no ato.")
    private String descricaoCobrancaPrimeiraParcela;

    @Schema(description = "Informações relacionadas à renovação de crédito para treino.", example = "Renovação automática ativada.")
    private String informacaoRenovacaoCreditoTreino;

    @Schema(description = "Ano de cobrança da anuidade.", example = "2025")
    private Integer anoCobrancaAnuidade;

    @Schema(description = "Indica se a renovação automática é permitida.", example = "true")
    private boolean permiterenovacaoautomatica;

    @Schema(description = "Indica se a renovação automática está marcada como sim ou não.", example = "true")
    private boolean renovacaoautomaticasimnao;

    @Schema(description = "Número de dias de cobrança associados ao cartão.", example = "5")
    private Integer diasCartao;

    @Schema(description = "Informações sobre o grupo de desconto aplicado na simulação.")
    private GrupoDescontoDTO grupo;

    @Schema(description = "Informações sobre o desconto aplicado por renovação antecipada.")
    private DescontoDTO descontoRenovacaoAntecipada;

    public List<ValorModalidadeDTO> getValoresModalidades(){
        return valoresModalidades;
    }

    public Long getInicio() {
        return inicio;
    }

    public Long getFim() {
        return fim;
    }

    public Long getLancamento() {
        return lancamento;
    }

    public double getDescontos() {
        return descontos;
    }

    public void setDescontos(double descontos) {
        this.descontos = descontos;
    }

    public void setInicio(Long inicio) {
        this.inicio = inicio;
    }

    public void setFim(Long fim) {
        this.fim = fim;
    }

    public void setLancamento(Long lancamento) {
        this.lancamento = lancamento;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(int codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public boolean isContratoEnviado() {
        return contratoEnviado;
    }

    public void setContratoEnviado(boolean contratoEnviado) {
        this.contratoEnviado = contratoEnviado;
    }

    public boolean isReciboEnviado() {
        return reciboEnviado;
    }

    public void setReciboEnviado(boolean reciboEnviado) {
        this.reciboEnviado = reciboEnviado;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getVigenciaDe() {
        return vigenciaDe;
    }

    public void setVigenciaDe(String vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public String getVigenciaAteAjustada() {
        return vigenciaAteAjustada;
    }

    public void setVigenciaAteAjustada(String vigenciaAteAjustada) {
        this.vigenciaAteAjustada = vigenciaAteAjustada;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public List<ContratoModalidadeWS> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ContratoModalidadeWS> modalidades) {
        this.modalidades = modalidades;
    }

    public double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public double getValorAnuidade() {
        return valorAnuidade;
    }

    public void setValorAnuidade(double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public int getDiaVencimentoAnuidade() {
        return diaVencimentoAnuidade;
    }

    public void setDiaVencimentoAnuidade(int diaVencimentoAnuidade) {
        this.diaVencimentoAnuidade = diaVencimentoAnuidade;
    }

    public String getMesVencimentoAnuidade() {
        return mesVencimentoAnuidade;
    }

    public void setMesVencimentoAnuidade(String mesVencimentoAnuidade) {
        this.mesVencimentoAnuidade = mesVencimentoAnuidade;
    }

    public double getValorAdesao() {
        return valorAdesao;
    }

    public void setValorAdesao(double valorAdesao) {
        this.valorAdesao = valorAdesao;
    }

    public int getCodigoPlano() {
        return codigoPlano;
    }

    public void setCodigoPlano(int codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    public String getMsgValidacao() {
        return msgValidacao;
    }

    public void setMsgValidacao(String msgValidacao) {
        this.msgValidacao = msgValidacao;
    }

    public double getValorBase() {
        return valorBase;
    }

    public void setValorBase(double valorBase) {
        this.valorBase = valorBase;
    }

    public Integer getNumeroMeses() {
        if (numeroMeses == null) {
            numeroMeses = 0;
        }
        return numeroMeses;
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public boolean isCrossfit() {
        return crossfit;
    }

    public void setCrossfit(boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getSituacaoSubordinada() {
        if (situacaoSubordinada == null) {
            situacaoSubordinada = "";
        }
        return situacaoSubordinada;
    }

    public void setSituacaoSubordinada(String situacaoSubordinada) {
        this.situacaoSubordinada = situacaoSubordinada;
    }

    public boolean isPermiteMarcarAula() {
        return permiteMarcarAula;
    }

    public void setPermiteMarcarAula(boolean permiteMarcarAula) {
        this.permiteMarcarAula = permiteMarcarAula;
    }

    public boolean isVendaCreditoTreino() {
        return vendaCreditoTreino;
    }

    public void setVendaCreditoTreino(boolean vendaCreditoTreino) {
        this.vendaCreditoTreino = vendaCreditoTreino;
    }

    public Integer getNrParcelas() {
        if (nrParcelas == null) {
            nrParcelas = 0;
        }
        return nrParcelas;
    }

    public void setNrParcelas(Integer nrParcelas) {
        this.nrParcelas = nrParcelas;
    }

    public String getHorarioDescricao() {
        if (horarioDescricao == null) {
            horarioDescricao = "";
        }
        return horarioDescricao;
    }

    public void setHorarioDescricao(String horarioDescricao) {
        this.horarioDescricao = horarioDescricao;
    }

    public boolean isPermiteRenovar() {
        return permiteRenovar;
    }

    public void setPermiteRenovar(boolean permiteRenovar) {
        this.permiteRenovar = permiteRenovar;
    }

    public boolean isSimulacao() {
        return simulacao;
    }

    public void setSimulacao(boolean simulacao) {
        this.simulacao = simulacao;
    }

    public boolean isBolsa() {
        return bolsa;
    }

    public void setBolsa(boolean bolsa) {
        this.bolsa = bolsa;
    }

    public List<ProdutoWS> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<ProdutoWS> produtos) {
        this.produtos = produtos;
    }

    public List<ParcelasEditarNegociacaoNovo> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<ParcelasEditarNegociacaoNovo> parcelas) {
        this.parcelas = parcelas;
    }

    public int getMaxVezesParcelarAdesao() {
        return maxVezesParcelarAdesao;
    }

    public void setMaxVezesParcelarAdesao(int maxVezesParcelarAdesao) {
        this.maxVezesParcelarAdesao = maxVezesParcelarAdesao;
    }

    public double getValorMatricula() {
        return valorMatricula;
    }

    public void setValorMatricula(double valorMatricula) {
        this.valorMatricula = valorMatricula;
    }

    public double getValorProRata() {
        return valorProRata;
    }

    public void setValorProRata(double valorProRata) {
        this.valorProRata = valorProRata;
    }

    public double getValorPrimeiraParcela() {
        return valorPrimeiraParcela;
    }

    public void setValorPrimeiraParcela(double valorPrimeiraParcela) {
        this.valorPrimeiraParcela = valorPrimeiraParcela;
    }

    public int getMaxVezesParcelarProduto() {
        return maxVezesParcelarProduto;
    }

    public void setMaxVezesParcelarProduto(int maxVezesParcelarProduto) {
        this.maxVezesParcelarProduto = maxVezesParcelarProduto;
    }

    public String getDescricaoCobrancaPrimeiraParcela() {
        return descricaoCobrancaPrimeiraParcela;
    }

    public void setDescricaoCobrancaPrimeiraParcela(String descricaoCobrancaPrimeiraParcela) {
        this.descricaoCobrancaPrimeiraParcela = descricaoCobrancaPrimeiraParcela;
    }

    public Integer getAnoCobrancaAnuidade() {
        return anoCobrancaAnuidade;
    }

    public void setAnoCobrancaAnuidade(Integer anoCobrancaAnuidade) {
        this.anoCobrancaAnuidade = anoCobrancaAnuidade;
    }

    public boolean isPermiterenovacaoautomatica() {
        return permiterenovacaoautomatica;
    }

    public void setPermiterenovacaoautomatica(boolean permiterenovacaoautomatica) {
        this.permiterenovacaoautomatica = permiterenovacaoautomatica;
    }

    public boolean isRenovacaoautomaticasimnao() {
        return renovacaoautomaticasimnao;
    }

    public void setRenovacaoautomaticasimnao(boolean renovacaoautomaticasimnao) {
        this.renovacaoautomaticasimnao = renovacaoautomaticasimnao;
    }

    public double getValorContrato() {
        return valorContrato;
    }

    public void setValorContrato(double valorContrato) {
        this.valorContrato = valorContrato;
    }

    public double getValorPlano() {
        return valorPlano;
    }

    public void setValorPlano(double valorPlano) {
        this.valorPlano = valorPlano;
    }

    public double getValorAcrescimos() {
        return valorAcrescimos;
    }

    public void setValorAcrescimos(double valorAcrescimos) {
        this.valorAcrescimos = valorAcrescimos;
    }

    public double getDescontosConvenio() {
        return descontosConvenio;
    }

    public void setDescontosConvenio(double descontosConvenio) {
        this.descontosConvenio = descontosConvenio;
    }

    public double getDescontosExtra() {
        return descontosExtra;
    }

    public void setDescontosExtra(double descontosExtra) {
        this.descontosExtra = descontosExtra;
    }

    public void setValoresModalidades(List<ValorModalidadeDTO> valoresModalidades) {
        this.valoresModalidades = valoresModalidades;
    }

    public Integer getDiasCartao() {
        return diasCartao;
    }

    public void setDiasCartao(Integer diasCartao) {
        this.diasCartao = diasCartao;
    }

    public List<ArredondamentoParcelaDTO> getValoresArredondados() {
        return valoresArredondados;
    }

    public void setValoresArredondados(List<ArredondamentoParcelaDTO> valoresArredondados) {
        this.valoresArredondados = valoresArredondados;
    }

    public GrupoDescontoDTO getGrupo() {
        return grupo;
    }

    public void setGrupo(GrupoDescontoDTO grupo) {
        this.grupo = grupo;
    }

    public DescontoDTO getDescontoRenovacaoAntecipada() {
        return descontoRenovacaoAntecipada;
    }

    public void setDescontoRenovacaoAntecipada(DescontoDTO descontoRenovacaoAntecipada) {
        this.descontoRenovacaoAntecipada = descontoRenovacaoAntecipada;
    }

    public String getInformacaoRenovacaoCreditoTreino() {
        return informacaoRenovacaoCreditoTreino;
    }

    public void setInformacaoRenovacaoCreditoTreino(String informacaoRenovacaoCreditoTreino) {
        this.informacaoRenovacaoCreditoTreino = informacaoRenovacaoCreditoTreino;
    }
}
