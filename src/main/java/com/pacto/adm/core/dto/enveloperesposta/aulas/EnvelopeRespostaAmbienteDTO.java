package com.pacto.adm.core.dto.enveloperesposta.aulas;

import com.pacto.adm.core.dto.ambiente.AmbienteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaAmbienteDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private AmbienteDTO content;

    public AmbienteDTO getContent() {
        return content;
    }

    public void setContent(AmbienteDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 60, "
                    + "\"descricao\": \"Sala 101 - Bloco A\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
