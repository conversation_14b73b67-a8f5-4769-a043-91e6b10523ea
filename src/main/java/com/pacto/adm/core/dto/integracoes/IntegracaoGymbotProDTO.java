package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Integração Gymbot Pro", description = "Informações utilizadas na comunicação entre sistemas com o Gymbot Pro.")
public class IntegracaoGymbotProDTO {

    @Schema(description = "Número de identificação da interação ou atendimento.", example = "456789")
    private String numero;

    @Schema(description = "Nome do Gymbot Pro.", example = "Gymbot Pro")
    private String nome;

    @Schema(description = "Identificador da empresa.", example = "1")
    private String idEmpresa;

    @Schema(description = "Identificador do cliente.", example = "123")
    private String idCliente;

    @Schema(description = "URL do WebHook genérico utilizado para integração com o Gymbot Pro.", example = "https://www.exemplo.com/webhook-gymbot")
    private String urlWebhookGenericoGymbot;

    @Schema(description = "Nome do usuário do Gymbot Pro.", example = "gymbot.academia")
    private String nomeUsuario;

    @Schema(description = "Identificador do usuário.", example = "3")
    private String idUsuario;

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getIdEmpresa() {
        return idEmpresa;
    }

    public void setIdEmpresa(String idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public String getUrlWebhookGenericoGymbot() {
        return urlWebhookGenericoGymbot;
    }

    public void setUrlWebhookGenericoGymbot(String urlWebhookGenericoGymbot) {
        this.urlWebhookGenericoGymbot = urlWebhookGenericoGymbot;
    }

    public String getIdCliente() {
        return idCliente;
    }

    public void setIdCliente(String idCliente) {
        this.idCliente = idCliente;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public String getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(String idUsuario) {
        this.idUsuario = idUsuario;
    }
}
