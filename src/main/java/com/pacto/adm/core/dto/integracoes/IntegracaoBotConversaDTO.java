package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Integração Bot Conversa", description = "Informações utilizadas na comunicação entre sistemas com o Bot Conversa.")
public class IntegracaoBotConversaDTO {

    @Schema(description = "Número de identificação do atendimento ou interação.", example = "123456")
    private String numero;

    @Schema(description = "Nome do cliente.", example = "Bot Conversa")
    private String nome;

    @Schema(description = "Identificador da empresa.", example = "1")
    private String idEmpresa;

    @Schema(description = "Identificador do cliente.", example = "123")
    private String idCliente;

    @Schema(description = "URL do WebHook genérico utilizado para integração com o Bot Conversa.", example = "https://www.exemplo.com/webhook-bot")
    private String urlWebhookGenerico;

    @Schema(description = "Nome do usuário.", example = "bot.academia")
    private String nomeUsuario;

    @Schema(description = "Identificador do usuário.", example = "1")
    private String idUsuario;

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getIdEmpresa() {
        return idEmpresa;
    }

    public void setIdEmpresa(String idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public String getUrlWebhookGenerico() {
        return urlWebhookGenerico;
    }

    public void setUrlWebhookGenerico(String urlWebhookGenerico) {
        this.urlWebhookGenerico = urlWebhookGenerico;
    }

    public String getIdCliente() {
        return idCliente;
    }

    public void setIdCliente(String idCliente) {
        this.idCliente = idCliente;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public String getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(String idUsuario) {
        this.idUsuario = idUsuario;
    }
}
