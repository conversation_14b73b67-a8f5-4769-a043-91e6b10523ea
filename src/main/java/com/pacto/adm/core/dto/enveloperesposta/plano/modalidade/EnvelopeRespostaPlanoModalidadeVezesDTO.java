package com.pacto.adm.core.dto.enveloperesposta.plano.modalidade;

import com.pacto.adm.core.dto.negociacao.PlanoModalidadeVezesDTO;
import io.swagger.v3.oas.annotations.media.Schema;


public class EnvelopeRespostaPlanoModalidadeVezesDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PlanoModalidadeVezesDTO content;

    public PlanoModalidadeVezesDTO getContent() {
        return content;
    }

    public void setContent(PlanoModalidadeVezesDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"vezes\": 10, "
                    + "\"duracao\": 60, "
                    + "\"horario\": 2, "
                    + "\"tipoOperacao\": \"ADICAO\", "
                    + "\"tipoValor\": \"FIXO\", "
                    + "\"valorModalidade\": 50.00, "
                    + "\"valorEspecifico\": 40.00, "
                    + "\"percentualDesconto\": 10.00, "
                    + "\"referencia\": false, "
                    + "\"creditoSessao\": true";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
