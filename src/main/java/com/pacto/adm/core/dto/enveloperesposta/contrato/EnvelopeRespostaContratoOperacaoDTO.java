package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.ContratoOperacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaContratoOperacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ContratoOperacaoDTO content;

    public ContratoOperacaoDTO getContent() {
        return content;
    }

    public void setContent(ContratoOperacaoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 65, "
                    + "\"clienteTransfereDias\": 10, "
                    + "\"clienteRecebeDias\":15, "
                    + "\"descricaoCalculo\": \"Adicionar aulas de natação ao contrato\", "
                    + "\"observacao\": \"Cliente optou por adicionar aulas de natação ao seu contrato\", "
                    + "\"dataOperacao\": \"2025-05-07T00:00:00\", "
                    + "\"dataInicioEfetivacaoOperacao\": \"2025-05-08T00:00:00\", "
                    + "\"dataFimEfetivacaoOperacao\": \"2025-05-10T00:00:00\", "
                    + "\"operacaoPaga\": true, "
                    + "\"tipoOperacao\": \"IM\", "
                    + "\"valor\": 20.00, "
                    + "\"nrDiasOperacao\": 1, "
                    + "\"informacoes\": \"CPF, RG, Código do Contrato e Exame Médico do Cliente para adicionar as aulas de natação\", "
                    + "\"chaveArquivo\": \"4568723425\", "
                    + "\"nomeArquivo\": \"contrato-natacao\", "
                    + "\"formatoArquivo\": \"pdf\", "
                    + "\"origemSistema\": 1, "
                    + "\"informacoesDesfazer\": \"Apenas CPF, RG e assinatura do cliente\", "
                    + "\"contrato\": {" + EnvelopeRespostaContratoDTO.atributos + "}, "
                    + "\"tipoJustificativa\": {" + EnvelopeRespostaJustificativaOperacaoDTO.atributos + "}, "
                    + "\"responsavel\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"responsavelLiberacao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                    + "\"diasOperacao\": 1, "
                    + "\"urlArquivoAtestado\": \"www.exemplo.pactosolucoes/arquivos/atestado.pdf\", "
                    + "\"permiteEstornar\": true";


    public final static String resposta = "{\"content\": {" + atributos + "}}";
}
