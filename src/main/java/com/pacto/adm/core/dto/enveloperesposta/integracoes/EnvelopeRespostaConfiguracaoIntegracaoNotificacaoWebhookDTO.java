package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoNotificacaoWebhookDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoNotificacaoWebhookDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoNotificacaoWebhookDTO content;

    public ConfiguracaoIntegracaoNotificacaoWebhookDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoNotificacaoWebhookDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"notificarWebhook\": true, "
                    + "\"urlWebhookNotificar\": \"https://api.exemplo.com/webhook\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"notificarWebhook\": true, "
                    + "\"urlWebhookNotificar\": \"https://api.exemplo.com/webhook\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
