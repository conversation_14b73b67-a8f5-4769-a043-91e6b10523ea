package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGymbotProDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoGymBotProDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoGymbotProDTO content;

    public ConfiguracaoIntegracaoGymbotProDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoGymbotProDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"ativo\": true, "
                    + "\"codigo\": 123, "
                    + "\"empresa\": 1, "
                    + "\"descricao\": \"Integração com Gymbot Pro para atendimento automatizado.\", "
                    + "\"tipoFluxo\": 2, "
                    + "\"fase\": \"INICIAL\", "
                    + "\"token\": \"token-gymbot-pro-xyz123\", "
                    + "\"idFluxo\": \"fluxo-001\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
