package com.pacto.adm.core.dto.grupocolaborador;

import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Grupo Colaborador", description = "Informações do grupo de colaboradores")
public class GrupoColaboradorDTO {

    @Schema(description = "Código único identificador do grupo de colaboradores", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição ou nome do grupo de colaboradores", example = "Equipe de Vendas")
    private String descricao;

    @Schema(description = "Detalhes da empresa associada ao grupo de colaboradores")
    private EmpresaDTO empresa;

    @Schema(description = "Detalhes do usuário gerente responsável pelo grupo")
    private UsuarioDTO gerente;

    @Schema(description = "Lista de participantes que fazem parte do grupo de colaboradores")
    private List<GrupoColaboradorParticipanteDTO> grupoColaboradorParticipantes;

    @Schema(description = "Situação atual do grupo de colaboradores. \n\n" +
            "**Valores disponíveis**\n" +
            "- AT (Ativo)\n" +
            "- IN (Inativo)\n" +
            "- NA (Inativo)\n", example = "AT")
    private String situacaoGrupo;

    @Schema(description = "Tipo do grupo de colaboradores. \n\n" +
            "**Valores disponíveis**\n" +
            "- FA (Em Família)\n" +
            "- GR (Em grupo)\n", example = "GR")
    private String tipoGrupo;

    @Schema(description = "Indica se é um grupo especial para colaboradores sem grupo definido", example = "false")
    private boolean semGrupo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public UsuarioDTO getGerente() {
        return gerente;
    }

    public void setGerente(UsuarioDTO gerente) {
        this.gerente = gerente;
    }

    public List<GrupoColaboradorParticipanteDTO> getGrupoColaboradorParticipantes() {
        return grupoColaboradorParticipantes;
    }

    public void setGrupoColaboradorParticipantes(List<GrupoColaboradorParticipanteDTO> grupoColaboradorParticipantes) {
        this.grupoColaboradorParticipantes = grupoColaboradorParticipantes;
    }

    public String getSituacaoGrupo() {
        return situacaoGrupo;
    }

    public void setSituacaoGrupo(String situacaoGrupo) {
        this.situacaoGrupo = situacaoGrupo;
    }

    public String getTipoGrupo() {
        return tipoGrupo;
    }

    public void setTipoGrupo(String tipoGrupo) {
        this.tipoGrupo = tipoGrupo;
    }

    public boolean isSemGrupo() {
        return semGrupo;
    }

    public void setSemGrupo(boolean semGrupo) {
        this.semGrupo = semGrupo;
    }
}
