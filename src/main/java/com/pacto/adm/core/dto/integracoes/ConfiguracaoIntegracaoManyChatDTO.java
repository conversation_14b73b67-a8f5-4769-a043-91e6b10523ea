package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracaomanychat.TagDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "Configuração de Integração com ManyChat", description = "Configurações necessárias para a integração com a plataforma ManyChat.")
public class ConfiguracaoIntegracaoManyChatDTO {

    @Schema(description = "Indica se a integração com o ManyChat está habilitada.", example = "true")
    private Boolean habilitada;

    @Schema(description = "Token de autenticação para a API do ManyChat.", example = "manychat-token-xyz")
    private String token;

    @Schema(description = "Detalhes da tag utilizada para identificar a unidade na integração com o ManyChat.")
    private TagDTO tagUnidade;

    @Schema(description = "Detalhes da empresa associada à configuração da integração.")
    private EmpresaDTO empresa;

    public Boolean getHabilitada() {
        return habilitada;
    }

    public void setHabilitada(Boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public TagDTO getTagUnidade() {
        return tagUnidade;
    }

    public void setTagUnidade(TagDTO tagUnidade) {
        this.tagUnidade = tagUnidade;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
