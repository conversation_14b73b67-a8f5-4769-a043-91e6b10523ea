package com.pacto.adm.core.dto.enveloperesposta.vacina;

import com.pacto.adm.core.dto.ComprovanteVacinaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Informações Cliente Colaborador", description = "Representação das respostas contendo as informações do cliente visualizadas por um colaborador")
public class EnvelopeRespostaComprovanteVacinaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ComprovanteVacinaDTO content;

    public ComprovanteVacinaDTO getContent() {
        return content;
    }

    public void setContent(ComprovanteVacinaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 434, "
                    + "\"tipo\": 1, "
                    + "\"dataAplicacao\": \"2022-05-05T00:00:00Z\", "
                    + "\"fabricante\": \"AstraZeneca\", "
                    + "\"lote\": \"23134\", "
                    + "\"estabelecimentoSaude\": \"UBS Jardim Primavera\", "
                    + "\"vacinador\": \"Gregory House\", "
                    + "\"pessoa\": 34, "
                    + "\"usuarioResponsavel\": 1, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
