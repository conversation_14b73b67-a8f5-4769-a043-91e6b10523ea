package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Resultado Negociação", description = "Informações de retorno após a finalização da negociação do contrato.")
public class ResultadoNegociacaoDTO {

    @Schema(description = "Código do contrato gerado após a negociação.", example = "12345")
    private Integer contrato;

    @Schema(description = "Link para visualização ou assinatura do contrato.", example = "https://pactosolucoes.com.br/contrato/12345")
    private String link;

    @Schema(description = "Número de WhatsApp formatado para envio do link.", example = "+5511999999999")
    private String whatsapp;

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(String whatsapp) {
        this.whatsapp = whatsapp;
    }
}
