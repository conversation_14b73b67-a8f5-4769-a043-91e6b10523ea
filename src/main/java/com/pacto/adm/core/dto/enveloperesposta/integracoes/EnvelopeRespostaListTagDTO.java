package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.integracaomanychat.TagDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class EnvelopeRespostaListTagDTO {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações encontradas")
    private List<TagDTO> content;

    public List<TagDTO> getContent() {
        return content;
    }

    public void setContent(List<TagDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaTagDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
