package com.pacto.adm.core.dto.enveloperesposta.empresa;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListEmpresaDTO extends RepresentacaoPaginadorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<EmpresaDTO> content;

    public List<EmpresaDTO> getContent() {
        return content;
    }

    public void setContent(List<EmpresaDTO> content) {
        this.content = content;
    }


    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaEmpresaDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
