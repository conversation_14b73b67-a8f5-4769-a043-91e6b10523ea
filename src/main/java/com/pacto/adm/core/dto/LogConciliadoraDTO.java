package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor


@Schema(description = "Log das Conciliadoras de Pagamantos")
public class LogConciliadoraDTO {
    @Schema(description = "Codigo único identificador do Log", example = "5")
    private Integer codigo;
    @Schema(description = "Data e hora que foi registrada o Log", example = "2025/04/28T00:00:00Z")
    private Date data;
    @Schema(description = "Mostra se a operação ocorreu com sucesso", example = "false")
    private boolean sucesso = false;
    @Schema(description = "Resultado do Log", example = "Transferência realizada")
    private String resultado;
}
