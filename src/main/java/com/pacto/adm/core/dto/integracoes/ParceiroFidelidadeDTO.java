package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonInclude
@Schema(description = "Informações do Parceiro Fidelidade")
public class ParceiroFidelidadeDTO {
    @Schema(description = "Código único identificador do parceiro de fidelidade", example = "1")
    private Integer codigo;

    @Schema(description = "Tipo do parceiro de fidelidade", example = "2")
    private Integer tipoParceiro;

    @Schema(description = "Indica se o cliente deve ser validado antes da integração", example = "true")
    private boolean validarCliente;

    @Schema(description = "ID do cliente utilizado na autenticação da API do parceiro", example = "client-id-12345")
    private String clientId;

    @Schema(description = "Segredo do cliente utilizado na autenticação da API do parceiro", example = "client-secret-67890")
    private String clientSecret;

    @Schema(description = "ID do cliente utilizado para resgate de pontos", example = "redemption-client-id-12345")
    private String clientIdRedemption;

    @Schema(description = "Segredo do cliente utilizado para resgate de pontos", example = "redemption-client-secret-67890")
    private String clientSecretRedemption;

    @Schema(description = "Código da loja associada ao parceiro de fidelidade", example = "LOJA-001")
    private String codigoLoja;

    @Schema(description = "Código da máquina utilizada na integração", example = "MAQUINA-001")
    private String codigoMaquina;

    @Schema(description = "Código da oferta disponível no parceiro de fidelidade", example = "OFERTA-001")
    private String codigoOferta;

    @Schema(description = "Código utilizado para resgate de pontos", example = "RESCATE-001")
    private String codigoResgate;

    @Schema(description = "Tags associadas ao parceiro de fidelidade", example = "tag1, tag2, tag3")
    private String tags;

    @Schema(description = "CPF do cliente associado ao parceiro de fidelidade", example = "123.456.789-00")
    private String cpf;

    @Schema(description = "Token de autenticação gerado para o parceiro de fidelidade", example = "token-abcdef12345")
    private String token;

    @Schema(description = "Data de expiração do token de autenticação", example = "2023-12-31T23:59:59Z")
    private Date dataExpiracaoToken;

    @Schema(description = "Indica se o ambiente é de produção", example = "true")
    private boolean ambienteProducao;

    @Schema(description = "Indica se parcelas vencidas geram pontos no programa de fidelidade", example = "false")
    private boolean parcelaVencidaGeraPonto;

    @Schema(description = "Código da empresa associada ao parceiro de fidelidade", example = "10")
    private Integer empresa;

    @Schema(description = "Lista de tabelas de fidelidade associadas ao parceiro")
    private List<TabelaParceiroFidelidadeDTO> tabelas;

    @Schema(description = "Lista de produtos associados ao parceiro de fidelidade")
    private List<ProdutoParceiroFidelidadeDTO> produtos;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTipoParceiro() {
        return tipoParceiro;
    }

    public void setTipoParceiro(Integer tipoParceiro) {
        this.tipoParceiro = tipoParceiro;
    }

    public boolean isValidarCliente() {
        return validarCliente;
    }

    public void setValidarCliente(boolean validarCliente) {
        this.validarCliente = validarCliente;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getClientIdRedemption() {
        return clientIdRedemption;
    }

    public void setClientIdRedemption(String clientIdRedemption) {
        this.clientIdRedemption = clientIdRedemption;
    }

    public String getClientSecretRedemption() {
        return clientSecretRedemption;
    }

    public void setClientSecretRedemption(String clientSecretRedemption) {
        this.clientSecretRedemption = clientSecretRedemption;
    }

    public String getCodigoLoja() {
        return codigoLoja;
    }

    public void setCodigoLoja(String codigoLoja) {
        this.codigoLoja = codigoLoja;
    }

    public String getCodigoMaquina() {
        return codigoMaquina;
    }

    public void setCodigoMaquina(String codigoMaquina) {
        this.codigoMaquina = codigoMaquina;
    }

    public String getCodigoOferta() {
        return codigoOferta;
    }

    public void setCodigoOferta(String codigoOferta) {
        this.codigoOferta = codigoOferta;
    }

    public String getCodigoResgate() {
        return codigoResgate;
    }

    public void setCodigoResgate(String codigoResgate) {
        this.codigoResgate = codigoResgate;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getDataExpiracaoToken() {
        return dataExpiracaoToken;
    }

    public void setDataExpiracaoToken(Date dataExpiracaoToken) {
        this.dataExpiracaoToken = dataExpiracaoToken;
    }

    public boolean isAmbienteProducao() {
        return ambienteProducao;
    }

    public void setAmbienteProducao(boolean ambienteProducao) {
        this.ambienteProducao = ambienteProducao;
    }

    public boolean isParcelaVencidaGeraPonto() {
        return parcelaVencidaGeraPonto;
    }

    public void setParcelaVencidaGeraPonto(boolean parcelaVencidaGeraPonto) {
        this.parcelaVencidaGeraPonto = parcelaVencidaGeraPonto;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public List<TabelaParceiroFidelidadeDTO> getTabelas() {
        if (tabelas == null) {
            tabelas = new ArrayList<>();
        }
        return tabelas;
    }

    public void setTabelas(List<TabelaParceiroFidelidadeDTO> tabelas) {
        this.tabelas = tabelas;
    }

    public List<ProdutoParceiroFidelidadeDTO> getProdutos() {
        if (produtos == null) {
            produtos = new ArrayList<>();
        }
        return produtos;
    }

    public void setProdutos(List<ProdutoParceiroFidelidadeDTO> produtos) {
        this.produtos = produtos;
    }
}
