package com.pacto.adm.core.dto.enveloperesposta.parcelas.editarnegociacao;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import com.pacto.adm.core.dto.negociacao.ParcelasEditarNegociacaoNovo;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaParcelasEditarNegociacaoNovoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ParcelasEditarNegociacaoNovo content;

    public ParcelasEditarNegociacaoNovo getContent() {
        return content;
    }

    public void setContent(ParcelasEditarNegociacaoNovo content) {
        this.content = content;
    }


    public static final String atributos =
            "\"descricao\": \"Parcela 1 de 6\", "
                    + "\"cupom\": \"DESC10\", "
                    + "\"nrParcela\": 1, "
                    + "\"valorParcela\": 150.00";

    public final static String requestBody = "{" +atributos + "}";

    public final static String resposta = "{"
            + "\"content\": {"
            + atributos
            + "}}";
}
