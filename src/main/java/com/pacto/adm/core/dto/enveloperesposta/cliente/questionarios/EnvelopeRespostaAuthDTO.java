package com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios;

import com.pacto.adm.core.dto.auth.AuthDTO;
import com.pacto.adm.core.dto.indicacao.evento.EventoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaAuthDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private AuthDTO content;

    public AuthDTO getContent() {
        return content;
    }

    public void setContent(AuthDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 2312, "
                    + "\"username\": \"pacto\", "
                    + "\"senha\": \"abc1234\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";




}
