package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Plano Horário", description = "Informações sobre os horários associados ao plano, incluindo a descrição e a flexibilidade de uso.")
public class PlanoHorarioDTO {

    @Schema(description = "Código identificador do horário associado ao plano.", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição do horário associado ao plano, como faixa de horário ou turno.", example = "Segunda a Sexta, 08:00 - 12:00")
    private String descricao;

    @Schema(description = "Indica se o horário é livre, ou seja, sem restrições de tempo.", example = "true")
    private Boolean livre;

    public PlanoHorarioDTO() {

    }

    public PlanoHorarioDTO(Integer codigo, String descricao, Boolean livre) {
        this.codigo = codigo;
        this.livre = livre;
        this.descricao = descricao;
    }

    public Boolean getLivre() {
        return livre;
    }

    public void setLivre(Boolean livre) {
        this.livre = livre;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
