package com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios;

import com.pacto.adm.core.dto.QuestionarioClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListQuestionarioClienteDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<QuestionarioClienteDTO> content;

    public List<QuestionarioClienteDTO> getContent() {
        return content;
    }

    public void setContent(List<QuestionarioClienteDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaQuestionarioClienteDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
