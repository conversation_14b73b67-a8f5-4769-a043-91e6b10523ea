package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoKobanaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoKobanaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoKobanaDTO content;

    public ConfiguracaoIntegracaoKobanaDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoKobanaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 12345, "
                    + "\"ambiente\": 1, "
                    + "\"email\": \"<EMAIL>\", "
                    + "\"business_cnpj\": \"12345678000195\", "
                    + "\"nickname\": \"empresa_nickname\", "
                    + "\"business_legal_name\": \"Empresa Ltda\", "
                    + "\"api_access_token\": \"xyz12345token\", "
                    + "\"ativo\": true, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"id\": 789, "
                    + "\"created_At\": \"2022-04-15T14:30:00Z\"";

    public static final String atributosSemEmpresa =
            "\"codigo\": 12345, "
                    + "\"ambiente\": 1, "
                    + "\"email\": \"<EMAIL>\", "
                    + "\"business_cnpj\": \"12345678000195\", "
                    + "\"nickname\": \"empresa_nickname\", "
                    + "\"business_legal_name\": \"Empresa Ltda\", "
                    + "\"api_access_token\": \"xyz12345token\", "
                    + "\"ativo\": true, "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\", "
                    + "\"id\": 789, "
                    + "\"created_At\": \"2022-04-15T14:30:00Z\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
