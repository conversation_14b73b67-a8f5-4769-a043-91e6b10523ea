package com.pacto.adm.core.dto.objecao;

import com.pacto.adm.core.enumerador.objecaotipogrupo.ObjecaoTipoGrupoEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Objecao", description = "Informações sobre objeções registradas, incluindo o tipo de objeção e o comentário relacionado.")
public class ObjecaoDTO {

    @Schema(description = "Código único identificador da objeção.", example = "123")
    private Integer codigo;

    @Schema(description = "Indica se a objeção está ativa ou não.", example = "true")
    private Boolean ativo;

    @Schema(description = "Comentário relacionado à objeção.", example = "Cliente desistiu do plano devido ao preço.")
    private String comentario;

    @Schema(description = "Descrição detalhada da objeção.", example = "Cliente alegou que o valor é elevado para o serviço oferecido.")
    private String descricao;

    @Schema(description = "Grupo de classificação da objeção.", example = "Objeção Desistência")
    private String grupo;

    @Schema(description = "Tipo do grupo da objeção. Valores disponíveis: OB: Objeção, MD: Objeção desistência, OD: Objeção definitiva.", example = "MD")
    private ObjecaoTipoGrupoEnum tipoGrupo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public ObjecaoTipoGrupoEnum getTipoGrupo() {
        return tipoGrupo;
    }

    public void setTipoGrupo(ObjecaoTipoGrupoEnum tipoGrupo) {
        this.tipoGrupo = tipoGrupo;
    }
}
