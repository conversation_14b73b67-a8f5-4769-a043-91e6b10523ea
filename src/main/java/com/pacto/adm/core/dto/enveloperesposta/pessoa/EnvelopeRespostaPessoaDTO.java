package com.pacto.adm.core.dto.enveloperesposta.pessoa;

import com.pacto.adm.core.dto.PessoaDTO;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "Representação das respostas")
public class EnvelopeRespostaPessoaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PessoaDTO content;

    public PessoaDTO getContent() {
        return content;
    }

    public void setContent(PessoaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 12345, "
                    + "\"codCliente\": 67890, "
                    + "\"nome\": \"<PERSON>\", "
                    + "\"fotoKey\": \"foto12345.jpg\", "
                    + "\"urlFoto\": \"https://exemplo.com/fotos/foto12345.jpg\", "
                    + "\"cpf\": \"123.456.789-00\", "
                    + "\"categoria\": \"ALUNO\", "
                    + "\"situacao\": \"ATIVO\", "
                    + "\"situacaoContrato\": \"VIGENTE\", "
                    + "\"tipo\": \"PF\", "
                    + "\"tipoColaborador\": \"PROFESSOR\", "
                    + "\"tipoCompraCredito\": \"PLANO\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
