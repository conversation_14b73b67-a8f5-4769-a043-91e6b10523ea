package com.pacto.adm.core.dto.enveloperesposta.integracao.sesi;

import com.pacto.adm.core.dto.IntegracaoSesiDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios.EnvelopeRespostaEventoDTO;
import com.pacto.adm.core.dto.enveloperesposta.colaborador.EnvelopeRespostaColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.indicacao.IndicacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaIntegracaoSesiDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private IntegracaoSesiDTO content;

    public IntegracaoSesiDTO getContent() {
        return content;
    }

    public void setContent(IntegracaoSesiDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"messageID\": \"id01293018\", "
                    + "\"dataRequisicao\": 1746796800000, "
                    + "\"dataRetorno\": 1746796800000, "
                    + "\"xmlEnviado\": \"<xml><integracao>INFORMACOES DA INTEGRACAO</integracao> </xml>\", "
                    + "\"codigoEntidade\": \"2\", "
                    + "\"key_empresa\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\", "
                    + "\"funcionalidade\": \"INTEGRACAO_PIX\", "
                    + "\"dtRequisicao\": \"2025-05-09\", "
                    + "\"aluno\": \"Renato Alves Cariri\", "
                    + "\"tipoSolicitacao\": \"Integração SESI\", "
                    + "\"id\": \"1\", "
                    + "\"status\": \"EN\", "
                    + "\"statusDescricao\": \"Enviado\", "
                    + "\"dtRetorno\": \"2025-05-09\", "
                    + "\"resultado\": \"INTEGRADO\", "
                    + "\"solicitacao\": \"solicitação de integração PIX\", "
                    + "\"resposta\": \"Integrado\", "
                    + "\"codigoEmpresa\": 1, "
                    + "\"nomeEmpresa\": \"ACADEMIA PACTO\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";




}
