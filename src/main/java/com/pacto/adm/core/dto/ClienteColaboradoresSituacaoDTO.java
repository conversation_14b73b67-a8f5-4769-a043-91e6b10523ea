package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Cliente Colaboradores Situacao", description = "Informações da situação do cliente")
public class ClienteColaboradoresSituacaoDTO {

    @Schema(description = "Tipo de situação", example = "A")
    private String tipo;

    @Schema(description = "Situação do cliente", example = "ATIVO")
    private String situacao;
    @Schema(description = "Situação do contrato do cliente", example = "ATIVO")
    private String situacaoContrato;

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}
