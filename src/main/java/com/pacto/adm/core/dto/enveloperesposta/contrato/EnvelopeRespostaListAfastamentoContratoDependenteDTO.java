package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.AfastamentoContratoDependenteDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListAfastamentoContratoDependenteDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<AfastamentoContratoDependenteDTO> content;

    public List<AfastamentoContratoDependenteDTO> getContent() {
        return content;
    }

    public void setContent(List<AfastamentoContratoDependenteDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaAfastamentoContratoDependenteDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
