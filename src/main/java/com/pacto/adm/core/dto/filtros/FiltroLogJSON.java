package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class FiltroLogJSON {
    private String nomeEntidade;
    private Date dataAlteracaoInicial;
    private Date dataAlteracaoFinal;
    private String operacao;
    private String nomeEmpresa;
    private Integer codigoEmpresa;
    private List<String> nomesColaboradores;
    private String parametro;

    public FiltroLogJSON() {
    }

    public FiltroLogJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();

            if (filters.has("nomeEntidade"))
                this.nomeEntidade = filters.optString("nomeEntidade");
            if (filters.has("dataAlteracaoInicial"))
                this.dataAlteracaoInicial = Date.from(Instant.parse(filters.optString("dataAlteracaoInicial")));
            if (filters.has("dataAlteracaoFinal"))
                this.dataAlteracaoFinal = Date.from(Instant.parse(filters.optString("dataAlteracaoFinal")));
            if (filters.has("operacao"))
                this.operacao = filters.getString("operacao");
            if (filters.has("nomeEmpresa"))
                this.nomeEmpresa = filters.getString("nomeEmpresa");
            if (filters.has("codigoEmpresa"))
                this.codigoEmpresa = filters.getInt("codigoEmpresa");
            if (filters.has("nomesColaboradores")) {
                this.nomesColaboradores = filters.optJSONArray("nomesColaboradores").toList().stream().map(c -> (String) c).collect(Collectors.toList());
            }
        }
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public Date getDataAlteracaoInicial() {
        return dataAlteracaoInicial;
    }

    public void setDataAlteracaoInicial(Date dataAlteracaoInicial) {
        this.dataAlteracaoInicial = dataAlteracaoInicial;
    }

    public Date getDataAlteracaoFinal() {
        return dataAlteracaoFinal;
    }

    public void setDataAlteracaoFinal(Date dataAlteracaoFinal) {
        this.dataAlteracaoFinal = dataAlteracaoFinal;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public List<String> getNomesColaboradores() {
        return nomesColaboradores;
    }

    public void setNomesColaboradores(List<String> nomesColaboradores) {
        this.nomesColaboradores = nomesColaboradores;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }
}
