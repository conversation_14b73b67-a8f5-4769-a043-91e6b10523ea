package com.pacto.adm.core.dto.enveloperesposta.log;

import com.pacto.adm.core.dto.LogConciliadoraDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo os logs das conciliadoras")
public class EnvelopeRespostaListLogConciliadoraDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<LogConciliadoraDTO> content;

    public List<LogConciliadoraDTO> getContent() {
        return content;
    }

    public void setContent(List<LogConciliadoraDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaLogConciliadoraDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
