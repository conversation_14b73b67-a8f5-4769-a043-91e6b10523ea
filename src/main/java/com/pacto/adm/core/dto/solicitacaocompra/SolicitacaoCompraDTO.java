package com.pacto.adm.core.dto.solicitacaocompra;

import com.pacto.adm.core.dto.ArquivoDTO;
import com.pacto.adm.core.enumerador.SituacaoSolicitacaoCompraEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "SolicitacaoCompra", description = "Informações da solicitação de compra")
public class SolicitacaoCompraDTO {

    @Schema(description = "Código único identificador da solicitação de compra", example = "1")
    private Integer codigo;

    @Schema(description = "Título da solicitação de compra", example = "Compra de equipamentos de musculação")
    private String titulo;

    @Schema(description = "Data em que a solicitação foi criada", example = "1744122600000")
    private Date dataSolicitacao;

    @Schema(description = "Situação atual da solicitação de compra. \n\n" +
            "**Valores disponíveis**\n" +
            "- PENDENTE (Aguardando análise)\n" +
            "- APROVADO (Solicitação aprovada)\n" +
            "- NEGADO (Solicitação negada)\n", example = "PENDENTE")
    private SituacaoSolicitacaoCompraEnum situacao;

    @Schema(description = "Descrição detalhada da solicitação de compra", example = "Solicitação para compra de 5 esteiras ergométricas para a área de cardio")
    private String descricao;

    @Schema(description = "Lista de arquivos anexados à solicitação de compra")
    private List<ArquivoDTO> arquivos;

    @Schema(description = "Motivo da negação da solicitação, preenchido apenas quando a situação for NEGADO", example = "Orçamento insuficiente para o período")
    private String motivoNegacao;
}
