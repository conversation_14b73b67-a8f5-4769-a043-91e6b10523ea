package com.pacto.adm.core.dto.filtros;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FiltroLogClienteJSON {

    private String parametro;
    private List<String> tipo;
    private Date dataInicio;
    private Date dataFim;


    public FiltroLogClienteJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();

            JSONArray tipo = filters.optJSONArray("tipo");
            if (tipo != null) {
                this.tipo = new ArrayList<>();
                for (int i = 0; i < tipo.length(); i++) {
                    if (tipo.get(i).equals("INSERT")) {
                        this.tipo.add("INCLUSÃO");
                        this.tipo.add("INCLUSAO");
                        this.tipo.add("INSERÇÃO");
                        this.tipo.add("INSERCAO");
                        this.tipo.add("INCLUIR");
                    } else if (tipo.get(i).equals("UPDATE")) {
                        this.tipo.add("ALTERAÇÃO");
                        this.tipo.add("ALTERACAO");
                        this.tipo.add("ALTERAR");
                    } else if (tipo.get(i).equals("DELETE")) {
                        this.tipo.add("EXCLUSÃO");
                        this.tipo.add("EXCLUSAO");
                        this.tipo.add("EXCLUIR");
                    }
                }
            }
            long dataInicio = filters.optLong("dataInicio");
            if (dataInicio != 0) {
                setDataInicio(new Date(dataInicio));
            }
            long dataFim = filters.optLong("dataFim");
            if (dataFim != 0) {
                setDataFim(new Date(dataFim));
            }
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public List<String> getTipo() {
        return tipo;
    }

    public void setTipo(List<String> tipo) {
        this.tipo = tipo;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }
}
