package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.PeriodoAcessoClienteAdapter;
import com.pacto.adm.core.dao.interfaces.PeriodoAcessoClienteDao;
import com.pacto.adm.core.dto.AtestadoContratoDTO;
import com.pacto.adm.core.dto.PeriodoAcessoClienteDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.contrato.PeriodoAcessoCliente;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.PeriodoAcessoClienteService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Iterator;
import java.util.List;

@Service
public class PeriodoAcessoClienteServiceImpl implements PeriodoAcessoClienteService {

    @Autowired
    PeriodoAcessoClienteAdapter periodoAcessoClienteAdapter;
    @Autowired
    private PeriodoAcessoClienteDao periodoAcessoClienteDao;

    public static String getTiposQuePermiteAcesso() {
        StringBuilder sb = new StringBuilder();
        sb.append("'CA',");
        sb.append("'BO',");
        sb.append("'TO',");
        sb.append("'TD',");
        sb.append("'PL',");
        sb.append("'AA',");
        sb.append("'DI',");
        sb.append("'RT',");
        sb.append("'RR',");
        sb.append("'RA',");
        sb.append("'RC'");

        return sb.toString();
    }

    private Throwable getRootCause(Throwable throwable) {
        Throwable cause = throwable.getCause();
        if (cause != null) {
            return getRootCause(cause);
        }
        return throwable;
    }

    @Override
    public void inicializarDadosPeriodoAcessoClienteAtestadoContrato(AtestadoContratoDTO atestadoContratoDTO) throws Exception {
        List<PeriodoAcessoCliente> listaPeriodos = periodoAcessoClienteDao.consultarPorVigenteOuFuturoContrato(atestadoContratoDTO.getDataInicio(), atestadoContratoDTO.getContrato().getCodigo());
        for (PeriodoAcessoCliente periodoAcesso : listaPeriodos) {
            if (Uteis.getCompareData(periodoAcesso.getDataInicioAcesso(), atestadoContratoDTO.getDataInicio()) != 0) {
                periodoAcesso.setDataFinalAcesso(Uteis.obterDataAnterior(atestadoContratoDTO.getDataInicio(), 1));
            }
            periodoAcessoClienteDao.save(periodoAcesso);
        }
        gravarPeriodoAcessoAtestadoContrato(atestadoContratoDTO);
        inicializarPeriodoAcessoRetornoAtestadoContrato(atestadoContratoDTO);
    }

    public List<PeriodoAcessoClienteDTO> consultaPeriodoAcesso(Integer codPessoa, boolean gymPass, PaginadorDTO paginadorDTO) throws Exception {
        List<PeriodoAcessoClienteDTO> periodoAcessoClienteDTOS = periodoAcessoClienteAdapter.toDtos(periodoAcessoClienteDao.findByPessoa(codPessoa, gymPass, paginadorDTO));
        for (PeriodoAcessoClienteDTO periodoAcessoClienteDTO : periodoAcessoClienteDTOS) {
            periodoAcessoClienteDTO.setLegenda(getTiposQuePermiteAcesso().contains(periodoAcessoClienteDTO.getTipoAcesso()));
        }
        return periodoAcessoClienteDTOS;
    }

    public List<PeriodoAcessoClienteDTO> consultaPeriodoAcessoGoGood(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        List<PeriodoAcessoClienteDTO> periodoAcessoClienteDTOS = periodoAcessoClienteAdapter.toDtos(periodoAcessoClienteDao.findByPessoaGoGood(codPessoa, paginadorDTO));
        for (PeriodoAcessoClienteDTO periodoAcessoClienteDTO : periodoAcessoClienteDTOS) {
            periodoAcessoClienteDTO.setLegenda(getTiposQuePermiteAcesso().contains(periodoAcessoClienteDTO.getTipoAcesso()));
        }
        return periodoAcessoClienteDTOS;
    }

    public void gravarPeriodoAcessoAtestadoContrato(AtestadoContratoDTO atestadoContratoDTO) throws Exception {
        PeriodoAcessoCliente obj = new PeriodoAcessoCliente();
        obj.setPessoa(atestadoContratoDTO.getContrato().getPessoa());
        obj.setContrato(atestadoContratoDTO.getContrato().getCodigo());
        obj.setContratoBaseadoRenovacao(atestadoContratoDTO.getContrato().getContratoBaseadoRenovacao());
        obj.setTipoAcesso("AT");
        obj.setDataFinalAcesso(atestadoContratoDTO.getDataTermino());
        obj.setDataInicioAcesso(atestadoContratoDTO.getDataInicio());
        periodoAcessoClienteDao.save(obj);
    }

    public void inicializarPeriodoAcessoRetornoAtestadoContrato(AtestadoContratoDTO atestadoContratoDTO) throws Exception {
        PeriodoAcessoCliente novoPeriodoAcesso = new PeriodoAcessoCliente();
        novoPeriodoAcesso.setContrato(atestadoContratoDTO.getContrato().getCodigo());
        novoPeriodoAcesso.setContratoBaseadoRenovacao(atestadoContratoDTO.getContrato().getContratoBaseadoRenovacao());

        novoPeriodoAcesso.setDataFinalAcesso(atestadoContratoDTO.getDataTerminoRetorno());
        novoPeriodoAcesso.setDataInicioAcesso(atestadoContratoDTO.getDataInicioRetorno());

        novoPeriodoAcesso.setTipoAcesso("RA");
        novoPeriodoAcesso.setPessoa(atestadoContratoDTO.getContrato().getPessoa());
        if (!atestadoContratoDTO.getContrato().getSituacao().equals("IN")) {
            List<PeriodoAcessoCliente> periodosFuturos = periodoAcessoClienteDao.consultarPorDataInicioContrato(atestadoContratoDTO.getDataInicio(), atestadoContratoDTO.getContrato().getCodigo());
            if (!periodosFuturos.isEmpty()) {
                Iterator i = periodosFuturos.iterator();
                while (i.hasNext()) {
                    PeriodoAcessoCliente periodo = (PeriodoAcessoCliente) i.next();
                    periodoAcessoClienteDao.delete(periodo);
                }
            }
        }
        periodoAcessoClienteDao.save(novoPeriodoAcesso);
    }

    public PeriodoAcessoCliente save(PeriodoAcessoClienteDTO periodoAcessoClienteDTO) throws ServiceException {
        try {
            PeriodoAcessoCliente periodoAcessoCliente = periodoAcessoClienteAdapter.toEntity(periodoAcessoClienteDTO);
            return periodoAcessoClienteDao.save(periodoAcessoCliente);
        } catch (Exception ex) {
            throw new ServiceException(getRootCause(ex));
        }
    }

    public List<PeriodoAcessoClienteDTO> findNextFreepass(Cliente cliente, LocalDate localDate) throws ServiceException {
        try {
            List<PeriodoAcessoCliente> periodosFreepass = periodoAcessoClienteDao.findByClienteAndDataGreaterThanAndTipoPeriodoAcesso(cliente, Calendario.toDate(localDate), "PL");
            return periodoAcessoClienteAdapter.toDtos(periodosFreepass);
        } catch (Exception ex) {
            throw new ServiceException(getRootCause(ex));
        }
    }

    public void delete(PeriodoAcessoClienteDTO periodoFreepass) throws ServiceException {
        try {
            periodoAcessoClienteDao.delete(periodoFreepass.getCodigo());
        } catch (Exception ex) {
            throw new ServiceException(getRootCause(ex));
        }
    }
}
