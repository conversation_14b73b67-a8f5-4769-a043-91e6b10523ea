package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface DiscoveryService {

    ClientDiscoveryDataDTO getClientDiscovery() throws ServiceException;

    ClientDiscoveryDataDTO getClientDiscovery(String chave, String token) throws ServiceException;

    void clearCache();

    List<String> getDiscoveryCache();
}
