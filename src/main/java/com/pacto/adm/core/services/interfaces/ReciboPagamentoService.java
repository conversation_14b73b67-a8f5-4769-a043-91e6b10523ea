package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.ReciboPagamentoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.objects.EstornoRecibo;

import java.util.List;

public interface ReciboPagamentoService {

    List<ReciboPagamentoDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ReciboPagamentoDTO> findAllByCodParcela(Integer codParcela) throws ServiceException;

    void estornarNotasFiscais(EstornoRecibo estornoRecibo, Contrato contrato);
}
