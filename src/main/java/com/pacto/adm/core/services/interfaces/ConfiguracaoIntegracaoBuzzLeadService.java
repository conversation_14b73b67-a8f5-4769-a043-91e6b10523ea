package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoBuzzLeadDTO;
import com.pacto.config.exceptions.ServiceException;

public interface ConfiguracaoIntegracaoBuzzLeadService {

    public String getTokenBuzzLeadAcessoZw(Integer codigoEmpresa);

    public String getUrlWebHookBuzz();

    ConfiguracaoIntegracaoBuzzLeadDTO findByEmpresaId(Integer codigo) throws ServiceException;

    ConfiguracaoIntegracaoBuzzLeadDTO salvar(ConfiguracaoIntegracaoBuzzLeadDTO configuracaoIntegracaoBuzzLeadDTO) throws ServiceException;

}
