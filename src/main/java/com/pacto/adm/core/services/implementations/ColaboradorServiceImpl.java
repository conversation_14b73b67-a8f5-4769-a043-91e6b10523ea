package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.colaborador.ColaboradorAdapter;
import com.pacto.adm.core.dao.interfaces.ColaboradorDao;
import com.pacto.adm.core.dao.interfaces.colaborador.ColaboradorRepository;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.filtros.FiltroColaboradorJSON;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.enumerador.SituacaoColaboradorEnum;
import com.pacto.adm.core.enumerador.TipoColaboradorEnum;
import com.pacto.adm.core.services.interfaces.ColaboradorService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@Service
public class ColaboradorServiceImpl implements ColaboradorService {

    private final RequestService requestService;
    private final ColaboradorDao colaboradorDao;
    private final ColaboradorAdapter colaboradorAdapter;
    private final ColaboradorRepository colaboradorRepository;

    public ColaboradorServiceImpl(RequestService requestService, ColaboradorDao colaboradorDao, ColaboradorAdapter colaboradorAdapter, ColaboradorRepository colaboradorRepository) {
        this.requestService = requestService;
        this.colaboradorDao = colaboradorDao;
        this.colaboradorAdapter = colaboradorAdapter;
        this.colaboradorRepository = colaboradorRepository;
    }

    @Override
    public List<Colaborador> findAllColaboradoresAtivos() throws ServiceException {
        try {
            List<Colaborador> colaboradores = new ArrayList<>();

            try (SessionImplementor sessionImplementor = colaboradorDao.createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    PreparedStatement pst;
                    String sql = "SELECT c.codigo FROM Colaborador c JOIN TipoColaborador t on c.codigo = t.colaborador WHERE upper(t.descricao) LIKE ('CO%') AND c.situacao = 'AT'" + " AND c.empresa = " + requestService.getEmpresaId() + "  ORDER BY t.descricao";
                    try {
                        ResultSet rs = colaboradorDao.createStatement(connection, sql);
                        while (rs.next()) {
                            Colaborador colaborador = colaboradorDao.findById(rs.getInt("codigo"));
                            colaboradores.add(colaborador);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return colaboradores;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ColaboradorDTO> findAllConsultoresAtivos() throws ServiceException {
        try {
            return this.colaboradorAdapter.toDtos(colaboradorDao.findColaboradorByTipoColaboradorAndSituacao(
                    new TipoColaboradorEnum[]{TipoColaboradorEnum.CONSULTOR},
                    SituacaoColaboradorEnum.ATIVO,
                    this.requestService.getEmpresaId()
            ));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ColaboradorDTO> findAllProfessoresAtivos() throws ServiceException {
        try {
            return this.colaboradorAdapter.toDtos(colaboradorDao.findColaboradorByTipoColaboradorAndSituacao(
                    new TipoColaboradorEnum[]{TipoColaboradorEnum.PROFESSOR, TipoColaboradorEnum.PROFESSOR_TREINO},
                    SituacaoColaboradorEnum.ATIVO,
                    this.requestService.getEmpresaId()
            ));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public ColaboradorDTO findByCodigo(Integer codigo) throws ServiceException {
        try {
            return this.colaboradorAdapter.toDto(colaboradorDao.findById(codigo));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<String> tiposColaborador(Integer codigo, Integer usuario) throws ServiceException {
        try {
            return this.colaboradorDao.tiposDoColaborador(codigo, usuario);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ColaboradorDTO> findAllAtivosPorTipoVinculo(String tipoVinculo, boolean unpage, FiltroColaboradorJSON filtroColaboradorJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            if (filtroColaboradorJSON.getEmpresa() == null) {
                filtroColaboradorJSON.setEmpresa(requestService.getEmpresaId());
            }
            if (!unpage) {
                Pageable pageable = PageRequest.of(0, 10);
                if (paginadorDTO != null) {
                    if (paginadorDTO.getPage() == null) {
                        paginadorDTO.setPage(0L);
                    }
                    if (paginadorDTO.getSize() == null) {
                        paginadorDTO.setSize(10L);
                    }
                    pageable = PageRequest.of(paginadorDTO.getPage().intValue(), pageable.getPageSize());
                }
                Page<Colaborador> colaboradorPage = colaboradorRepository.findAllPageablePorSituacaoAndVinculoAndEmpresa(
                        "AT",
                        tipoVinculo,
                        requestService.getEmpresaId(),
                        filtroColaboradorJSON.getParametro(),
                        pageable
                );
                if (paginadorDTO != null) {
                    paginadorDTO.setPage((long) colaboradorPage.getNumber());
                    paginadorDTO.setSize((long) colaboradorPage.getSize());
                    paginadorDTO.setQuantidadeTotalElementos(colaboradorPage.getTotalElements());
                }
                return colaboradorAdapter.toDtos(colaboradorPage.getContent());
            }
            return colaboradorAdapter.toDtos(colaboradorRepository.findAllPorSituacaoAndVinculoAndEmpresa(
                    "AT",
                    tipoVinculo,
                    filtroColaboradorJSON.getEmpresa(),
                    filtroColaboradorJSON.getParametro()
            ));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
