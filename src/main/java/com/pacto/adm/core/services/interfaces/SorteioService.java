package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.SorteioDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroSorteioJSON;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface SorteioService {

    SorteioDTO findById(Integer id) throws ServiceException;

    List<SorteioDTO> findAll(FiltroSorteioJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    SorteioDTO realizarSorteio() throws ServiceException;

    SorteioDTO validarResultado(SorteioDTO sorteioDTO) throws ServiceException;

    public SorteioDTO saveOrUpdate(SorteioDTO sorteioDTO) throws ServiceException;
}
