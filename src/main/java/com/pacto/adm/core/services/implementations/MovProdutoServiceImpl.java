package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.MovParcelaAdapter;
import com.pacto.adm.core.adapters.MovProdutoAdapter;
import com.pacto.adm.core.adapters.PagamentoMovParcelaAdapter;
import com.pacto.adm.core.adapters.ReciboPagamentoAdapter;
import com.pacto.adm.core.dao.interfaces.MovProdutoDao;
import com.pacto.adm.core.dao.interfaces.MovProdutoParcelaDao;
import com.pacto.adm.core.dao.interfaces.PagamentoMovParcelaDao;
import com.pacto.adm.core.dao.interfaces.VendaAvulsaDao;
import com.pacto.adm.core.dao.interfaces.nativerepositories.movproduto.MovProdutoNativeRepository;
import com.pacto.adm.core.dto.AplicacaoDescontoTotaisDTO;
import com.pacto.adm.core.dto.AulaAvulsaDiariaDTO;
import com.pacto.adm.core.dto.MovProdutoDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroMovProdutoJSON;
import com.pacto.adm.core.entities.MovParcela;
import com.pacto.adm.core.entities.MovProduto;
import com.pacto.adm.core.entities.MovProdutoParcela;
import com.pacto.adm.core.entities.financeiro.Boleto;
import com.pacto.adm.core.entities.financeiro.VendaAvulsa;
import com.pacto.adm.core.objects.EstornoMovProduto;
import com.pacto.adm.core.objects.EstornoRecibo;
import com.pacto.adm.core.services.interfaces.BoletoService;
import com.pacto.adm.core.services.interfaces.MovProdutoService;
import com.pacto.adm.core.services.interfaces.ReciboPagamentoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


@Service
public class MovProdutoServiceImpl implements MovProdutoService {

    @Autowired
    private MovProdutoDao movProdutoDao;
    @Autowired
    private MovProdutoParcelaDao movProdutoParcelaDao;

    @Autowired
    private PagamentoMovParcelaDao pagamentoMovParcelaDao;
    @Autowired
    private MovParcelaAdapter movParcelaAdapter;
    @Autowired
    private PagamentoMovParcelaAdapter pagamentoMovParcelaAdapter;
    @Autowired
    private VendaAvulsaDao vendaAvulsaDao;
    @Autowired
    private ReciboPagamentoService reciboPagamentoService;
    @Autowired
    private BoletoService boletoService;
    @Autowired
    private MovProdutoAdapter movProdutoAdapter;

    @Autowired
    private ReciboPagamentoAdapter reciboPagamentoAdapter;

    private Logger logger = Logger.getLogger(MovProdutoServiceImpl.class.getName());
    @Autowired
    private MovProdutoNativeRepository movProdutoNativeRepository;

    public List<MovProdutoDTO> findAllByCodPessoa(Integer codPessoa, FiltroMovProdutoJSON filtroMovProdutoJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovProduto> movProdutos = movProdutoDao.findAllByPessoa(codPessoa, filtroMovProdutoJSON, paginadorDTO);
            return movProdutoAdapter.toDtos(movProdutos);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public List<MovProdutoDTO> findAllByCodContrato(Integer contrato, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovProduto> movProdutos = movProdutoDao.consultarPorCodigoContrato(contrato);

            List<MovProdutoDTO> movProdutoDTOS = movProdutoAdapter.toDtos(movProdutos);

            if (paginadorDTO != null
                    && paginadorDTO.getPage() != null
                    && paginadorDTO.getSize() != null) {
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                if (ultimoRegistro > movProdutoDTOS.size()) {
                    movProdutoDTOS = movProdutoDTOS.subList(primeiroPaginacao, movProdutoDTOS.size());
                } else {
                    movProdutoDTOS = movProdutoDTOS.subList(primeiroPaginacao, ultimoRegistro);
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) movProdutos.size());
            }
            return movProdutoDTOS;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<MovProdutoDTO> findAllByCodRecibo(Integer codRecibo) throws ServiceException {
        try {
            List<MovProdutoParcela> movProdutoParcelas = movProdutoParcelaDao.findAllByRecibo(codRecibo);

            Map<Integer, MovProduto> mapaProdutosRecibo = new HashMap<>();
            for (MovProdutoParcela mpp : movProdutoParcelas) {
                MovProduto mprod = mapaProdutosRecibo.get(mpp.getMovProduto().getCodigo());
                if (mprod == null) {
                    mprod = mpp.getMovProduto();
                    mapaProdutosRecibo.put(mprod.getCodigo(), mprod);
                }
            }

            return movProdutoAdapter.toDtos(new ArrayList<>(mapaProdutosRecibo.values()));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    public List<MovProdutoDTO> findAllByCodParcela(Integer codParcela, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovProdutoParcela> movProdutoParcelas = movProdutoParcelaDao.findAllByParcela(codParcela);

            Map<Integer, MovProduto> mapaProdutosRecibo = new HashMap<>();
            for (MovProdutoParcela mpp : movProdutoParcelas) {
                MovProduto mprod = mapaProdutosRecibo.get(mpp.getMovProduto().getCodigo());
                if (mprod == null) {
                    mprod = mpp.getMovProduto();
                    mapaProdutosRecibo.put(mprod.getCodigo(), mprod);
                }
            }

            List<MovProdutoDTO> movProdutoDTOS = movProdutoAdapter.toDtos(new ArrayList<>(mapaProdutosRecibo.values()));
            Integer total = movProdutoDTOS.size();

            if (paginadorDTO != null
                    && paginadorDTO.getPage() != null
                    && paginadorDTO.getSize() != null) {
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                if (ultimoRegistro > movProdutoDTOS.size()) {
                    movProdutoDTOS = movProdutoDTOS.subList(primeiroPaginacao, movProdutoDTOS.size());
                } else {
                    movProdutoDTOS = movProdutoDTOS.subList(primeiroPaginacao, ultimoRegistro);
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) total);
            }
            return movProdutoDTOS;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void estornarMovProduto(EstornoMovProduto obj, MovParcela parcela, String key) throws Exception {
        List<Boleto> listaBoletosCancelar = new ArrayList<>();
        try {
            estornarNotasFiscais(obj);
            listaBoletosCancelar = boletoService.excluirBoletoMovProduto(obj);

            // TODO (lucasaraujo) implementar metodos para estornar remessas, transações e gerar logs
//            List<LogVO> listaLog = obj.estornarMovProduto(key, obj.isEstornarOperadora(), con);
            if (!obj.getExiteOutroContratoPagouMinhaParcela()) {
                MovProduto movProduto = new MovProduto();
                for (MovProduto produto : obj.getListaMovProduto()) {
                    movProdutoDao.delete(produto);
                }
            }

            if (!obj.getExiteOutroContratoPagouMinhaParcela()) {
                if (parcela.getVendaAvulsa().getCodigo() != 0) {
                    VendaAvulsa vendaAvulsa = new VendaAvulsa();
                    vendaAvulsa.setCodigo(parcela.getVendaAvulsa().getCodigo());
                    // TODO implementar metodos para estornar agenda estudio
//                    AgendaEstudio agendaEstudioDAO = new AgendaEstudio(con);
//                    agendaEstudioDAO.estornarAgendasPorVendaAvulsa(vendaAvulsa.getCodigo(), false);
//                    agendaEstudioDAO = null;
                    vendaAvulsaDao.delete(vendaAvulsa);
                }
//                else if (parcela.getPersonal().getCodigo() != 0) {
//                    ControleTaxaPersonal controleTaxaPersonalDAO = new ControleTaxaPersonal(con);
//                    controleTaxaPersonalDAO.excluirSemCommit(parcela.getPersonal().getCodigo());
//                    controleTaxaPersonalDAO = null;
//                }
            }
            // TODO (lucasaraujo) Implementar registro de log dessa parte
//            try {
//                if (UteisValidacao.emptyNumber(obj.getClienteVO().getPessoa().getCodigo())) {
//                    Colaborador colaboradorDAO = new Colaborador(con);
//                    Integer codigoColaborador = colaboradorDAO.consultarPorCodigoPessoa(obj.getMovProdutoVO().getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS).getCodigo();
//                    colaboradorDAO = null;
//                    Iterator i = listaLog.iterator();
//                    while (i.hasNext()) {
//                        LogVO log = (LogVO) i.next();
//                        if (log.getChavePrimaria().equals("0")) {
//                            log.setChavePrimaria(codigoColaborador.toString());
//                            log.setNomeEntidade("COLABORADOR");
//                        }
//                    }
//                    SuperControle.registrarLogObjetoVO(listaLog, obj.getMovProdutoVO().getPessoa().getCodigo());
//                } else {
//                    SuperControle.registrarLogObjetoVO(listaLog, obj.getClienteVO().getPessoa().getCodigo());
//                }
//            } catch (Exception e) {
//                SuperControle.registrarLogErroObjetoVO("ESTORNOMOVPRODUTO", obj.getClienteVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ESTORNO MOV PRODUTO", obj.getUsuarioVO().getNome(), obj.getUsuarioVO().getUserOamd());
//                e.printStackTrace();
//            }
        } catch (Exception e) {
            listaBoletosCancelar = new ArrayList<>();
            throw e;
        } finally {
            if (!UteisValidacao.emptyList(listaBoletosCancelar)) {
                // TODO (lucasaraujo) Implementar metodos cancearBoletos
//                try {
//                    boletoDAO.cancelarBoletos(listaBoletosCancelar, obj.getResponsavelEstorno(), "EstornoMovProduto");
//                } catch (Exception ex) {
//                    ex.printStackTrace();
//                }
            }
        }
    }

    @Override
    public MovProdutoDTO details(Integer codigoMovProduto) throws ServiceException {
        try {
            MovProdutoDTO movProdutoDTO = movProdutoAdapter.toDto(movProdutoDao.findById(codigoMovProduto));

            List<MovProdutoParcela> movProdutoParcelas = movProdutoParcelaDao.findByMovProduto(codigoMovProduto);
            movProdutoDTO.setParcelas(movProdutoParcelas.stream()
                    .map(movProdutoParcela -> movParcelaAdapter.toDto(movProdutoParcela.getMovParcela()))
                    .collect(Collectors.toList()));
            movProdutoDTO.setRecibos(new ArrayList<>());

            for (MovProdutoParcela movProdutoParcela : movProdutoParcelas) {
                boolean reciboIncluido = movProdutoDTO.getRecibos()
                        .stream()
                        .filter(reciboPagamentoDTO -> reciboPagamentoDTO.getCodigo().equals(movProdutoParcela.getReciboPagamento().getCodigo()))
                        .findFirst()
                        .isPresent();
                if (!reciboIncluido) {
                    movProdutoDTO.getRecibos().add(reciboPagamentoAdapter.toDto(movProdutoParcela.getReciboPagamento()));
                }
            }

            return movProdutoDTO;
        } catch (Exception e) {
            String msg = "Falha ao consultar detalhes do produto";
            logger.log(Level.SEVERE, msg, e);
            throw new ServiceException(msg);
        }
    }

    private void estornarNotasFiscais(EstornoMovProduto estornoMovProduto) throws Exception {
        for (EstornoRecibo estornoReciboVO : estornoMovProduto.getListaEstornoRecibo()) {
            // TODO (lucasaraujo) Implementar metodos para estornar notas vinculas
            reciboPagamentoService.estornarNotasFiscais(estornoReciboVO, null);
        }
    }

    @Override
    public List<MovProdutoDTO> consultarProdutoComValidadePorCodigoPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            return this.movProdutoAdapter.toDtos(
                    this.movProdutoDao.consultarProdutoComValidadePorCodigoPessoa(codPessoa, paginadorDTO)
            );
        }catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<MovProdutoDTO> consultarMovProdutosPorCodigoPessoa(Integer codPessoa, FiltroMovProdutoJSON filtroMovProdutoJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<MovProduto> movProdutos = movProdutoDao.consultarMovProdutosPorCodigoPessoa(codPessoa, null, filtroMovProdutoJSON, paginadorDTO);
            List<MovProdutoDTO> dtos = movProdutoAdapter.toDtos(movProdutos);
            if (dtos != null && !dtos.isEmpty()) {
                dtos.forEach(dto -> {
                    try {
                        Integer codAulaAvulsaDiaria = movProdutoDao.consultarPorCodigoMovProdutoRetornaAulaAvulsaDiaria(dto.getCodigo());
                        if (!UteisValidacao.emptyNumber(codAulaAvulsaDiaria)) {
                            dto.setAulaAvulsaDiaria(new AulaAvulsaDiariaDTO());
                            dto.getAulaAvulsaDiaria().setCodigo(codAulaAvulsaDiaria);
                        }
                    } catch (Exception ex) {
                        Uteis.logar(ex, MovProdutoServiceImpl.class);
                    }
                });
            }
            return dtos;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public AplicacaoDescontoTotaisDTO findAllAplicacaoDesconto(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        if (filtroBIControleOperacoesJSON.getInicio() == null) {
            throw new ServiceException("filtro-data-inicio-nao-informado", "Deve ser informado o filtro data início para consulta!");
        }

        if (filtroBIControleOperacoesJSON.getFim() == null) {
            throw new ServiceException("filtro-data-fim-nao-informado", "Deve ser informado o filtro data fim para consulta!");
        }

        try {
            return movProdutoNativeRepository.findAllAplicacaoDesconto(filtroBIControleOperacoesJSON, paginadorDTO);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(
                    "erro-consultar-aplicacao-desconto",
                    "Ocorreu um erro ao consultar as aplicações de descontos"
            );
        }

    }
}
