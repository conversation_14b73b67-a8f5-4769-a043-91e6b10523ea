package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.empresa.ConfiguracaoEmpresaRDStationAdapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoEmpresaRDStationDao;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaRDStationDTO;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaRDStation;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoEmpresaRDStationService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Locale;

@Service
public class ConfiguracaoEmpresaRDStationServiceImpl implements ConfiguracaoEmpresaRDStationService {

    @Autowired
    private DiscoveryService discoveryService;
    @Value("${integracao.rdstation.urlbase}")
    private String urlBaseRdStation;
    @Value("${integracao.rdstation.clienteid}")
    private String clientIdRdStation;

    private final ConfiguracaoEmpresaRDStationDao configuracaoEmpresaRDStationDao;
    private final ConfiguracaoEmpresaRDStationAdapter configuracaoEmpresaRDStationAdapter;
    private final HttpServico httpServico;
    private final RequestService requestService;
    private final MessageSource messageSource;

    public ConfiguracaoEmpresaRDStationServiceImpl(ConfiguracaoEmpresaRDStationDao configuracaoEmpresaRDStationDao, ConfiguracaoEmpresaRDStationAdapter configuracaoEmpresaRDStationAdapter, HttpServico httpServico, RequestService requestService, MessageSource messageSource) {
        this.configuracaoEmpresaRDStationDao = configuracaoEmpresaRDStationDao;
        this.configuracaoEmpresaRDStationAdapter = configuracaoEmpresaRDStationAdapter;
        this.httpServico = httpServico;
        this.requestService = requestService;
        this.messageSource = messageSource;
    }

    @Override
    public ConfiguracaoEmpresaRDStationDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException {
        ConfiguracaoEmpresaRDStation configuracaoEmpresaRDStation = configuracaoEmpresaRDStationDao.findByEmpresaId(codigoEmpresa);
        ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO;
        if (!UteisValidacao.emptyNumber(configuracaoEmpresaRDStation.getCodigo())) {
            configuracaoEmpresaRDStationDTO = configuracaoEmpresaRDStationAdapter.toDto(configuracaoEmpresaRDStation);
        } else {
            configuracaoEmpresaRDStationDTO = new ConfiguracaoEmpresaRDStationDTO();
        }
        configuracaoEmpresaRDStationDTO.setApiV13Link(getUrlApiLeadOldRD(codigoEmpresa));
        configuracaoEmpresaRDStationDTO.setUrlWebHookRd(getUrlWebHookRd(requestService.getUsuarioAtual().getChave(), codigoEmpresa));
        configuracaoEmpresaRDStationDTO.setEmpresa(new EmpresaDTO());
        configuracaoEmpresaRDStationDTO.getEmpresa().setCodigo(codigoEmpresa);
        return configuracaoEmpresaRDStationDTO;
    }

    @Override
    public ConfiguracaoEmpresaRDStationDTO salvarConfiguracaoEmpresaRDStation(ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO) throws ServiceException {
        validarCampos(configuracaoEmpresaRDStationDTO);
        try {
            configuracaoEmpresaRDStationDao.getCurrentSession().clear();
            ConfiguracaoEmpresaRDStation configuracaoEmpresaRDStation;
            if (Uteis.intNullOrEmpty(configuracaoEmpresaRDStationDTO.getCodigo())) {
                configuracaoEmpresaRDStation = configuracaoEmpresaRDStationAdapter.toEntity(configuracaoEmpresaRDStationDTO);
                configuracaoEmpresaRDStation = configuracaoEmpresaRDStationDao.save(configuracaoEmpresaRDStation);
            } else {
                configuracaoEmpresaRDStation = configuracaoEmpresaRDStationAdapter.toEntity(configuracaoEmpresaRDStationDTO);
                configuracaoEmpresaRDStation = configuracaoEmpresaRDStationDao.update(configuracaoEmpresaRDStation);
            }
            ConfiguracaoEmpresaRDStationDTO dtoRetornar = configuracaoEmpresaRDStationAdapter.toDto(configuracaoEmpresaRDStation);
            return dtoRetornar;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public ConfiguracaoEmpresaRDStationDTO aprovar(ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO) throws ServiceException {
        try {
            ConfiguracaoEmpresaRDStationDTO dtoRetornar = salvarConfiguracaoEmpresaRDStation(configuracaoEmpresaRDStationDTO);
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
            String urlBaseApi = clientDiscoveryDataDTO.getServiceUrls().getApiZwUrl().replaceAll("/prest", "");
            String urlApiPovoarParansRD = urlBaseApi + "/prest/lead/povoarParansRD";
            JSONObject params = new JSONObject();
            params.put("key", requestService.getUsuarioAtual().getChave());
            params.put("empresa", dtoRetornar.getEmpresa().getCodigo());
            params.put("idEvent", dtoRetornar.getEventWeebHook());
            params.put("expiration", String.valueOf(new Date().getTime() + 300000)); // 5 minutos.

            ResponseEntity<String> resposta = httpServico.doJson(urlApiPovoarParansRD, params.toString(), HttpMethod.POST, new HttpHeaders());

            if (resposta.getBody().equals("OK")) {
                StringBuilder url = new StringBuilder();
                url.append(urlBaseRdStation);
                url.append("?client_id=" + clientIdRdStation);
                url.append("&redirect_url=" + urlBaseApi + "/prest/lead/oauth2redirect");
                dtoRetornar.setUrlRdStation(url.toString());
            } else if (resposta.getBody().equals("FAIL")) {
                throw new ServiceException(messageSource.getMessage("integracao.rdstation.limite.requisicoes.atingido", null, new Locale(requestService.getLocale())));
            }
            return dtoRetornar;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void validarCampos(ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO) throws ServiceException {
        if (configuracaoEmpresaRDStationDTO.getEmpresa() == null || UteisValidacao.emptyNumber(configuracaoEmpresaRDStationDTO.getEmpresa().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.rdstation.empresa.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configuracaoEmpresaRDStationDTO.getResponsavelPadrao() == null || UteisValidacao.emptyNumber(configuracaoEmpresaRDStationDTO.getResponsavelPadrao().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.rdstation.responsavel.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nullOrEmpty(configuracaoEmpresaRDStationDTO.getHoraLimite())) {
            throw new ServiceException(messageSource.getMessage("integracao.rdstation.hora.limite.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configuracaoEmpresaRDStationDTO.getAcaoObjecao() == null) {
            throw new ServiceException(messageSource.getMessage("integracao.rdstation.acao.objecao.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nullOrEmpty(configuracaoEmpresaRDStationDTO.getEventWeebHook())) {
            throw new ServiceException(messageSource.getMessage("integracao.rdstation.gatilho.nao.informado", null, new Locale(requestService.getLocale())));
        }
    }

    public String getUrlApiLeadOldRD(Integer codigoEmpresa) throws ServiceException {
        String urlApi = discoveryService.getClientDiscovery().getServiceUrls().getApiZwUrl().replaceAll("/prest", "");
        String urlBase = urlApi + "/prest/lead/";
        String chaveZW = requestService.getUsuarioAtual().getChave();
        return urlBase + chaveZW + "/" + codigoEmpresa + "/cadastrarLeadRD";
    }

    public String getUrlWebHookRd(String chave, Integer codigoEmpresa) throws ServiceException {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();

            String urlBase = clientDiscoveryDataDTO.getServiceUrls().getZwUrl() + "/prest/lead";
            String params = "/v2/addLeadWebhook";
            return urlBase + "/" + chave + "/" + codigoEmpresa + params;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
}
