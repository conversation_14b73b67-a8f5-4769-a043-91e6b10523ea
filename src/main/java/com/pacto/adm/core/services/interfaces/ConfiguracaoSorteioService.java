package com.pacto.adm.core.services.interfaces;


import com.pacto.adm.core.dto.ConfiguracaoSorteioDTO;
import com.pacto.config.exceptions.ServiceException;

public interface ConfiguracaoSorteioService {

    ConfiguracaoSorteioDTO findByEmpresaId(Integer empresaId) throws ServiceException;

    ConfiguracaoSorteioDTO saveOrUpdate(ConfiguracaoSorteioDTO sorteioDTO) throws ServiceException;

    String getRegras() throws  ServiceException;

}
