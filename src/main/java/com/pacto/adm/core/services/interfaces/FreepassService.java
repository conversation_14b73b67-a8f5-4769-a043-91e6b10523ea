package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.FreepassDTO;
import com.pacto.adm.core.dto.LogDTO;
import com.pacto.adm.core.dto.PeriodoAcessoClienteDTO;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface FreepassService {
    PeriodoAcessoClienteDTO gravarFreepass(FreepassDTO freepassDTO) throws ServiceException;

    boolean removerFreepass(Integer codCliente) throws ServiceException;

    PeriodoAcessoClienteDTO findByCliente(Integer cliente) throws ServiceException;

    List<LogDTO> buscarLogs(FiltroLogClienteJSON filtros, PaginadorDTO paginador) throws ServiceException;

}
