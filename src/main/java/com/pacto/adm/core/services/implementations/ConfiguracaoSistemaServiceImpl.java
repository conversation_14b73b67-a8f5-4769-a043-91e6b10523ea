package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ConfiguracaoSistemaAdapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoSistemaDao;
import com.pacto.adm.core.dto.ConfiguracaoSistemaDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.ConfiguracaoSistemaService;
import org.springframework.stereotype.Service;

@Service
public class ConfiguracaoSistemaServiceImpl implements ConfiguracaoSistemaService {

    private final ConfiguracaoSistemaDao configuracaoSistemaDao;
    private final ConfiguracaoSistemaAdapter configuracaoSistemaAdapter;

    public ConfiguracaoSistemaServiceImpl(ConfiguracaoSistemaDao configuracaoSistemaDao, ConfiguracaoSistemaAdapter configuracaoSistemaAdapter) {
        this.configuracaoSistemaDao = configuracaoSistemaDao;
        this.configuracaoSistemaAdapter = configuracaoSistemaAdapter;
    }

    @Override
    public ConfiguracaoSistemaDTO get() throws ServiceException {
        try {
            return configuracaoSistemaAdapter.toDto(configuracaoSistemaDao.get());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
