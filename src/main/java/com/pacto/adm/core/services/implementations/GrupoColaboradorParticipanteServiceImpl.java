package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.GrupoColaboradorParticipanteRepository;
import com.pacto.adm.core.dao.interfaces.UsuarioRepository;
import com.pacto.adm.core.dao.interfaces.colaborador.ColaboradorRepository;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.entities.GrupoColaboradorParticipante;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.services.interfaces.GrupoColaboradorParticipanteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class GrupoColaboradorParticipanteServiceImpl implements GrupoColaboradorParticipanteService {

    private final GrupoColaboradorParticipanteRepository grupoColaboradorParticipanteRepository;
    private final UsuarioRepository usuarioRepository;
    private final ColaboradorRepository colaboradorRepository;

    public GrupoColaboradorParticipanteServiceImpl(GrupoColaboradorParticipanteRepository grupoColaboradorParticipanteRepository, UsuarioRepository usuarioRepository, ColaboradorRepository colaboradorRepository) {
        this.grupoColaboradorParticipanteRepository = grupoColaboradorParticipanteRepository;
        this.usuarioRepository = usuarioRepository;
        this.colaboradorRepository = colaboradorRepository;
    }

    @Override
    @Transactional
    public List<GrupoColaboradorParticipante> consultarUsuariosSemGrupo(Integer empresa) {
        List<GrupoColaboradorParticipante> grupoColaboradorParticipantes = new ArrayList<>();
        List<Integer> codigoColaboradoresSemGrupo = grupoColaboradorParticipanteRepository.consultarUsuariosSemGrupo(empresa);

        List<Colaborador> colaboradores = colaboradorRepository.findByCodigoIn(codigoColaboradoresSemGrupo);
        List<Usuario> usuarios = usuarioRepository.consultarPorColaboresEmpresa(codigoColaboradoresSemGrupo, empresa);

        codigoColaboradoresSemGrupo.forEach(codigoColaborador -> {
            GrupoColaboradorParticipante grupoColaboradorParticipante = new GrupoColaboradorParticipante();
            grupoColaboradorParticipante.setCodigo(codigoColaborador);
            grupoColaboradorParticipante.setColaboradorParticipante(colaboradores.stream().filter(c -> c.getCodigo().equals(codigoColaborador)).findFirst().orElse(null));
            grupoColaboradorParticipante.setUsuarioParticipante(usuarios.stream().filter(u -> u.getColaborador().getCodigo().equals(codigoColaborador)).findFirst().orElse(null));
            grupoColaboradorParticipantes.add(grupoColaboradorParticipante);
        });

        return grupoColaboradorParticipantes;
    }
}
