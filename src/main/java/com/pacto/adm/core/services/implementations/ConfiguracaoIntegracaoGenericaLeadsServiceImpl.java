package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoGenericaLeadsAdapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoGenericaLeadsDao;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGenericaLeadsDTO;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoGenericaLeads;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoGenericaLeadsService;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Locale;

@Service
public class ConfiguracaoIntegracaoGenericaLeadsServiceImpl implements ConfiguracaoIntegracaoGenericaLeadsService {

    private final DiscoveryService discoveryService;
    private final MessageSource messageSource;
    private final RequestService requestService;

    private final ConfiguracaoIntegracaoGenericaLeadsDao configDao;
    private final ConfiguracaoIntegracaoGenericaLeadsAdapter configAdapter;

    public ConfiguracaoIntegracaoGenericaLeadsServiceImpl(DiscoveryService discoveryService, MessageSource messageSource, RequestService requestService, ConfiguracaoIntegracaoGenericaLeadsDao configDao, ConfiguracaoIntegracaoGenericaLeadsAdapter configAdapter) {
        this.discoveryService = discoveryService;
        this.messageSource = messageSource;
        this.requestService = requestService;
        this.configDao = configDao;
        this.configAdapter = configAdapter;
    }

    public String getUrlWebHookGenerico(String chave, Integer codigoEmpresa) throws ServiceException {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();

            String urlBase = clientDiscoveryDataDTO.getServiceUrls().getApiZwUrl() + "/lead";
            String rota = "/v2/addLead";
            return urlBase + "/" + chave + "/" + codigoEmpresa + rota;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
    @Override
    public ConfiguracaoIntegracaoGenericaLeadsDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException {
        ConfiguracaoIntegracaoGenericaLeads configEntity = configDao.findByEmpresaId(codigoEmpresa);
        ConfiguracaoIntegracaoGenericaLeadsDTO configDTO;
        if (!UteisValidacao.emptyNumber(configEntity.getCodigo())) {
            configDTO = configAdapter.toDto(configEntity);
            configDTO.setUrlWebhookGenerico(getUrlWebHookGenerico(requestService.getUsuarioAtual().getChave(), codigoEmpresa));
        } else {
            configDTO = new ConfiguracaoIntegracaoGenericaLeadsDTO();
            configDTO.setEmpresa(new EmpresaDTO());
            configDTO.getEmpresa().setCodigo(codigoEmpresa);
            configDTO.setUrlWebhookGenerico(getUrlWebHookGenerico(requestService.getUsuarioAtual().getChave(), codigoEmpresa));
        }
        return configDTO;
    }

    @Override
    public ConfiguracaoIntegracaoGenericaLeadsDTO salvar(ConfiguracaoIntegracaoGenericaLeadsDTO configDTO) throws ServiceException {
        validarCampos(configDTO);
        try {
            configDao.getCurrentSession().clear();
            ConfiguracaoIntegracaoGenericaLeads configEntity;
            if (Uteis.intNullOrEmpty(configDTO.getCodigo())) {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.save(configEntity);
            } else {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.update(configEntity);
            }
            ConfiguracaoIntegracaoGenericaLeadsDTO dtoRetornar = configAdapter.toDto(configEntity);
            return dtoRetornar;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void validarCampos(ConfiguracaoIntegracaoGenericaLeadsDTO configDTO) throws ServiceException {
        if (configDTO.getEmpresa() == null || UteisValidacao.emptyNumber(configDTO.getEmpresa().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.genericaleads.empresa.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configDTO.getResponsavelPadrao() == null || UteisValidacao.emptyNumber(configDTO.getResponsavelPadrao().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.genericaleads.responsavel.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nullOrEmpty(configDTO.getHoraLimite())) {
            throw new ServiceException(messageSource.getMessage("integracao.genericaleads.hora.limite.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configDTO.getAcaoObjecao() == null) {
            throw new ServiceException(messageSource.getMessage("integracao.genericaleads.acao.objecao.nao.informado", null, new Locale(requestService.getLocale())));
        }
    }

}
