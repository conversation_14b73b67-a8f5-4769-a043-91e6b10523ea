package com.pacto.adm.core.services.implementations.conveniocobranca;

import com.pacto.adm.core.dao.interfaces.conveniocobranca.ConvenioCobrancaRepository;
import com.pacto.adm.core.entities.conveniocobranca.ConvenioCobranca;
import com.pacto.adm.core.services.interfaces.conveniocobranca.ConvenioCobrancaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ConvenioCobrancaServiceImpl implements ConvenioCobrancaService {

    private final ConvenioCobrancaRepository convenioCobrancaRepository;

    public ConvenioCobrancaServiceImpl(ConvenioCobrancaRepository convenioCobrancaRepository) {
        this.convenioCobrancaRepository = convenioCobrancaRepository;
    }

    @Override
    public List<ConvenioCobranca> convenioCobrancaPorEmpresa(Integer empresaId) throws ServiceException {
        try {
            if (UteisValidacao.emptyNumber(empresaId)) {
                return convenioCobrancaRepository.findAll();
            }

            return convenioCobrancaRepository.findByEmpresaId(empresaId);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
