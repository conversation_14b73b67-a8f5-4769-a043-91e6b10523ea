package com.pacto.adm.core.services.interfaces;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoWordPressDTO;
import com.pacto.config.exceptions.ServiceException;


public interface ConfiguracaoIntegracaoWordPressService {

    ConfiguracaoIntegracaoWordPressDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException;

    ConfiguracaoIntegracaoWordPressDTO salvar(ConfiguracaoIntegracaoWordPressDTO configuracaoIntegracaoWordPressDTO) throws ServiceException;

}
