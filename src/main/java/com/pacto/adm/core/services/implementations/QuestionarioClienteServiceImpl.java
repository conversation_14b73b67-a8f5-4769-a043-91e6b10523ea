package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.QuestionarioClienteAdapter;
import com.pacto.adm.core.adapters.QuestionarioPerguntaClienteAdapter;
import com.pacto.adm.core.adapters.colaborador.ColaboradorAdapter;
import com.pacto.adm.core.adapters.questionario.QuestionarioAdapter;
import com.pacto.adm.core.dao.interfaces.ClienteRepository;
import com.pacto.adm.core.dao.interfaces.LogRepository;
import com.pacto.adm.core.dao.interfaces.QuestionarioClienteDao;
import com.pacto.adm.core.dao.interfaces.QuestionarioPerguntaClienteDao;
import com.pacto.adm.core.dao.interfaces.RespostaPergClienteDao;
import com.pacto.adm.core.dao.interfaces.RespostaPergClienteRepository;
import com.pacto.adm.core.dao.interfaces.UsuarioRepository;
import com.pacto.adm.core.dao.interfaces.agenda.AgendaRepository;
import com.pacto.adm.core.dao.interfaces.colaborador.ColaboradorRepository;
import com.pacto.adm.core.dao.interfaces.configuracaosistema.ConfiguracaoSistemaRepository;
import com.pacto.adm.core.dao.interfaces.empresa.EmpresaRepository;
import com.pacto.adm.core.dao.interfaces.periodoacessocliente.PeriodoAcessoClienteRepository;
import com.pacto.adm.core.dao.interfaces.permissao.PermissaoRepository;
import com.pacto.adm.core.dao.interfaces.questionario.questionarioCliente.QuestionarioClienteRepository;
import com.pacto.adm.core.dao.interfaces.questionario.questionarioperguntacliente.QuestionarioPerguntaClienteRepository;
import com.pacto.adm.core.dao.interfaces.questionariopergunta.QuestionarioPerguntaRepository;
import com.pacto.adm.core.dao.interfaces.vinculo.VinculoRepository;
import com.pacto.adm.core.dto.QuestionarioClienteDTO;
import com.pacto.adm.core.dto.QuestionarioPerguntaClienteDTO;
import com.pacto.adm.core.dto.RespostaPergClienteDTO;
import com.pacto.adm.core.dto.VinculoDTO;
import com.pacto.adm.core.dto.auth.AuthDTO;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.ConfiguracaoSistema;
import com.pacto.adm.core.entities.Log;
import com.pacto.adm.core.entities.PerguntaCliente;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.entities.Questionario;
import com.pacto.adm.core.entities.QuestionarioCliente;
import com.pacto.adm.core.entities.QuestionarioPergunta;
import com.pacto.adm.core.entities.QuestionarioPerguntaCliente;
import com.pacto.adm.core.entities.RespostaPergCliente;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.Vinculo;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.enumerador.TipoBV;
import com.pacto.adm.core.enumerador.TipoColaboradorEnum;
import com.pacto.adm.core.enumerador.TipoServicoEnum;
import com.pacto.adm.core.services.interfaces.ClienteService;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.adm.core.services.interfaces.QuestionarioClienteService;
import com.pacto.adm.core.services.interfaces.VinculoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class QuestionarioClienteServiceImpl implements QuestionarioClienteService {

    @Autowired
    QuestionarioClienteDao questionarioClienteDao;
    @Autowired
    QuestionarioClienteAdapter questionarioClienteAdapter;
    @Autowired
    QuestionarioPerguntaClienteDao questionarioPerguntaClienteDao;
    @Autowired
    QuestionarioPerguntaClienteAdapter questionarioPerguntaClienteAdapter;
    @Autowired
    RespostaPergClienteDao respostaPergClienteDao;
    @Autowired
    private QuestionarioClienteRepository questionarioClienteRepository;
    @Autowired
    private ClienteRepository clienteRepository;
    @Autowired
    private QuestionarioPerguntaClienteRepository questionarioPerguntaClienteRepository;
    @Autowired
    private ConfiguracaoSistemaRepository configuracaoSistemaRepository;
    @Autowired
    private QuestionarioAdapter questionarioAdapter;
    @Autowired
    private VinculoRepository vinculoRepository;
    @Autowired
    private ColaboradorAdapter colaboradorAdapter;
    @Autowired
    private QuestionarioPerguntaRepository questionarioPerguntaRepository;
    @Autowired
    private RespostaPergClienteRepository respostaPergClienteRepository;
    @Autowired
    private RequestService requestService;
    @Autowired
    private PermissaoRepository permissaoRepository;
    @Autowired
    private UsuarioRepository usuarioRepository;
    @Autowired
    private VinculoService vinculoService;
    @Autowired
    private AgendaRepository agendaRepository;
    @Autowired
    private ClienteService clienteService;
    @Autowired
    private PeriodoAcessoClienteRepository periodoAcessoClienteRepository;
    @Autowired
    private LogRepository logRepository;
    @Autowired
    private ColaboradorRepository colaboradorRepository;
    @Autowired
    private LogServiceImpl logServiceImpl;
    @Autowired
    private EmpresaRepository empresaRepository;
    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private HttpServico httpServico;

    @Override
    public List<QuestionarioClienteDTO> consultaHistoricoBVs(Integer codCliente, PaginadorDTO paginadorDTO) throws Exception {
        List<QuestionarioClienteDTO> questionarioClienteDTOS = questionarioClienteAdapter.toDtos(questionarioClienteDao.findByCliente(codCliente, paginadorDTO));

        for (QuestionarioClienteDTO questionarioClienteDTO : questionarioClienteDTOS) {

            questionarioClienteDTO.setQuestionarioPerguntaCliente(questionarioPerguntaClienteAdapter.toDtos(questionarioPerguntaClienteDao.findByQuestionarioCliente(questionarioClienteDTO.getCodigo(), null)));
        }

        return questionarioClienteDTOS;
    }

    @Override
    public QuestionarioClienteDTO editarBVs(QuestionarioClienteDTO questionarioClienteDTO) throws Exception {
        try {
            QuestionarioCliente questionarioCliente = questionarioClienteDao.update(questionarioClienteAdapter.toEntity(questionarioClienteDTO));
            Set<QuestionarioPerguntaCliente> questionarioPerguntaClientes = new HashSet<>();
            for(QuestionarioPerguntaCliente questionarioPerguntaCliente : questionarioPerguntaClienteAdapter.toEntities(questionarioClienteDTO.getQuestionarioPerguntaCliente())) {
                questionarioPerguntaClientes.add(questionarioPerguntaClienteDao.update(questionarioPerguntaCliente));
                Set<RespostaPergCliente> respostaPergClientes = questionarioPerguntaCliente.getPerguntaCliente().getRespostaPergCliente();
                for(RespostaPergCliente respostaPergCliente : respostaPergClientes) {
                    respostaPergCliente.setPerguntaCliente(questionarioPerguntaCliente.getPerguntaCliente());
                    respostaPergClienteDao.update(respostaPergCliente);
                }
            }

            questionarioClienteDTO = questionarioClienteAdapter.toDto(questionarioCliente);
            questionarioClienteDTO.setQuestionarioPerguntaCliente(questionarioPerguntaClienteAdapter.toDtos(questionarioPerguntaClientes));

            return questionarioClienteDTO;
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, ServiceException.class}, transactionManager = "zwClientTransactionManager")
    public QuestionarioClienteDTO cadastrarBv(QuestionarioClienteDTO questionarioClienteDTO, Boolean rematricula) throws ServiceException {
        try {
            QuestionarioClienteDTO ultimoBvCliente = ultimoBVCliente(questionarioClienteDTO.getCliente(), rematricula, false);
            validacoesAntesCadastrarBv(questionarioClienteDTO, ultimoBvCliente);
            Cliente cliente = clienteRepository.findById(questionarioClienteDTO.getCliente())
                    .orElseThrow(() -> new ServiceException("cliente-nao-encontrato", "Cliente não encontrado com o id informado!"));

            if (questionarioClienteDTO.getFreepass() != null && questionarioClienteDTO.getFreepass().getCodigo() != null) {
                if (questionarioClienteDTO.getResponsavelFreepass() != null) {
                    usuarioRepository.verificarLoginUsuario(
                            questionarioClienteDTO.getResponsavelFreepass().getCodigo(),
                            Uteis.encriptar(questionarioClienteDTO.getResponsavelFreepass().getSenha())
                    ).orElseThrow(() -> new ServiceException("usuario-inválido", "Usuario responsável pelo lançamento do freepas é inválido!"));
                }

                cliente.setResponsavelfreepass(questionarioClienteDTO.getResponsavelFreepass().getCodigo());
                cliente.setFreepass(new Produto(questionarioClienteDTO.getFreepass().getCodigo()));
                clienteService.alterarFreepass(cliente.getFreepass().getCodigo(), cliente.getResponsavelfreepass());
                cliente = clienteRepository.findById(questionarioClienteDTO.getCliente())
                        .orElseThrow(() -> new ServiceException("cliente-nao-encontrato", "Cliente não encontrado com o id informado!"));
                periodoAcessoClienteRepository.save(cliente.gerarPeriodoAcessoBaseadoCliente());
            }

            vinculoService.validarVinculoConsultor(cliente, questionarioClienteDTO);

            // limpar o código para não realizar edição do último bv do cliente
            questionarioClienteDTO.setCodigo(null);
            QuestionarioCliente questionarioCliente = questionarioClienteRepository.save(questionarioClienteAdapter.toEntity(questionarioClienteDTO));

            Set<QuestionarioPerguntaCliente> questionarioPerguntaClientes = new HashSet<>();
            for(QuestionarioPerguntaCliente questionarioPerguntaCliente : questionarioPerguntaClienteAdapter.toEntities(questionarioClienteDTO.getQuestionarioPerguntaCliente())) {
                questionarioPerguntaCliente.setQuestionarioCliente(questionarioCliente.getCodigo());
                Set<RespostaPergCliente> respostaPergClientes = questionarioPerguntaCliente.getPerguntaCliente().getRespostaPergCliente();
                for(RespostaPergCliente respostaPergCliente : respostaPergClientes) {
                    respostaPergCliente.setPerguntaCliente(questionarioPerguntaCliente.getPerguntaCliente());
                    if (questionarioPerguntaCliente.getPerguntaCliente().getTipoPergunta().equalsIgnoreCase("TE") || questionarioPerguntaCliente.getPerguntaCliente().getTipoPergunta().equalsIgnoreCase("SE")) {
                        if (respostaPergCliente.getRespostaTextual() == null) {
                            respostaPergCliente.setDescricaoRespota("");
                        } else {
                            respostaPergCliente.setDescricaoRespota(respostaPergCliente.getRespostaTextual());
                        }
                        if (respostaPergCliente.getRespostaOpcao() == null) {
                            respostaPergCliente.setRespostaOpcao(false);
                        } else {
                            respostaPergCliente.setRespostaOpcao(respostaPergCliente.getRespostaOpcao());
                        }
                        respostaPergCliente.setRespostaTextual(null);
                    } else if (questionarioPerguntaCliente.getPerguntaCliente().getTipoPergunta().equalsIgnoreCase("ME")){
                        if (respostaPergCliente.getRespostaOpcao() == null) {
                            respostaPergCliente.setRespostaOpcao(false);
                        }
                        respostaPergCliente.setRespostaTextual("");

                    }
                }

                questionarioPerguntaClientes.add(questionarioPerguntaClienteRepository.save(questionarioPerguntaCliente));
            }

            if (questionarioClienteDTO.getAlterarAgendamentoConsultorAtual() &&
                    this.agendaRepository.clientePossuiAgendamentosColaborador(
                            questionarioClienteDTO.getCodigoConsultorAntesAlteracao(),
                            questionarioClienteDTO.getCliente(),
                            Uteis.getDataJDBC(Calendario.hoje()))
            ) {
                validarAgendamentoCliente(questionarioCliente, questionarioClienteDTO.getCodigoConsultorAntesAlteracao());
            }

            AuthDTO responsavelAlteracaoDataBv = questionarioClienteDTO.getResponsavelAlteracaoDataBv();
            questionarioClienteDTO = questionarioClienteAdapter.toDto(questionarioCliente);
            questionarioClienteDTO.setResponsavelAlteracaoDataBv(responsavelAlteracaoDataBv);
            questionarioClienteDTO.setQuestionarioPerguntaCliente(questionarioPerguntaClienteAdapter.toDtos(questionarioPerguntaClientes));
            if (!Calendario.igual(Calendario.hoje(), questionarioClienteDTO.getData())) {
                if (questionarioClienteDTO.getResponsavelAlteracaoDataBv() == null || questionarioClienteDTO.getResponsavelAlteracaoDataBv().getCodigo() == null) {
                    throw new ServiceException("data-bv-alterada-sem-usuario", "A data do BV foi alterada, mas não foi informado o usuário responsável pela alteração!");
                }
                registrarLogAlteracaoDataBoletim(questionarioClienteDTO, cliente);
            }

            clienteService.limparPendenciaQuestionarioCliente(cliente);

            atualizarSintetico(questionarioCliente);
            questionarioClienteDTO.setConsultor(
                    colaboradorAdapter.toDto(colaboradorRepository.findById(questionarioClienteDTO.getConsultor().getCodigo()).orElseThrow(() -> new ServiceException("consultor-nao-encontrato", "O consultor não foi encontrado no banco!")))
            );

            logServiceImpl.incluirLogInclusaoAlteracao(questionarioCliente, null, "QUESTIONARIOCLIENTE", "Questionário Cliente", cliente.getPessoa().getCodigo());

            return questionarioClienteDTO;
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }

    private void registrarLogAlteracaoDataBoletim(QuestionarioClienteDTO questionarioCliente, Cliente cliente) {
        Log log = new Log();
        log.setPessoa(cliente.getPessoa().getCodigo());
        log.setChavePrimaria(questionarioCliente.getCodigo().toString());
        log.setNomeEntidade("BOLETIM_VISITA");
        log.setNomeEntidadeDescricao("QuestionarioCliente");
        log.setOperacao("ALTERAÇÃO DA DATA DO BOLETIM " + questionarioCliente.getCodigo());
        log.setResponsavelAlteracao(questionarioCliente.getResponsavelAlteracaoDataBv().getUsername());
        log.setNomeCampo("DATA");
        log.setDataAlteracao(Calendario.hoje());
        log.setValorCampoAnterior(Uteis.getData(Calendario.hoje()));
        log.setValorCampoAlterado(Uteis.getData(questionarioCliente.getData()));
        logRepository.save(log);
    }

    private void atualizarSintetico(QuestionarioCliente questionarioCliente) throws ServiceException {
        try {
            clienteService.atualizarSinteticoTelaCliente(questionarioCliente.getCliente());
        } catch (ServiceException e) {
            throw new ServiceException("falha-atualizar-sintentico-cliente", "Não foi possível atualizar o sintético: ", e);
        }
    }

    @Transactional(transactionManager = "zwClientTransactionManager")
    public void validarAgendamentoCliente(QuestionarioCliente questionarioCliente, Integer codigoConsultorAntesAlteracao) throws ServiceException {
        if(codigoConsultorAntesAlteracao != null && questionarioCliente.getConsultor() != null && !codigoConsultorAntesAlteracao.equals(questionarioCliente.getConsultor().getCodigo())){
            Usuario usuarioAntigo = usuarioRepository.findByColaboradorCodigo(codigoConsultorAntesAlteracao);
            Usuario usuarioNovo = usuarioRepository.findByColaboradorCodigo(questionarioCliente.getConsultor().getCodigo());
            if (usuarioNovo == null) {
                throw new ServiceException("usuario-colaborador-nao-encontrato", "Nenhum usuário encontrado para este colaborador nesta empresa!");
            }
            agendaRepository.alterarConsultorResponsavelAgenda(usuarioAntigo.getCodigo(), usuarioNovo.getCodigo(), questionarioCliente.getCliente(), Calendario.hoje());
        }
    }

    private void validacoesAntesCadastrarBv(QuestionarioClienteDTO questionarioClienteDTO, QuestionarioClienteDTO ultimoBvCliente) throws ServiceException {
        Cliente cliente = clienteRepository.findById(questionarioClienteDTO.getCliente()).orElseThrow(
                () -> new ServiceException("cliente-nao-encontrado", "Nenhum cliente encontrado com o código informado."));
        if (cliente.getEmpresa() == null || cliente.getEmpresa().getCodigo().equals(0)) {
            throw new ServiceException("cliente-sem-empresa", "O cliente foi encontrado mas não possui empresa vinculada.");
        }
        Empresa empresa = cliente.getEmpresa();
        if (empresa.getBvObrigatorio()) {
            String respostaObrigatoria = validarRespondeuTodasAsQuestoesObrigatorias(questionarioClienteDTO);
            if (StringUtils.hasText(respostaObrigatoria)) {
                throw new ServiceException(
                        "perguntas-obrigatorias-nao-respondidas",
                        "A pergunta " + respostaObrigatoria + " é obrigatória!"
                );
            }
        }
        if (questionarioClienteDTO.getConsultor() == null || questionarioClienteDTO.getConsultor().getCodigo() == 0) {
            throw new ServiceException(
                    "consultor-nao-informado",
                    "O consultor não foi informado!"
            );
        }
        if (questionarioClienteDTO.getCliente() == null || questionarioClienteDTO.getCliente() == 0) {
            throw new ServiceException(
                    "cliente-nao-informado",
                    "O cliente não foi informado!"
            );
        }
        if (questionarioClienteDTO.getData() == null) {
            throw new ServiceException(
                    "data-nao-informada",
                    "A data não foi informada!"
            );
        }
        if (questionarioClienteDTO.getFreepass() != null &&
                questionarioClienteDTO.getFreepass().getCodigo() != null &&
                questionarioClienteDTO.getResponsavelFreepass() == null
        ) {
            throw new ServiceException(
                    "responsavel-freepass-nao-informado",
                    "Foi selecionado um FreePass para o cliente mas não foi informado o resposável pelo lançamento!"
            );
        }
        boolean temPermissaoAlterarVinculoConsultor = permissaoRepository
                .existsByCodUsuarioAndNomeEntidadeAndTituloApresentacao(
                        requestService.getUsuarioAtual().getCodZw(),
                        "Vinculo",
                        "2.29 - Incluir Vínculos Cliente/Colaborador"
                );

        if (!temPermissaoAlterarVinculoConsultor) {
            if (ultimoBvCliente != null &&
                    ((ultimoBvCliente.getConsultor() == null && questionarioClienteDTO.getConsultor() != null) ||
                            (!ultimoBvCliente.getConsultor().getCodigo().equals(questionarioClienteDTO.getConsultor().getCodigo())))) {
                throw new ServiceException(
                        "usuario-sem-permissao-para-alerar-consultor",
                        "O usuário não possui permissão para alterar o consultor do cliente!"
                );
            }

            if (ultimoBvCliente == null && questionarioClienteDTO != null && !CollectionUtils.isEmpty(questionarioClienteDTO.getVinculos())) {
                throw new ServiceException(
                        "usuario-sem-permissao-para-alerar-consultor",
                        "O usuário não possui permissão para alterar o consultor do cliente!"
                );
            }

            if (ultimoBvCliente != null) {
                if (ultimoBvCliente.getVinculos().size() != questionarioClienteDTO.getVinculos().size()) {
                    throw new ServiceException(
                            "usuario-sem-permissao-para-alerar-consultor",
                            "O usuário não possui permissão para alterar o consultor do cliente!"
                    );
                }

                List<VinculoDTO> vinculosDiferentes = questionarioClienteDTO.getVinculos().stream().filter(vinculoQuestionario -> {
                    return ultimoBvCliente.getVinculos().stream().anyMatch(vinculoUltimoBV -> !vinculoQuestionario.getCodigoColaborador().equals(vinculoQuestionario.getCodigoColaborador())
                            && !vinculoQuestionario.getTipoVinculo().equals(vinculoQuestionario.getTipoVinculo()));
                }).collect(Collectors.toList());

                if (!UteisValidacao.emptyList(vinculosDiferentes)){
                    throw new ServiceException(
                            "usuario-sem-permissao-para-alerar-consultor",
                            "O usuário não possui permissão para alterar o consultor do cliente!"
                    );
                }
            }
        }
    }

    private String validarRespondeuTodasAsQuestoesObrigatorias(QuestionarioClienteDTO questionarioClienteDTO) {
        for (QuestionarioPerguntaClienteDTO q : questionarioClienteDTO.getQuestionarioPerguntaCliente()) {
            if(!q.getPerguntaCliente().getObrigatoria()){
                continue;
            }
            boolean respondeuPergunta = false;
            for (RespostaPergClienteDTO res : q.getPerguntaCliente().getRespostaPergCliente()) {
                if (!q.getPerguntaCliente().getTipoPergunta().equals("TE")) {
                    respondeuPergunta = respondeuPergunta || (res.getRespostaOpcao() != null);
                } else {
                    respondeuPergunta = respondeuPergunta || StringUtils.hasText(res.getRespostaTextual());
                }
            }
            if (!respondeuPergunta) {
                return q.getPerguntaCliente().getDescricao();
            }
        }

        return "";
    }

    @Override
    @Transactional(transactionManager = "zwClientTransactionManager")
    public QuestionarioClienteDTO ultimoBVCliente(Integer codCliente, Boolean rematricula, Boolean ehVendaDeSessao) throws ServiceException {
        Cliente cliente = clienteRepository.findById(codCliente).orElseThrow(
                () -> new ServiceException("cliente-nao-encontrado", "Nenhum cliente encontrado com o código informado."));
        if (cliente.getEmpresa() == null || cliente.getEmpresa().getCodigo().equals(0)) {
            throw new ServiceException("cliente-sem-empresa", "O cliente foi encontrado mas não possui empresa vinculada.");
        }

        Empresa empresa = cliente.getEmpresa();
        long nrdias = 0;

        QuestionarioClienteDTO questionarioClienteDTO = null;
        questionarioClienteDTO = questionarioClienteFromEmpresa(empresa, nrdias, codCliente, rematricula, ehVendaDeSessao);

        if (questionarioClienteDTO == null) {
            questionarioClienteDTO = questionarioClienteFromConfiguracaoSistema(codCliente, nrdias, rematricula, ehVendaDeSessao);
        }

        if (questionarioClienteDTO == null) {
            throw new ServiceException(
                    "questionario-nao-definidos-empresa-configuracao-sistema",
                    "Os questionários não foram definidos na empresa ou na configuração do sistema."
            );
        }

        Vinculo vinculo = vinculoRepository.findByTipoVinculoAndCliente_Codigo(TipoColaboradorEnum.CONSULTOR.getSigla(), codCliente);
        if (vinculo != null) {
            questionarioClienteDTO.setConsultor(
                    colaboradorAdapter.toDto(vinculo.getColaborador())
            );
            questionarioClienteDTO.setCodigoConsultorAntesAlteracao(
                    vinculo.getColaborador().getCodigo()
            );
        }

        List<Vinculo> vinculos = vinculoRepository.findByCliente_CodigoAndTipoVinculoNotLike(codCliente, TipoColaboradorEnum.CONSULTOR.getSigla());
        questionarioClienteDTO.setVinculos(vinculos.stream().map(VinculoDTO::new).collect(Collectors.toList()));

        questionarioClienteDTO.setCliente(cliente.getCodigo());
        questionarioClienteDTO.setPossuiAgendamentosConsultorAtual(
                agendaRepository.clientePossuiAgendamentosColaborador(
                        questionarioClienteDTO.getConsultor().getCodigo(),
                        questionarioClienteDTO.getCliente(),
                        Uteis.getDataJDBC(Calendario.hoje())
                )
        );
        return questionarioClienteDTO;
    }

    private QuestionarioClienteDTO questionarioClienteFromEmpresa(
            Empresa empresa,
            long nrdias,
            Integer codCliente,
            Boolean rematricula,
            boolean ehVendaDeSessao) throws ServiceException {
        QuestionarioClienteDTO questionarioClienteDTO = null;
        TipoServicoEnum tipoServicoEnum = ehVendaDeSessao ? TipoServicoEnum.SERVICO : TipoServicoEnum.PLANO;

        if (ehVendaDeSessao) {
            return questionarioEmpresaVendaSessao(empresa, nrdias, codCliente, rematricula, tipoServicoEnum);
        }

        if (empresa.getQuestionarioPrimeiraVisita() != null && empresa.getQuestionarioPrimeiraVisita().getCodigo() != 0
                && empresa.getQuestionarioRetorno() != null && empresa.getQuestionarioRetorno().getCodigo() != 0
                && empresa.getQuestionarioRematricula() != null && empresa.getQuestionarioRematricula().getCodigo() != 0) {
            List<QuestionarioCliente> ultimoQuestionario = questionarioClienteRepository.findByClienteAndTipoQuestionario(
                    codCliente,
                    tipoServicoEnum.getTipo(), PageRequest.of(0, 1)
            ).getContent();
            QuestionarioCliente questionarioCliente = null;
            if (!ultimoQuestionario.isEmpty()) {
                questionarioCliente = ultimoQuestionario.get(0);
            }

            if (rematricula) {
                if (questionarioCliente != null) {
                    nrdias = Uteis.nrDiasEntreDatas(questionarioCliente.getData(),
                            Calendario.hoje());

                    if (validarDataQuestionario(questionarioCliente)) {
                        if (Calendario.menor(questionarioCliente.getData(),
                                Calendario.hoje())) {
                           return fillQuestionarioCliente(
                                    TipoBV.RE,
                                    questionarioCliente,
                                    empresa.getQuestionarioRematricula()
                            );
                        }
                    }

                    if (nrdias > empresa.getNrDiasVigenteQuestionarioRematricula()) {
                        return fillQuestionarioCliente(
                                TipoBV.RE,
                                questionarioCliente,
                                empresa.getQuestionarioRematricula()
                        );
                    } else {
                        return new QuestionarioClienteDTO();
                    }
                } else {
                    return fillQuestionarioCliente(
                            TipoBV.RE,
                            new QuestionarioCliente(),
                            empresa.getQuestionarioRematricula()
                    );
                }
            }

            // se for matricula
            questionarioClienteDTO = questionarioMatricula(questionarioCliente, empresa.getQuestionarioPrimeiraVisita());
            if (questionarioClienteDTO != null) {
                return questionarioClienteDTO;
            }
            // validar se a data do questionario respondindo e do mes passado ou do
            // mes corrente
            // pois se for do mes passado e mesmo o numero de dias de validade para
            // o questionario nao ter terminado ele vai responder o questionario.
            if (empresa.getQuestionarioRetorno().getCodigo() != 0
                    && questionarioCliente != null) {
                nrdias = Uteis.nrDiasEntreDatas(questionarioCliente.getData(),
                        Calendario.hoje());

                if (validarDataQuestionario(questionarioCliente)) {
                    if (Calendario.menor(questionarioCliente.getData(),
                            Calendario.hoje())) {
                        return fillQuestionarioCliente(
                                TipoBV.RT,
                                questionarioCliente,
                                empresa.getQuestionarioRetorno()
                        );
                    }
                }
                if (nrdias > empresa.getNrDiasVigenteQuestionarioRetorno()) {
                    return fillQuestionarioCliente(
                            TipoBV.RT,
                            questionarioCliente,
                            empresa.getQuestionarioRetorno()
                    );
                } else {
                    return new QuestionarioClienteDTO();
                }
            }
        }
        return questionarioClienteDTO;
    }

    private QuestionarioClienteDTO questionarioEmpresaVendaSessao(Empresa empresa, long nrdias, Integer codCliente, Boolean rematricula, TipoServicoEnum tipoServicoEnum) throws ServiceException {
        List<QuestionarioCliente> questionariosCliente = questionarioClienteRepository.findByClienteAndTipoQuestionario(
                codCliente,
                tipoServicoEnum.getTipo(), PageRequest.of(0, 1)
        ).getContent();

        if (empresa.getQuestionarioPrimeiraCompra() != null && empresa.getQuestionarioPrimeiraCompra().getCodigo() != 0 && questionariosCliente.isEmpty()) {
            return fillQuestionarioCliente(
                    TipoBV.SS_PRIMEIRA_COMPRA,
                    new QuestionarioCliente(),
                    empresa.getQuestionarioPrimeiraCompra()
            );
        }

        QuestionarioCliente questionarioCliente = null;
        if (!questionariosCliente.isEmpty()) {
            questionarioCliente = questionariosCliente.get(0);
        }

        if (empresa.getQuestionarioRetornoCompra().getCodigo() != 0 && questionarioCliente != null) {
            nrdias = Uteis.nrDiasEntreDatas(questionarioCliente.getData(), Calendario.hoje());

            if (validarDataQuestionario(questionarioCliente)) {
                if (Calendario.menor(questionarioCliente.getData(), Calendario.hoje())) {
                    return fillQuestionarioCliente(
                            TipoBV.SS_RETORNO_COMPRA,
                            questionarioCliente,
                            empresa.getQuestionarioRetornoCompra()
                    );
                }
            }

            if (nrdias > empresa.getNrDiasVigenteQuestionarioRetornoCompra()) {
                return fillQuestionarioCliente(
                        TipoBV.SS_RETORNO_COMPRA,
                        questionarioCliente,
                        empresa.getQuestionarioRetornoCompra()
                );
            }
        }
        return null;
    }

    private QuestionarioClienteDTO questionarioClienteFromConfiguracaoSistema(
            Integer codCliente,
            long nrdias,
            Boolean rematricula,
            boolean ehVendaDeSessao) throws ServiceException {
        List<ConfiguracaoSistema> configuracoesSistema = configuracaoSistemaRepository.findAll();
        if(configuracoesSistema.isEmpty()){
            throw new ServiceException("Nenhuma configuração do sistema foi encontrada");
        }

        if(configuracoesSistema.size() > 1){
            throw new ServiceException("Mais de um registro foi encontrado em configuracaosistema");
        }

        ConfiguracaoSistema configuracaoSistema = configuracoesSistema.get(0);
        QuestionarioClienteDTO questionarioClienteDTO = null;
        TipoServicoEnum tipoServicoEnum = ehVendaDeSessao ? TipoServicoEnum.SERVICO : TipoServicoEnum.PLANO;

        if (ehVendaDeSessao) {
            return questionarioConfiguracaoVendaSessao(configuracaoSistema, nrdias, codCliente, rematricula, tipoServicoEnum);
        }

        if (configuracaoSistema.getCodigo() != 0
                && configuracaoSistema.getQuestionarioPrimeiraVisita() != null && configuracaoSistema.getQuestionarioPrimeiraVisita().getCodigo() != 0
                && configuracaoSistema.getQuestionarioRetorno() != null && configuracaoSistema.getQuestionarioRetorno().getCodigo() != 0
                && configuracaoSistema.getQuestionarioRematricula() != null && configuracaoSistema.getQuestionarioRematricula().getCodigo() != 0) {
            List<QuestionarioCliente> ultimoQuestionario = questionarioClienteRepository.findByClienteAndTipoQuestionario(
                    codCliente,
                    tipoServicoEnum.getTipo(), PageRequest.of(0, 1)
            ).getContent();
            QuestionarioCliente questionarioCliente = null;
            if (!ultimoQuestionario.isEmpty()) {
                questionarioCliente = ultimoQuestionario.get(0);
            }
            if (rematricula) {
                if (questionarioCliente != null) {
                    nrdias = Uteis.nrDiasEntreDatas(questionarioCliente.getData(),
                            Calendario.hoje());
                    if (validarDataQuestionario(questionarioCliente)) {
                        if (Calendario.menor(questionarioCliente.getData(),
                                Calendario.hoje())) {
                            return fillQuestionarioCliente(
                                    TipoBV.RE,
                                    questionarioCliente,
                                    configuracaoSistema.getQuestionarioRetorno()
                            );
                        }
                    }

                    if (nrdias > configuracaoSistema.getNrDiasVigenteQuestionarioRematricula()) {
                        return fillQuestionarioCliente(
                                TipoBV.RE,
                                questionarioCliente,
                                configuracaoSistema.getQuestionarioRematricula()
                        );
                    } else {
                        return new QuestionarioClienteDTO();
                    }
                } else {
                    return fillQuestionarioCliente(
                            TipoBV.RE,
                            new QuestionarioCliente(),
                            configuracaoSistema.getQuestionarioRematricula()
                    );
                }
            }

            questionarioClienteDTO = questionarioMatricula(questionarioCliente, configuracaoSistema.getQuestionarioPrimeiraVisita());
            if (questionarioClienteDTO != null) {
                return questionarioClienteDTO;
            }

            if (configuracaoSistema.getQuestionarioPrimeiraVisita().getCodigo() != 0 && questionarioCliente == null) {
                return fillQuestionarioCliente(
                        TipoBV.MA,
                        new QuestionarioCliente(),
                        configuracaoSistema.getQuestionarioPrimeiraVisita()
                );
            }

            if (configuracaoSistema.getQuestionarioRetorno().getCodigo() != 0 && questionarioCliente != null) {
                nrdias = Uteis.nrDiasEntreDatas(questionarioCliente.getData(), Calendario.hoje());

                if (validarDataQuestionario(questionarioCliente)) {
                    if (Calendario.menor(questionarioCliente.getData(),
                            Calendario.hoje())) {
                        return fillQuestionarioCliente(
                                TipoBV.RT,
                                questionarioCliente,
                                configuracaoSistema.getQuestionarioRetorno()
                        );
                    }
                }

                if (nrdias > configuracaoSistema.getNrDiasVigenteQuestionarioRetorno()) {
                    return fillQuestionarioCliente(
                            TipoBV.RT,
                            questionarioCliente,
                            configuracaoSistema.getQuestionarioRetorno()
                    );
                } else {
                    return new QuestionarioClienteDTO();
                }
            }
        }
        return questionarioClienteDTO;
    }

    private QuestionarioClienteDTO questionarioConfiguracaoVendaSessao(ConfiguracaoSistema configuracaoSistema, long nrdias, Integer codCliente, Boolean rematricula, TipoServicoEnum tipoServicoEnum) throws ServiceException {
        List<QuestionarioCliente> questionariosCliente = questionarioClienteRepository.findByClienteAndTipoQuestionario(
                codCliente,
                tipoServicoEnum.getTipo(), PageRequest.of(0, 1)
        ).getContent();

        if (questionariosCliente.isEmpty()) {
            throw new ServiceException("questionario-configuracao-nao-encontrado-venda-sessao", "Não foi encontrado nenhum questionário na configuração do sistema.");
        }

        QuestionarioCliente questionarioCliente = questionariosCliente.get(0);

        if (configuracaoSistema.getQuestionarioPrimeiraCompra().getCodigo() != 0 && questionarioCliente == null) {
            return fillQuestionarioCliente(
                    TipoBV.SS_PRIMEIRA_COMPRA,
                    new QuestionarioCliente(),
                    configuracaoSistema.getQuestionarioPrimeiraCompra()
            );
        }

        if (configuracaoSistema.getQuestionarioRetornoCompra().getCodigo() != 0 && questionarioCliente != null) {
            nrdias = Uteis.nrDiasEntreDatas(questionarioCliente.getData(), Calendario.hoje());

            if (validarDataQuestionario(questionarioCliente)) {
                if (Calendario.menor(questionarioCliente.getData(), Calendario.hoje())) {
                    return fillQuestionarioCliente(
                            TipoBV.SS_RETORNO_COMPRA,
                            questionarioCliente,
                            configuracaoSistema.getQuestionarioRetornoCompra()
                    );
                }
            }

            if (nrdias > configuracaoSistema.getNrDiasVigenteQuestionarioRetornoCompra()) {
                return fillQuestionarioCliente(
                        TipoBV.SS_RETORNO_COMPRA,
                        questionarioCliente,
                        configuracaoSistema.getQuestionarioRetornoCompra()
                );
            }
        }
        return null;
    }

    private QuestionarioClienteDTO questionarioMatricula(QuestionarioCliente questionarioCliente, Questionario questionarioPrimeiraVisita) {
        if (questionarioPrimeiraVisita.getCodigo() != 0 && questionarioCliente == null) {
            return fillQuestionarioCliente(
                    TipoBV.MA,
                    new QuestionarioCliente(),
                    questionarioPrimeiraVisita
            );
        }
        return null;
    }

    private boolean validarDataQuestionario(QuestionarioCliente questionarioCliente) {
        int mesValidade = Uteis.getMesData(questionarioCliente.getData());

        int anoValidade = Uteis.getAnoData(questionarioCliente.getData());

        int mesCorrente = Uteis.getMesData(Calendario.hoje());

        int anoCorrente = Uteis.getAnoData(Calendario.hoje());

        return mesValidade != mesCorrente || anoValidade != anoCorrente;
    }

    private QuestionarioClienteDTO fillQuestionarioCliente(TipoBV tipoBV, QuestionarioCliente questionarioCliente, Questionario questionario) {
        questionarioCliente.setTipoBV(tipoBV.getCodigo());
        questionarioCliente.setQuestionario(questionario);
        QuestionarioClienteDTO questionarioClienteDTO = questionarioClienteAdapter
                .toDto(questionarioCliente);
        questionarioClienteDTO.setQuestionario(questionarioAdapter.toDto(questionario));
        List<QuestionarioPergunta> questionarioPerguntas = questionarioPerguntaRepository
                .findByQuestionario_Codigo(questionario.getCodigo());

        List<QuestionarioPerguntaCliente> perguntas = inicializarQuestionarioPerguntaClientes(questionarioPerguntas);
        questionarioClienteDTO.setQuestionarioPerguntaCliente(
                questionarioPerguntaClienteAdapter.toDtos(perguntas)
        );
        questionarioClienteDTO.setPreencherQuestionario(true);
        return questionarioClienteDTO;
    }

    private List<QuestionarioPerguntaCliente> inicializarQuestionarioPerguntaClientes(List<QuestionarioPergunta> questionarioPerguntas) {
        List<QuestionarioPerguntaCliente> questionarioPerguntaClientes = new ArrayList<>();
        questionarioPerguntas.forEach(
                objExistente -> {
                    QuestionarioPerguntaCliente questionarioPerguntaClienteVO = new QuestionarioPerguntaCliente();
                    PerguntaCliente perguntaCliente = getPerguntaCliente(objExistente);

                    objExistente.getPergunta().getRespostaPerguntas().forEach(
                            objrespota -> {
                                RespostaPergCliente repostaPergClienteVO = new RespostaPergCliente();
                                repostaPergClienteVO.setDescricaoRespota(objrespota.getDescricaorespota());
                                perguntaCliente.getRespostaPergCliente().add(
                                        repostaPergClienteVO);
                            }
                    );
                    if (perguntaCliente.getRespostaPergCliente().isEmpty()) {
                        perguntaCliente.getRespostaPergCliente().add(new RespostaPergCliente());
                    }
                    questionarioPerguntaClienteVO.setPerguntaCliente(perguntaCliente);
                    questionarioPerguntaClientes.add(questionarioPerguntaClienteVO);
                }
        );

        return questionarioPerguntaClientes;
    }

    private PerguntaCliente getPerguntaCliente(QuestionarioPergunta objExistente) {
        PerguntaCliente perguntaCliente = new PerguntaCliente();
        perguntaCliente.setPerguntaCodigo(objExistente.getPergunta().getCodigo());
        perguntaCliente.setObrigatoria(objExistente.getObrigatoria());
        perguntaCliente.setDescricao(
                objExistente.getPergunta().getDescricao());
        perguntaCliente.setTipoPergunta(
                objExistente.getPergunta().getTipoPergunta());

        if (objExistente.getPergunta().getTipoPergunta().equals("SE")
                || objExistente.getPergunta().getTipoPergunta().equals("SN")) {
            perguntaCliente.setSimples(
                    true);
        }

        if (objExistente.getPergunta().getTipoPergunta().equals("ME")) {
            perguntaCliente.setMultipla(
                    true);
        }

        if (objExistente.getPergunta().getTipoPergunta().equals("TE")) {
            perguntaCliente.setTextual(
                    true);
        }
        return perguntaCliente;
    }

    @Override
    @Transactional(transactionManager = "zwClientTransactionManager")
    public QuestionarioClienteDTO obterBVVisitante(Integer empresa) throws ServiceException {

        Empresa empresaObj = empresaRepository.findById(empresa).orElseThrow(() -> new ServiceException("empresa-nao-encontrada", "Empresa não encontrada com o id informado!"));
        if (empresaObj.getQuestionarioPrimeiraVisita() == null ||
                empresaObj.getQuestionarioPrimeiraVisita().getCodigo() != 0) {
            return fillQuestionarioCliente(
                    TipoBV.MA,
                    new QuestionarioCliente(),
                    empresaObj.getQuestionarioPrimeiraVisita());

        }

        List<ConfiguracaoSistema> configuracoesSistema = configuracaoSistemaRepository.findAll();
        if(configuracoesSistema.isEmpty()){
            throw new ServiceException("configuracao-sistema-nao-encontrada", "Nenhuma configuração do sistema foi encontrada");
        }
        if(configuracoesSistema.size() > 1){
            throw new ServiceException("configuracao-sistema-mais-um-registro", "Mais de um registro foi encontrado em configuracaosistema");
        }

        ConfiguracaoSistema configuracaoSistema = configuracoesSistema.get(0);
        if (configuracaoSistema.getQuestionarioPrimeiraVisita() == null ||
                configuracaoSistema.getQuestionarioPrimeiraVisita().getCodigo() != 0) {
            return fillQuestionarioCliente(
                    TipoBV.MA,
                    new QuestionarioCliente(),
                    configuracaoSistema.getQuestionarioPrimeiraVisita());

        }

        throw new ServiceException(
                    "questionario-nao-definidos-empresa",
                    "Os questionários não foram definidos na empresa."
            );
    }

    public void notificarWebhookBV(QuestionarioClienteDTO questionarioClienteDTO) throws ServiceException {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
            StringBuilder urlZw = new StringBuilder(clientDiscoveryDataDTO.getServiceUrls().getZwUrl());
            urlZw.append("/prest/negociacao")
                    .append("?key=").append(requestService.getUsuarioAtual().getChave())
                    .append("&operacao=WEBHOOK_BV")
                    .append("&questionario=").append(questionarioClienteDTO.getCodigo())
                    .append("&usuario=").append(requestService.getUsuarioAtual().getCodZw())
                    .append("&cliente=").append(questionarioClienteDTO.getCliente());

            ResponseEntity<String> responseZw = httpServico.doJson(
                    urlZw.toString(),
                    null, HttpMethod.POST, "");

            JSONObject jsonObject = new JSONObject(responseZw);
            if (!jsonObject.optString("erro").isEmpty()) {
                String msg = "Falha ao tentar notificar o boletim de visitas do cliente! " + jsonObject.optString("erro");
                System.out.println(msg);
                throw new ServiceException(msg);
            }

        } catch (Exception ex) {}
    }
}
