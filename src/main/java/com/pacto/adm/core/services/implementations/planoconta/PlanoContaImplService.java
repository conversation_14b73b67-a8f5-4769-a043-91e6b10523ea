package com.pacto.adm.core.services.implementations.planoconta;

import com.pacto.adm.core.dao.interfaces.nativerepositories.planoconta.PlanoContaNativeRepository;
import com.pacto.adm.core.dao.interfaces.nativerepositories.produto.ProdutoNativeRepository;
import com.pacto.adm.core.dto.filtros.FiltroPlanoContaDespesasJSON;
import com.pacto.adm.core.dto.filtros.FiltroPlanoContaProdutoJSON;
import com.pacto.adm.core.entities.PlanoConta;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.services.interfaces.planoconta.PlanoContaService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PlanoContaImplService implements PlanoContaService {

    private final ProdutoNativeRepository produtoNativeRepository;
    private final PlanoContaNativeRepository planoContaNativeRepository;

    public PlanoContaImplService(ProdutoNativeRepository produtoNativeRepository, PlanoContaNativeRepository planoContaNativeRepository) {
        this.produtoNativeRepository = produtoNativeRepository;
        this.planoContaNativeRepository = planoContaNativeRepository;
    }

    @Override
    public List<Produto> findAllProdutos(FiltroPlanoContaProdutoJSON filtroPlanoContaProdutoJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Produto> produtos = produtoNativeRepository.findAllProdutos(filtroPlanoContaProdutoJSON, paginadorDTO);
            return produtos;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<PlanoConta> findAllDespesas(FiltroPlanoContaDespesasJSON filtroPlanoContaDespesasJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<PlanoConta> planoContas = planoContaNativeRepository.consultarTodosPlanoContaComValoresPorFiltro(filtroPlanoContaDespesasJSON, paginadorDTO);
            return planoContas;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
