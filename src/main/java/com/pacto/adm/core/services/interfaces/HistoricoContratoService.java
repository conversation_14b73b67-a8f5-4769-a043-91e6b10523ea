package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.AtestadoContratoDTO;
import com.pacto.adm.core.dto.HistoricoContratoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.Date;
import java.util.List;

public interface HistoricoContratoService {

    List<HistoricoContratoDTO> findAllByContrato(Integer codContrato, PaginadorDTO paginadorDTO) throws ServiceException;

    HistoricoContratoDTO findById(Integer id) throws ServiceException;

    Boolean inicializarDadosHistoricoContratoAtestado(AtestadoContratoDTO atestadoContratoDTO) throws Exception;

    void alterarUltimoHistorico(Integer codigo, Date dataTerminoRetorno) throws Exception;

    boolean obterHistoricoContratoPorCodigoContratoDescricao(Integer codContrato, String descricao) throws Exception;
}
