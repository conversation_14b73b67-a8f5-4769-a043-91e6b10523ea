package com.pacto.adm.core.services.implementations.metafinanceiraempresa;

import com.pacto.adm.core.dao.interfaces.repositories.metafinanceiraempresa.MetaFinanceiraEmpresaRepository;
import com.pacto.adm.core.dto.filtros.metafinanceiraempresa.FiltroMetaFinanceiraEmpresaJSON;
import com.pacto.adm.core.entities.metafinanceira.MetaFinanceiraEmpresa;
import com.pacto.adm.core.services.interfaces.metafinanceiraempresa.MetaFinanceiraEmpresaService;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MetaFinanceiraEmpresaServiceImpl implements MetaFinanceiraEmpresaService {

    private final MetaFinanceiraEmpresaRepository metaFinanceiraEmpresaRepository;

    public MetaFinanceiraEmpresaServiceImpl(MetaFinanceiraEmpresaRepository metaFinanceiraEmpresaRepository) {
        this.metaFinanceiraEmpresaRepository = metaFinanceiraEmpresaRepository;
    }

    @Override
    public List<MetaFinanceiraEmpresa> findByEmpresaIdAndMesAndAno(FiltroMetaFinanceiraEmpresaJSON filtroMetaFinanceiraEmpresaJSON, PaginadorDTO paginadorDTO) {
        Pageable pageable = PageRequest.of(0, 10);
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(10L);
            }
            pageable = PageRequest.of(paginadorDTO.getPage().intValue(), pageable.getPageSize());
        }
        Page<MetaFinanceiraEmpresa> metaFinanceiraEmpresaPage = metaFinanceiraEmpresaRepository.findByMesAndAnoAndEmpresaCodigoIn(
                filtroMetaFinanceiraEmpresaJSON.getMes(), filtroMetaFinanceiraEmpresaJSON.getAno(), filtroMetaFinanceiraEmpresaJSON.getEmpresas(), pageable
        );
        if (paginadorDTO != null) {
            paginadorDTO.setPage((long) metaFinanceiraEmpresaPage.getNumber());
            paginadorDTO.setSize((long) metaFinanceiraEmpresaPage.getSize());
            paginadorDTO.setQuantidadeTotalElementos(metaFinanceiraEmpresaPage.getTotalElements());
        }
        return metaFinanceiraEmpresaPage.getContent();
    }
}
