package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.ClassificacaoDao;
import com.pacto.adm.core.entities.Classificacao;
import com.pacto.adm.core.services.interfaces.ClassificacaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ClassificacaoServiceImpl implements ClassificacaoService {

    @Autowired
    ClassificacaoDao classificacaoDao;

    @Override
    public List<Classificacao> findAll() throws Exception {
        return classificacaoDao.findAll();
    }
}
