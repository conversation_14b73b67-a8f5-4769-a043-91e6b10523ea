package com.pacto.adm.core.services.interfaces;


import com.pacto.adm.core.dto.AvisoInternoDTO;
import com.pacto.adm.core.dto.PerfilAcessoDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface AvisoInternoService {

    List<AvisoInternoDTO> listarAvisosParaTodos() throws ServiceException;

    List<AvisoInternoDTO> listarAvisosByUsuario(Integer empresa) throws ServiceException;

    AvisoInternoDTO listarAviso(Integer codAviso) throws ServiceException;

    AvisoInternoDTO saveOrUpdateAvisoInterno(Integer empresa, AvisoInternoDTO avisoInternoDto) throws ServiceException;

    void excluirAvisoInterno(Integer codAvisoInterno) throws Exception;

    void inativarAvisoInterno(Integer codAvisoInterno) throws Exception;

    List<UsuarioDTO> listarUsuarios() throws ServiceException;

    List<PerfilAcessoDTO> listarPerfisAcesso() throws ServiceException;
}
