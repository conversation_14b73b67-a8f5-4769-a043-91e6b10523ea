package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ReciboPagamentoAdapter;
import com.pacto.adm.core.dao.interfaces.MovProdutoParcelaDao;
import com.pacto.adm.core.dao.interfaces.ReciboPagamentoDao;
import com.pacto.adm.core.dto.ReciboPagamentoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.MovProdutoParcela;
import com.pacto.adm.core.entities.ReciboPagamento;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.objects.EstornoRecibo;
import com.pacto.adm.core.services.interfaces.ReciboPagamentoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class ReciboPagamentoServiceImpl implements ReciboPagamentoService {

    @Autowired
    private MovProdutoParcelaDao movProdutoParcelaDao;
    @Autowired
    private ReciboPagamentoDao reciboPagamentoDao;
    @Autowired
    private ReciboPagamentoAdapter reciboPagamentoAdapter;

    public List<ReciboPagamentoDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<ReciboPagamento> recibosPagamento = reciboPagamentoDao.findAllByPessoa(codPessoa);

            List<ReciboPagamentoDTO> reciboPagamentoDTOS = reciboPagamentoAdapter.toDtos(recibosPagamento);

            if (paginadorDTO != null && paginadorDTO.getPage() != null && paginadorDTO.getSize() != null) {
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                if (ultimoRegistro > reciboPagamentoDTOS.size()) {
                    reciboPagamentoDTOS = reciboPagamentoDTOS.subList(primeiroPaginacao, reciboPagamentoDTOS.size());
                } else {
                    reciboPagamentoDTOS = reciboPagamentoDTOS.subList(primeiroPaginacao, ultimoRegistro);
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) reciboPagamentoDTOS.size());
            }
            return reciboPagamentoDTOS;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ReciboPagamentoDTO> findAllByCodParcela(Integer codParcela) throws ServiceException {
        try {
            List<MovProdutoParcela> movProdutoParcelas = movProdutoParcelaDao.findAllByParcela(codParcela);

            Map<Integer, ReciboPagamento> mapaRecibos = new HashMap<>();
            for (MovProdutoParcela mpp : movProdutoParcelas) {
                if (mpp.getReciboPagamento() != null) {
                    ReciboPagamento reciboPagamento = mapaRecibos.get(mpp.getReciboPagamento().getCodigo());
                    if (reciboPagamento == null) {
                        reciboPagamento = mpp.getReciboPagamento();
                        mapaRecibos.put(reciboPagamento.getCodigo(), reciboPagamento);
                    }
                }
            }

            return reciboPagamentoAdapter.toDtos(new ArrayList<>(mapaRecibos.values()));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void estornarNotasFiscais(EstornoRecibo estornoReciboVO, Contrato contrato) {

    }
}
