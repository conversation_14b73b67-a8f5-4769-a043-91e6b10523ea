package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.GrupoDao;
import com.pacto.adm.core.entities.Grupo;
import com.pacto.adm.core.services.interfaces.GrupoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GrupoServiceImpl implements GrupoService {

    @Autowired
    GrupoDao grupoDao;

    @Override
    public List<Grupo> findAll() throws Exception {
        return grupoDao.findAll();
    }
}
