package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.AcessoClienteDTO;
import com.pacto.adm.core.dto.ColetorDTO;
import com.pacto.adm.core.dto.InicioDadosAcessoDTO;
import com.pacto.adm.core.entities.AcessoCliente;
import com.pacto.adm.core.entities.Coletor;

import java.util.List;

public interface RegistroDeAcessoClienteService {
 InicioDadosAcessoDTO iniciaDadosRegistroDeAcessoCliente(Integer matricula) throws Exception;
 List<ColetorDTO> buscaColetor(Integer localAcesso, boolean desativado) throws Exception;
 List<AcessoClienteDTO> listarRegistraAcessoManualDia(AcessoClienteDTO registroDeAcessoManual) throws Exception;
 List<AcessoCliente> listarAcessoClienteDTO(AcessoClienteDTO acessoClienteDTO) throws Exception;
 String registroDeAcessoManual(AcessoClienteDTO acessoClienteDTO) throws Exception;
 String editarAcessoCliente(AcessoClienteDTO acessoClienteDTO)throws Exception;
}
