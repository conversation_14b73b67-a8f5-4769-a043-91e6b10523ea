package com.pacto.adm.core.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pacto.adm.core.adapters.PlanoAdapter;
import com.pacto.adm.core.dao.interfaces.ClienteDao;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoSistemaDao;
import com.pacto.adm.core.dao.interfaces.ContratoDao;
import com.pacto.adm.core.dao.interfaces.EmpresaDao;
import com.pacto.adm.core.dao.interfaces.HistoricoContratoDao;
import com.pacto.adm.core.dao.interfaces.PlanoDao;
import com.pacto.adm.core.dto.ClienteRestricaoDTO;
import com.pacto.adm.core.dto.ConfigConsultaTurmaDTO;
import com.pacto.adm.core.dto.PlanoDTO;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.dto.filtros.FiltroNegociacaoPlanoJSON;
import com.pacto.adm.core.dto.negociacao.AgendaTurmaDTO;
import com.pacto.adm.core.dto.negociacao.CheckNegociacaoDTO;
import com.pacto.adm.core.dto.negociacao.ClienteNegociacaoDTO;
import com.pacto.adm.core.dto.negociacao.ConfigsContratoDTO;
import com.pacto.adm.core.dto.negociacao.HorarioTurmaAgendaDTO;
import com.pacto.adm.core.dto.negociacao.NegociacaoDTO;
import com.pacto.adm.core.dto.negociacao.PlanoDuracaoCreditoDTO;
import com.pacto.adm.core.dto.negociacao.PlanoDuracaoDTO;
import com.pacto.adm.core.dto.negociacao.PlanoHorarioDTO;
import com.pacto.adm.core.dto.negociacao.PlanoModalidadeDTO;
import com.pacto.adm.core.dto.negociacao.PlanoModalidadeTurmaDTO;
import com.pacto.adm.core.dto.negociacao.ResultadoNegociacaoDTO;
import com.pacto.adm.core.dto.negociacao.SimuladoDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.ConfiguracaoSistema;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.contrato.HistoricoContrato;
import com.pacto.adm.core.entities.contrato.Plano;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.enumerador.DisponibilidadeTurmaEnum;
import com.pacto.adm.core.enumerador.TipoHorarioCreditoTreinoEnum;
import com.pacto.adm.core.services.implementations.ClienteRestricaoServiceImpl;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.adm.core.services.interfaces.NegociacaoService;
import com.pacto.adm.core.services.interfaces.SescDfService;
import com.pacto.adm.core.services.interfaces.SescGoService;
import com.pacto.config.enumerador.DiaSemana;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.JSONMapper;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class NegociacaoServiceImpl implements NegociacaoService {

    @Autowired
    private PlanoDao planoDao;
    @Autowired
    private ContratoDao contratoDao;
    @Autowired
    private ConfiguracaoSistemaDao configuracaoSistemaDao;
    @Autowired
    private ClienteDao clienteDao;
    @Autowired
    private PlanoAdapter planoAdapter;
    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private HttpServico httpServico;
    @Autowired
    private RequestService requestService;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private HistoricoContratoDao historicoContratoDao;
    @Autowired
    private ClienteRestricaoServiceImpl clienteRestricaoService;
    @Autowired
    private SescDfService sescDfService;
    @Autowired
    private SescGoService sescGoService;
    private static Map<String, String> mapUrl = new HashMap<>();


    @Override
    public List<PlanoDTO> consultarPlanos(Boolean incluirBolsa, Integer planoForcar,
                                          FiltroNegociacaoPlanoJSON filtros, Integer codigoCliente,
                                          Integer contratoRenovar) throws ServiceException {
        try {
            ConfiguracaoSistema configuracaoSistema = configuracaoSistemaDao.get();
            List<Plano> all = planoDao.findVigentesByEmpresa(requestService.getEmpresaId(),
                    planoForcar,
                    configuracaoSistema.getControleAcessoMultiplasEmpresasPorPlano(),
                    filtros, codigoCliente, contratoRenovar);
            return planoAdapter.toDtos(all.stream()
                    .filter(plano -> (incluirBolsa || !plano.getBolsa()))
                    .collect(Collectors.toList()));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public NegociacaoDTO montarEscolhasNegociacao(Integer plano, Integer empresaId, SituacaoContratoEnum situacaoContrato, Integer contratoBaseado) throws ServiceException {
        try {
            NegociacaoDTO negociacao = new NegociacaoDTO();
            Plano planoById = planoDao.findById(plano);
            if (planoById != null) {
                negociacao.setBolsa(planoById.getBolsa());
                negociacao.setNrVezesparcelarProduto(planoById.getNrVezesparcelarProduto());
                negociacao.setNrVezesParcelarAdesao(planoById.getNrVezesParcelarAdesao());
                negociacao.setCobrarAdesaoSeparada(planoById.getCobrarAdesaoSeparada()
                        && planoById.getNrVezesParcelarAdesao() != null && planoById.getNrVezesParcelarAdesao() > 0);
                negociacao.setProRataObrigatorio(planoById.getProRataObrigatorio());
                negociacao.setCobrarProdutoSeparado(planoById.getCobrarProdutoSeparado() &&
                        planoById.getNrVezesparcelarProduto() != null && planoById.getNrVezesparcelarProduto() > 0);
                Calendar dataCalendar = Calendario.getInstance();
                dataCalendar.setTime(Calendario.hoje());
                Integer diaAtual = dataCalendar.get(Calendar.DAY_OF_MONTH);
                negociacao.setDiasProRata(planoDao.diasProRata(diaAtual, planoById.getDiasVencimentoProrata()));
                negociacao.setRecorrencia(planoById.getRecorrencia());
                negociacao.setVendaCreditoTreino(planoById.getVendaCreditoTreino());
                negociacao.setCreditoSessao(planoById.getCreditoSessao());
                negociacao.setCreditoTreinoNaoCumulativo(planoById.getCreditoTreinoNaoCumulativo());
                if (planoById.getRecorrencia()) {
                    negociacao.setDiaSugerido(999);
                    negociacao.setDiasCartao(planoDao.diasCartao(plano, diaAtual));
                    negociacao.setDeveGerarParcelasComValorDiferente(planoById.getRecorrencia()
                            && planoById.getPlanoRecorrencia() != null
                            && planoById.getPlanoRecorrencia().getGerarParcelasValorDiferente()
                            && !planoById.getPlanoRecorrencia().getParcelas().isEmpty()
                            && (!situacaoContrato.equals(SituacaoContratoEnum.RN) || planoById.getPlanoRecorrencia().getGerarParcelasValorDiferenteRenovacao()));
                } else {
                    negociacao.setDeveGerarParcelasComValorDiferente(false);
                }
            }
            negociacao.setPlano(plano);
            negociacao.setDuracoes(planoDao.duracoesPorPlano(plano, false));
            boolean tudoHorarioLivre = true;
            if (planoById.getVendaCreditoTreino()) {
                Set<TipoHorarioCreditoTreinoEnum> horarios = new HashSet<>();
                for (PlanoDuracaoDTO planoDuracaoDTO : negociacao.getDuracoes()) {
                    if (planoDuracaoDTO.getCreditos() == null) {
                        continue;
                    }
                    for (PlanoDuracaoCreditoDTO planoDuracaoCreditoDTO : planoDuracaoDTO.getCreditos()) {
                        TipoHorarioCreditoTreinoEnum tipoEnum = TipoHorarioCreditoTreinoEnum.getTipo(planoDuracaoCreditoDTO.getTipoHorarioCreditoTreino());
                        if (tipoEnum != null) {
                            if (tipoEnum == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA) {
                                tudoHorarioLivre = false;
                            }
                            horarios.add(tipoEnum);
                        }
                    }
                }
                if (horarios.isEmpty()) {
                    negociacao.setHorarios(planoDao.horariosPorPlano(plano, false));
                } else {
                    List<PlanoHorarioDTO> horariosCreditos = new ArrayList<>();
                    for (TipoHorarioCreditoTreinoEnum tipo : horarios) {
                        horariosCreditos.add(new PlanoHorarioDTO(tipo.getCodigo(), tipo.getDescricao(), tipo != TipoHorarioCreditoTreinoEnum.HORARIO_TURMA));
                    }
                    negociacao.setHorarios(horariosCreditos);
                }

            } else {
                negociacao.setHorarios(planoDao.horariosPorPlano(plano, false));
            }
            contratoBaseado = UteisValidacao.emptyNumber(contratoBaseado) ? 0 : planoDao.contratoEPlano(plano, contratoBaseado);
            negociacao.setModalidades(planoDao.modalidadesPorPlano(plano,
                    negociacao.getVendaCreditoTreino() && tudoHorarioLivre,
                    contratoBaseado));
            negociacao.setPacotes(planoDao.pacotesPorPlano(plano));
            negociacao.setProdutos(planoDao.produtodosPorPlano(plano, situacaoContrato));
            negociacao.setDescontos(planoDao.descontosPorPlano(empresaId));
            negociacao.setContratoBaseado(contratoBaseado);
            contratoDao.montarSugestoesPlanoNegociacao(requestService.getUsuarioAtual().getCodZw(), negociacao, situacaoContrato);
            return negociacao;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public List<AgendaTurmaDTO> montarAgendaTurma(Integer modalidade,
                                                  Integer nivel,
                                                  Integer professor,
                                                  String periodo,
                                                  String disponibilidade,
                                                  Integer cliente,
                                                  String inicio,
                                                  Integer empresa) throws ServiceException {
        Date dataBase = Calendario.hoje();
        if (inicio != null) {
            try {
                dataBase = Uteis.getDate(inicio, "dd/MM/yyyy");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return planoDao.montarAgendaTurma(modalidade, nivel, professor, periodo, disponibilidade, cliente, null, dataBase, empresa);
    }

    public ConfigConsultaTurmaDTO configConsultaTurma(Integer empresa, Integer modalidade) throws ServiceException {
        return planoDao.configAgenda(empresa, modalidade);
    }

    @Override
    public List<ClienteNegociacaoDTO> consultarClientesNegociacao(String nome) throws ServiceException {
        return clienteDao.consultarClientesSimplificado(nome, this.requestService.getEmpresaId());
    }

    @Override
    public CheckNegociacaoDTO checkNegociacao(Integer codigoCliente, Integer codigoContrato, Integer empresa, Boolean novaLinha) throws ServiceException {
        return checkNegociacao(codigoCliente, codigoContrato, empresa, novaLinha, null);
    }

    private void atualizarClienteSesc(Integer codigoCliente, Integer idEmpresa) {
        try {
            final Empresa empresa = empresaDao.findById(idEmpresa);
            final ConfiguracaoSistema configuracaoSistema = configuracaoSistemaDao.get();
            final Cliente cliente = clienteDao.findById(codigoCliente);

            if (cliente != null) {
                if (empresa != null && empresa.getUsarSescDf()) {
                    sescDfService.atualizarCliente(cliente.getPessoa().getCpf(), empresa.getTokenSescDf());
                }

                if (!UteisValidacao.emptyString(configuracaoSistema.getChavePublicaSESC()) &&
                        !UteisValidacao.emptyString(configuracaoSistema.getUsuarioApiSescGo())) {
                    sescGoService.atualizarCliente(cliente.getPessoa().getCpf(), cliente.getPessoa().getCodigo(), configuracaoSistema);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public ResultadoNegociacaoDTO gravar(ConfigsContratoDTO configs) throws ServiceException {
        Plano planoById = null;
        try {
            planoById = planoDao.findById(configs.getPlano());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (planoById != null && !UteisValidacao.emptyNumber(configs.getContratoBase()) && !planoById.getRecorrencia()) {
            configs.setVencimentoCartao(null);
        }
        ResultadoNegociacaoDTO resultado = new ResultadoNegociacaoDTO();
        preencherVencimentoCartao(configs);
        JSONObject jsonObject = contrato(configs, "gravarNegociacao");
        if (jsonObject.has("contrato")) {
            Uteis.logar("CÓDIGO DO CONTRATO GERADO JSON: " + jsonObject.getInt("contrato"));
        } else {
            Uteis.logar("CÓDIGO DO CONTRATO GERADO JSON: NÃO FOI RETORNADO CÓDIGO DO CONTRATO!!!");
        }
        resultado.setContrato(jsonObject.getInt("contrato"));
        if (resultado.getContrato() != null) {
            Uteis.logar("CÓDIGO DO CONTRATO GERADO RESULTADO: " + resultado.getContrato());
        } else {
            Uteis.logar("CÓDIGO DO CONTRATO GERADO RESULTADO: NÃO FOI RETORNADO CÓDIGO DO CONTRATO!!!");
        }
        if (configs.getGerarLink()) {
            String link = jsonObject.getString("link");
            resultado.setLink(link);
            resultado.setWhatsapp(linkCompartilharWhatsapp(configs.getCliente(), link));
        }
        try {
            fixarAulas(configs);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e.getMessage());
        }
        return resultado;
    }


    private void fixarAulas(ConfigsContratoDTO configs) throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
        String treinoApiUrl = clientDiscoveryDataDTO.getServiceUrls().getTreinoApiUrl();
        if (UteisValidacao.emptyString(treinoApiUrl)) {
            throw new Exception("TreinoApiUrl não informado");
        }
        List<Integer> horariosSelecionados = new ArrayList<>();
        for (PlanoModalidadeDTO planoModalidadeDTO : configs.getModalidades()) {
            for (PlanoModalidadeTurmaDTO horarioTurmaDTO : planoModalidadeDTO.getTurmas()) {
                horariosSelecionados.add(horarioTurmaDTO.getCodigo());
            }
        }

        if (!horariosSelecionados.isEmpty()) {
            Cliente cliente = clienteDao.findById(configs.getCliente());
            List<Integer> horariosFixar = planoDao.horariosFixar(horariosSelecionados.toArray(new Integer[0]));
            for (Integer horario : horariosFixar) {
                fixarAlunoEmAula(treinoApiUrl, horario, cliente.getCodigoMatricula(), false, null);
            }
        }
    }

    public List<String> validarFixarAulas(Integer aluno, Integer duracao, List<Integer> horariosSelecionados) throws ServiceException {
        List<String> diasAulas = new ArrayList<>();
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
            String treinoApiUrl = clientDiscoveryDataDTO.getServiceUrls().getTreinoApiUrl();
            if (UteisValidacao.emptyString(treinoApiUrl)) {
                throw new Exception("TreinoApiUrl não informado");
            }
            Date ate = Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, 3);
            if (!horariosSelecionados.isEmpty()) {
                Cliente cliente = clienteDao.findById(aluno);
                List<Integer> horariosFixar = planoDao.horariosFixar(horariosSelecionados.toArray(new Integer[0]));
                for (Integer horario : horariosFixar) {
                    diasAulas.addAll(fixarAlunoEmAula(treinoApiUrl, horario, cliente.getCodigoMatricula(), true, ate));
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        Collections.sort(diasAulas);
        return diasAulas;
    }

    private List<String> fixarAlunoEmAula(String treinoApiUrl, Integer aulaHorario, Integer matricula, Boolean validarApenas, Date ate) {
        List<String> aulasCheias = new ArrayList<>();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("empresaId", requestService.getEmpresaId().toString());
            headers.set("Authorization", requestService.getToken());
            String params = "matricula=" + matricula;
            params += "&dataAula=" + Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMdd");
            if (validarApenas && ate != null) {
                params += "&validarAulaCheia=true";
                params += "&dataFinal=" + ate.getTime();
                params += "&tipo=DATA_DETERMINADA";
            } else {
                params += "&dataFinal=null";
                params += "&tipo=FIM_CONTRATO";
            }
            params += "&origem=NE";

            ResponseEntity<String> resposta = httpServico.doJson(
                    treinoApiUrl + "/psec/agenda/turmas/" + aulaHorario + "/fixar-aluno?" + params,
                    null, HttpMethod.PUT, headers);
            if (!resposta.getStatusCode().equals(HttpStatus.OK)) {
                Uteis.logar("Erro ao fixar aluno em aula: " + resposta.getBody());
            }

            if (validarApenas) {
                JSONObject content = new JSONObject(resposta.getBody()).getJSONObject("content");
                if (content != null && content.has("aulasCheias") && !content.getJSONArray("aulasCheias").isEmpty()) {
                    JSONArray jsonArray = content.getJSONArray("aulasCheias");
                    for (int i = 0; i < jsonArray.length(); i++) {
                        aulasCheias.add(jsonArray.getString(i));
                    }
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return aulasCheias;
    }

    public SimuladoDTO simular(ConfigsContratoDTO configs) throws ServiceException {
        try {
            Plano planoById = planoDao.findById(configs.getPlano());
            if (planoById != null && !UteisValidacao.emptyNumber(configs.getContratoBase()) && !planoById.getRecorrencia()) {
                configs.setVencimentoCartao(null);
            }

            preencherVencimentoCartao(configs);

            JSONObject jsonObject = contrato(configs, "simularNegociacao");
            return JSONMapper.getObject(jsonObject, SimuladoDTO.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private void preencherVencimentoCartao(ConfigsContratoDTO configs) throws ServiceException {
        if (configs.getVencimentoCartao() != null && configs.getVencimentoCartao().equals(999)) {
            Date dataBase;

            if (configs.getInicio() != null) {
                dataBase = new Date(configs.getInicio());
            } else if (configs.getDataLancamento() != null) {
                dataBase = new Date(configs.getDataLancamento());
            } else {
                dataBase = Calendario.hoje();
                if (!UteisValidacao.emptyNumber(configs.getContratoBase())) {
                    try {
                        Contrato contratoObj = contratoDao.findById(configs.getContratoBase());
                        if (contratoObj != null && contratoObj.getContratoRecorrencia() != null && contratoObj.getContratoRecorrencia().getDiaVencimentoCartao() != null &&
                                contratoObj.getContratoRecorrencia().getDiaVencimentoCartao() > 0) {
                            Calendar vencimento = Calendario.getInstance();
                            vencimento.set(Calendar.DAY_OF_MONTH, contratoObj.getContratoRecorrencia().getDiaVencimentoCartao());
                            dataBase = vencimento.getTime();
                        }
                    } catch (Exception e) {}
                }
            }

            Calendar dataCalendar = Calendario.getInstance();
            dataCalendar.setTime(dataBase);

            int diaAtual = dataCalendar.get(Calendar.DAY_OF_MONTH);
            configs.setVencimentoCartao(diaAtual);
        }
    }

    public String linkCompartilharWhatsapp(Integer cliente, String link) {
        try {
            List<String> telefones = clienteDao.telefoneCliente(cliente);
            if (!UteisValidacao.emptyList(telefones)) {
                String conteudo = "*Seu link para efetuar o pagamento*\n" +
                        "URL_LINK_PAGAMENTO";
                conteudo = conteudo.replaceAll("URL_LINK_PAGAMENTO", link);

                return "https://web.whatsapp.com/send?phone=55"
                        + Uteis.removerMascara(telefones.get(0)
                                .replaceAll(" ", ""))
                        .replaceAll("\\(", "").replaceAll("\\)", "")
                        + "&text="
                        + URLEncoder.encode(conteudo, "UTF-8")
                        .replaceAll("\\+", "%20");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public JSONObject contrato(ConfigsContratoDTO configs, String op) throws ServiceException {
        try {
            String chave = requestService.getUsuarioAtual().getChave();
            configs.setEmpresa(requestService.getEmpresaId());
            String urlZW = urlZW(chave);
            StringBuilder urlZw = new StringBuilder(urlZW)
                    .append("/prest/negociacao")
                    .append("?key=")
                    .append(chave)
                    .append("&operacao=" + op);
            ObjectMapper objectMapper = new ObjectMapper();
            String objs = objectMapper.writeValueAsString(configs);
            ResponseEntity<String> responseZw = httpServico.doJson(
                    urlZw.toString(), objs
                    , HttpMethod.POST, "");
            JSONObject jsonObject = new JSONObject(new JSONObject(responseZw).getString("body")).getJSONObject("result");
            if (jsonObject.has("erro")) {
                throw new Exception(jsonObject.getString("erro"));
            }
            return jsonObject;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private String urlZW(String chave) throws Exception {
        String url = mapUrl.get(chave);
        if (url == null) {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
            url = clientDiscoveryDataDTO.getServiceUrls().getZwUrl();
            mapUrl.put(chave, url);
        }
        return url;
    }

    public CheckNegociacaoDTO checkNegociacao(Integer codigoCliente, Integer codigoContrato, Integer empresa, Boolean novaLInha, Boolean verificarEmpresaEContratoResponsavelRematricula) throws ServiceException {
        atualizarClienteSesc(codigoCliente, empresa);

        final CheckNegociacaoDTO checkNegociacao;
        if (verificarEmpresaEContratoResponsavelRematricula != null && verificarEmpresaEContratoResponsavelRematricula) {
            checkNegociacao = contratoDao.checkInicialNegociacaoAluno(requestService.getUsuarioAtual().getCodZw(),
                    codigoCliente, empresa, codigoContrato, novaLInha, verificarEmpresaEContratoResponsavelRematricula);
        } else {
            checkNegociacao = contratoDao.checkInicialNegociacaoAluno(requestService.getUsuarioAtual().getCodZw(),
                    codigoCliente, empresa, codigoContrato, novaLInha);
        }

        if (!UteisValidacao.emptyNumber(codigoCliente)) {
            checkNegociacao.setValorParcelasAberto(clienteDao.valorParcelasAberto(codigoCliente));
            checkNegociacao.setNivelAluno(nivelAluno(codigoCliente));
            if (!UteisValidacao.emptyNumber(checkNegociacao.getCodigoContratoRematricula())
                    || !UteisValidacao.emptyNumber(checkNegociacao.getCodigoContratoRenovacao())) {
                Integer contratoBaseado = UteisValidacao.emptyNumber(checkNegociacao.getCodigoContratoRematricula()) ?
                        checkNegociacao.getCodigoContratoRenovacao() : checkNegociacao.getCodigoContratoRematricula();
                List<AgendaTurmaDTO> agendaTurmaDTOS = planoDao.montarAgendaTurma(null, null, null,
                        null, DisponibilidadeTurmaEnum.disponiveis.name(), codigoCliente, contratoBaseado, Calendario.hoje(), empresa);
                checkNegociacao.setHorariosTurma(new ArrayList<>());
                for (AgendaTurmaDTO agendaTurmaDTO : agendaTurmaDTOS) {
                    Map<DiaSemana, List<HorarioTurmaAgendaDTO>> aulasDiaSemana = agendaTurmaDTO.getAulasDiaSemana();
                    checkNegociacao.getHorariosTurma().addAll(aulasDiaSemana.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                }
            }
            if (!UteisValidacao.emptyNumber(checkNegociacao.getCodigoContratoRematricula())) {
                Date limiteRematricula = dataLimiteRematricula(checkNegociacao.getCodigoContratoRematricula());
                checkNegociacao.setLimiteDataRematricula(limiteRematricula == null ? null : limiteRematricula.getTime());
            }

        }

        verificarClienteRestricoes(checkNegociacao, codigoCliente);
        return checkNegociacao;
    }

    private Date dataLimiteRematricula(Integer contrato) {
        try {
            HistoricoContrato historicoContrato = historicoContratoDao.obterUltimoHistoricoContratoPorContratoTipoHistorico(contrato, null);
            if (historicoContrato == null) {
                return null;
            }
            if (historicoContrato.getTipoHistorico().equals("CA")) {
                Contrato contratoObj = contratoDao.findById(contrato);
                return contratoObj == null ? null : contratoObj.getVigenciaAteAjustada();
            }
            return historicoContrato.getDataFinalSituacao();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    public Integer nivelAluno(Integer aluno) {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
            String treinoApiUrl = clientDiscoveryDataDTO.getServiceUrls().getTreinoApiUrl();
            if (UteisValidacao.emptyString(treinoApiUrl)) {
                throw new Exception("TreinoApiUrl não informado");
            }

            HttpHeaders headers = new HttpHeaders();
            headers.set("empresaId", requestService.getEmpresaId().toString());
            headers.set("Authorization", requestService.getToken());


            ResponseEntity<String> resposta = httpServico.doJson(
                    treinoApiUrl + "/psec/niveis/aluno/" + aluno,
                    null, HttpMethod.GET, headers);

            JSONObject jsonResp = new JSONObject(resposta.getBody());
            return jsonResp.optInt("content");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    private void verificarClienteRestricoes(CheckNegociacaoDTO checkNegociacao, Integer codigoCliente) {
        if (UteisValidacao.emptyNumber(codigoCliente)) {
            return;
        }
        try {
            Empresa empresa = empresaDao.findById(requestService.getEmpresaId());
            if (empresa.getUtilizaGestaoClientesComRestricoes()) {
                Cliente cliente = clienteDao.findById(codigoCliente);
                if (cliente != null) {
                    List<ClienteRestricaoDTO> clienteRestricaoDTOS = clienteRestricaoService.findByCodigoMatricula(cliente.getCodigoMatricula());
                    checkNegociacao.setClienteRestricoes(clienteRestricaoDTOS);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public HashMap<String, Object> chamarServletCarteirinhaCliente(JSONObject body) throws ServiceException {
        try {
            String tipoOperacao = body.getString("tipoOperacao");
            String codigoCliente = body.optString("codigoCliente", null);
            String codigoEmpresa = body.optString("codigoEmpresa");
            String chave = body.optString("chave");
            String usuario = String.valueOf(requestService.getUsuarioAtual().getCodZw());

            String endpointServletZw = discoveryService.getClientDiscovery().getServiceUrls().getZwUrl()
                    + "/prest/pactoprint/impressoesCarteirinhas"
                    + "?tipoOperacao=" + tipoOperacao
                    + (codigoCliente != null ? "&codigoCliente=" + codigoCliente : "")
                    + "&key=" + chave
                    + "&empresa=" + codigoEmpresa
                    + "&usuario=" + usuario;

            ResponseEntity<String> response = httpServico.doJson(endpointServletZw, body.toString(), HttpMethod.POST, "");

            if (response.toString().contains("ERRO_REQUISICAO_SERVLET")) {
                String responseBody = response.getBody();
                String errorMessage = responseBody.substring(responseBody.indexOf("Erro:") + 5).trim();
                throw new ServiceException(errorMessage);
            }

            JSONObject respostaServletJson = new JSONObject(response.getBody());
            HashMap<String, Object> responseMap = new HashMap<>();

            if (tipoOperacao.equals("CONSULTAR_HISTORICO")) {
                JSONArray historico = respostaServletJson.getJSONArray("historico");
                List<HashMap<String, Object>> historicoList = new ArrayList<>();

                for (int i = 0; i < historico.length(); i++) {
                    JSONObject carteirinhaJson = historico.getJSONObject(i);
                    HashMap<String, Object> carteirinhaMap = new HashMap<>();
                    carteirinhaJson.keys().forEachRemaining(key -> {
                        Object value = carteirinhaJson.isNull(key) ? null : carteirinhaJson.get(key);
                        carteirinhaMap.put(key, value);
                    });
                    historicoList.add(carteirinhaMap);
                }
                responseMap.put("historico", historicoList);

            } else {
                respostaServletJson.keys().forEachRemaining(key -> {
                    Object value = respostaServletJson.isNull(key) ? null : respostaServletJson.get(key);
                    responseMap.put(key, value);
                });
            }

            if (responseMap.containsKey("erro") && !((String) responseMap.get("erro")).isEmpty()) {
                throw new ServiceException((String) responseMap.get("erro"));
            }

            return responseMap;

        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void limparMapUrl() {
        mapUrl.clear();
    }

}
