package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.AluguelArmarioAdapter;
import com.pacto.adm.core.dao.interfaces.AluguelArmarioDao;
import com.pacto.adm.core.dto.AluguelArmarioDTO;
import com.pacto.adm.core.entities.AluguelArmario;
import com.pacto.adm.core.services.interfaces.AluguelArmarioService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AluguelArmarioServiceImpl implements AluguelArmarioService {

    @Autowired
    private MessageSource messageSource;
    @Autowired
    private RequestService requestService;
    @Autowired
    private AluguelArmarioDao aluguelArmarioDao;
    @Autowired
    private AluguelArmarioAdapter aluguelArmarioAdapter;

    public List<AluguelArmarioDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<AluguelArmario> lista = aluguelArmarioDao.findAllByPessoa(codPessoa, paginadorDTO);
            return aluguelArmarioAdapter.toDtos(lista);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Integer totalByCodPessoa(Integer codPessoa) throws ServiceException {
        try {
            return aluguelArmarioDao.totalByPessoa(codPessoa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

}
