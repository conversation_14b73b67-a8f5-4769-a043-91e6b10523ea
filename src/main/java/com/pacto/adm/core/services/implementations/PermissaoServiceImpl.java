package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.PermissaoDao;
import com.pacto.adm.core.entities.Permissao;
import com.pacto.adm.core.services.interfaces.PermissaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PermissaoServiceImpl implements PermissaoService {

    @Autowired
    PermissaoDao permissaoDao;

    @Override
    public List<Permissao> findAllByPerfilAcesso(Integer perfilAcesso) {
        return permissaoDao.findAllByPerfilAcesso(perfilAcesso);
    }
}
