package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.ClienteObservacaoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.json.JSONObject;

import java.util.List;

public interface ClienteObservacaoService {

    List<ClienteObservacaoDTO> findAllByCodPessoa(Integer codPessoa, JSONObject filtros,
                                                  PaginadorDTO paginadorDTO) throws ServiceException;

    Integer totalByCodPessoa(Integer codPessoa) throws ServiceException;

    ClienteObservacaoDTO saveOrUpdate(ClienteObservacaoDTO dto, Integer codigo) throws ServiceException;

    void excluir(Integer codigo) throws Exception;

    ClienteObservacaoDTO obter(Integer codigo) throws Exception;

    List<ClienteObservacaoDTO> findAllByCodMatricula(Integer codMatricula) throws ServiceException;

    List<ClienteObservacaoDTO> buscarObservacaoTelaCliente(Integer codPessoa, String matricula,
                                                           JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClienteObservacaoDTO> findAllByClienteTreino(Integer codClienteTreino) throws ServiceException;
}
