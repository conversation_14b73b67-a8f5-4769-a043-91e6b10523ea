package com.pacto.adm.core.services.interfaces.justificativaoperacao;

import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.entities.contrato.JustificativaOperacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface JustificativaOperacaoService {
    List<JustificativaOperacao> contratosCanceladosTransferidosOutroAluno(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException;
}
