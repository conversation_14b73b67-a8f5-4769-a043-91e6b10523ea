package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.ConfigConsultaTurmaDTO;
import com.pacto.adm.core.dto.PlanoDTO;
import com.pacto.adm.core.dto.filtros.FiltroNegociacaoPlanoJSON;
import com.pacto.adm.core.dto.negociacao.*;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import com.pacto.config.exceptions.ServiceException;

import org.json.JSONObject;

import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;

public interface NegociacaoService {

    List<PlanoDTO> consultarPlanos(Boolean incluirBolsa, Integer planoForcar, FiltroNegociacaoPlanoJSON filtros,
                                   Integer codigoCliente,
                                   Integer contratoRenovar) throws ServiceException;

    NegociacaoDTO montarEscolhasNegociacao(Integer plano, Integer empresaId, SituacaoContratoEnum situacaoContrato, Integer contratoBaseado) throws ServiceException;

    SimuladoDTO simular(ConfigsContratoDTO configs) throws ServiceException;

    ResultadoNegociacaoDTO gravar(ConfigsContratoDTO configs) throws ServiceException;

    List<AgendaTurmaDTO> montarAgendaTurma(Integer modalidade,
                                           Integer nivel,
                                           Integer professor,
                                           String periodo,
                                           String disponibilidade,
                                           Integer cliente,
                                           String inicio,
                                           Integer empresa) throws ServiceException;

    ConfigConsultaTurmaDTO configConsultaTurma(Integer empresa, Integer modalidade) throws ServiceException;

    List<ClienteNegociacaoDTO> consultarClientesNegociacao(String nome) throws ServiceException;

    CheckNegociacaoDTO checkNegociacao(Integer codigoCliente, Integer codigoContrato, Integer empresa, Boolean novaLinha) throws ServiceException;

    CheckNegociacaoDTO checkNegociacao(Integer codigoCliente, Integer codigoContrato, Integer empresa, Boolean novaLinha, Boolean verificarEmpresaEContratoResponsavelRematricula) throws ServiceException;

    List<String> validarFixarAulas(Integer aluno, Integer duracao, List<Integer> horariosSelecionados) throws ServiceException;

    HashMap<String, Object> chamarServletCarteirinhaCliente(@RequestBody JSONObject body) throws ServiceException;

    void limparMapUrl();
}
