package com.pacto.adm.core.services.implementations;


import com.pacto.adm.core.adapters.SorteioAdapter;
import com.pacto.adm.core.dao.interfaces.ClienteDao;
import com.pacto.adm.core.dao.interfaces.MovParcelaDao;
import com.pacto.adm.core.dao.interfaces.SorteioDao;
import com.pacto.adm.core.dto.ConfiguracaoSorteioDTO;
import com.pacto.adm.core.dto.SorteioDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroSorteioJSON;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.sorteio.Sorteio;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoSorteioService;
import com.pacto.adm.core.services.interfaces.LogService;
import com.pacto.adm.core.services.interfaces.MovParcelaService;
import com.pacto.adm.core.services.interfaces.SorteioService;
import com.pacto.config.utils.Uteis;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import org.hibernate.exception.ConstraintViolationException;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@Service
public class SorteioServiceImpl implements SorteioService {

    private final SorteioAdapter sorteioAdapter;
    private final SorteioDao sorteioDao;
    private final ClienteDao clienteDao;
    private final ConfiguracaoSorteioService configuracaoSorteioService;
    private final MovParcelaService movParcelaService;
    private final MovParcelaDao movParcelaDao;
    private final LogService logService;
    private final MessageSource messageSource;
    private final RequestService requestService;

    public SorteioServiceImpl(SorteioAdapter sorteioAdapter, SorteioDao sorteioDao, ClienteDao clienteDao, ConfiguracaoSorteioService configuracaoSorteioService, MovParcelaService movParcelaService, MovParcelaDao movParcelaDao, LogService logService, MessageSource messageSource, RequestService requestService) {
        this.sorteioAdapter = sorteioAdapter;
        this.sorteioDao = sorteioDao;
        this.clienteDao = clienteDao;
        this.configuracaoSorteioService = configuracaoSorteioService;
        this.movParcelaService = movParcelaService;
        this.movParcelaDao = movParcelaDao;
        this.logService = logService;
        this.messageSource = messageSource;
        this.requestService = requestService;
    }

    @Override
    public SorteioDTO realizarSorteio() throws ServiceException {
        try {

            ConfiguracaoSorteioDTO configuracaoSorteioDTO = configuracaoSorteioService.findByEmpresaId(requestService.getEmpresaId());

            Cliente cliente = clienteDao.sortearCliente(configuracaoSorteioDTO);

            if (cliente == null) {
                throw new ServiceException(messageSource.getMessage("sorteio.nenhum.cliente.encontrato", null, new Locale(requestService.getLocale())));
            }

            Sorteio sorteio = new Sorteio();
            sorteio.setDataSorteio(new Date());
            sorteio.setObservacoes("");
            sorteio.setCliente(cliente);
            Usuario usuario = new Usuario();
            usuario.setCodigo(requestService.getUsuarioAtual().getCodZw());
            sorteio.setUsuario(usuario);

            return sorteioAdapter.toDto(sorteio);

        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public SorteioDTO validarResultado(SorteioDTO sorteioDTO) throws ServiceException {
        try {
            for (Integer codParcela : sorteioDTO.getParcelas()) {
                movParcelaService.cancelarParcelaSorteio(codParcela, "CLIENTE VENCEDOR DE SORTEIO");
            }
            StringBuilder observacoes = new StringBuilder();
            if (sorteioDTO.getParcelas().size() == 0) {
                observacoes.append("Nenhuma parcela foi alterada.");
            } else {
                if (sorteioDTO.getParcelas().size() == 1) {
                    observacoes.append("1 parcela foi alterada.");
                } else {
                    observacoes.append(sorteioDTO.getParcelas().size() + " parcelas foram alteradas.");
                }
            }
            observacoes.append("\n");
            observacoes.append("Regras do Sorteio:\n").append(configuracaoSorteioService.getRegras());
            sorteioDTO.setObservacoes(observacoes.toString());

            sorteioDTO = this.saveOrUpdate(sorteioDTO);

            return sorteioDTO;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public SorteioDTO saveOrUpdate(SorteioDTO sorteioDTO) throws ServiceException {
        try {
            Sorteio sorteio;

            Sorteio sorteioAnterior = new Sorteio();
            if (Uteis.intNullOrEmpty(sorteioDTO.getCodigo())) {
                sorteio = sorteioAdapter.toEntity(sorteioDTO);
                sorteio = sorteioDao.save(sorteio);
            } else {
                sorteio = sorteioDao.findById(sorteioDTO.getCodigo());
                sorteioAnterior = sorteio.clone();
                sorteio = sorteioAdapter.toEntity(sorteioDTO);
                sorteio = sorteioDao.update(sorteio);
            }
            SorteioDTO dtoRetornar = sorteioAdapter.toDto(sorteio);
            logService.incluirLogInclusaoAlteracao(sorteio, sorteioAnterior, "SORTEIO", "Sorteio");
            return dtoRetornar;
        } catch (Exception e) {
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }


    @Override
    public SorteioDTO findById(Integer id) throws ServiceException {
        try {
            sorteioDao.getCurrentSession().clear();
            Sorteio sorteio = sorteioDao.findById(id);
            return sorteioAdapter.toDto(sorteio);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public List<SorteioDTO> findAll(FiltroSorteioJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Sorteio> sorteios = sorteioDao.findAll(filtros, paginadorDTO);
            return sorteioAdapter.toDtos(sorteios);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }


}
