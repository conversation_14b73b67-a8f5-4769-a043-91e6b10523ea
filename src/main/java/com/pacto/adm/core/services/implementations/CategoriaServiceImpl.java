package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.CategoriaDao;
import com.pacto.adm.core.entities.Categoria;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.services.interfaces.CategoriaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CategoriaServiceImpl implements CategoriaService {
    @Autowired
    CategoriaDao categoriaDao;

    public void criaCategoriaCasoNaoExista(Produto produto) throws Exception {
        Categoria categoria = new Categoria();
        categoria.setNome("SERVIÇOS");
        categoriaDao.save(categoria);
        produto.setCategoriaProduto(categoria);
    }

    @Override
    public List<Categoria> findAll() throws Exception {
        return categoriaDao.findAll();
    }

}
