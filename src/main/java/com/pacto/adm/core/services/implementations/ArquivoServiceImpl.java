package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ArquivoAdapter;
import com.pacto.adm.core.dao.interfaces.ArquivoDao;
import com.pacto.adm.core.dto.ArquivoDTO;
import com.pacto.adm.core.entities.Arquivo;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.mscomunication.mediams.MediaMs;
import com.pacto.adm.core.mscomunication.mediams.dto.MediaDTO;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ArquivoService;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.hibernate.exception.ConstraintViolationException;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

@Service
public class ArquivoServiceImpl implements ArquivoService {

    @Autowired
    private ArquivoDao arquivoDao;
    @Autowired
    private ArquivoAdapter arquivoAdapter;
    @Autowired
    private MediaMs mediaMs;
    @Autowired
    private RequestService requestService;

    @Override
    public ArquivoDTO saveOrUpdate(ArquivoDTO arquivoDTO) throws ServiceException {
        try {
            arquivoDao.getCurrentSession().clear();
            Arquivo arquivo;

            if (Uteis.intNullOrEmpty(arquivoDTO.getCodigo())) {
                arquivo = arquivoAdapter.toEntity(arquivoDTO);
                arquivo.setDataRegistro(new Date());
                arquivo = arquivoDao.save(arquivo);

                if (!Uteis.nullOrEmpty(arquivoDTO.getDados())) {
                    MediaDTO mediaDTO = new MediaDTO();
                    mediaDTO.setArquivo(arquivoDTO.getDados());
                    mediaDTO.setChave(this.requestService.getUsuarioAtual().getChave());
                    mediaDTO.setIdentificador(arquivo.getCodigo().toString());
                    mediaDTO.setTipo(arquivoDTO.getTipo());
                    mediaDTO.setEncriptarIdentificador(true);
                    mediaDTO.setExtensao(arquivo.getExtensao());
                    mediaMs.uploadFileWithEntension(mediaDTO);
                }
            } else {
                Arquivo arquivoAnterior = arquivoDao.findById(arquivoDTO.getCodigo());
                arquivo = arquivoAdapter.toEntity(arquivoDTO);
                arquivo.setDataRegistro(arquivoAnterior.getDataRegistro());
                arquivo = arquivoDao.update(arquivo);
            }
            return arquivoAdapter.toDto(arquivo);
        } catch (Exception e) {
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ArquivoDTO> findByIdSolicitacaoCompra(Integer idSolicitacaoCompra) {
        try {
            List<ArquivoDTO> dtos = new ArrayList<>();
            Map<String, Object> param = new HashMap<>();
            param.put("idSolicitacaoCompra", idSolicitacaoCompra);
            List<Arquivo> arquivos = arquivoDao.findByParam(new StringBuilder("where obj.solicitacaoCompra.codigo = :idSolicitacaoCompra"), param);
            if(!UteisValidacao.emptyList(arquivos)){
                for (Arquivo arquivo : arquivos) {
                    ArquivoDTO arquivoDTO = arquivoAdapter.toDto(arquivo);
                    if (!UteisValidacao.emptyString(arquivoDTO.getFotoKey())) {
                        try {
                            arquivoDTO.setUrlFull(mediaMs.getImageUrl(arquivoDTO.getFotoKey()));
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            continue;
                        }
                    }
                    dtos.add(arquivoDTO);
                }
            }
            return dtos;
        }catch (Exception ex){
            return new ArrayList<>();
        }
    }
}
