package com.pacto.adm.core.services.interfaces;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaHubSpotDTO;
import com.pacto.config.exceptions.ServiceException;
public interface ConfiguracaoEmpresaHubSpotService {

    ConfiguracaoEmpresaHubSpotDTO findByEmpresaId(Integer codigoEmpresa) throws Exception;

    ConfiguracaoEmpresaHubSpotDTO salvarConfiguracaoEmpresaHubSpot(ConfiguracaoEmpresaHubSpotDTO configDTO) throws ServiceException;

    ConfiguracaoEmpresaHubSpotDTO aprovar(ConfiguracaoEmpresaHubSpotDTO configDTO) throws ServiceException;
}
