package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ConfiguracaoSorteioAdapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoSorteioDao;
import com.pacto.adm.core.dto.ConfiguracaoSorteioDTO;
import com.pacto.adm.core.dto.PlanoDTO;
import com.pacto.adm.core.entities.sorteio.ConfiguracaoSorteio;
import com.pacto.adm.core.enumerador.SituacaoClienteEnum;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoSorteioService;
import com.pacto.adm.core.services.interfaces.LogService;
import com.pacto.config.utils.Uteis;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import org.hibernate.exception.ConstraintViolationException;

@Service
public class ConfiguracaoSorteioServiceImpl implements ConfiguracaoSorteioService {

    private final ConfiguracaoSorteioAdapter configuracaoSorteioAdapter;
    private final ConfiguracaoSorteioDao configuracaoSorteioDao;
    private final LogService logService;
    private final MessageSource messageSource;
    private final RequestService requestService;

    public ConfiguracaoSorteioServiceImpl(ConfiguracaoSorteioAdapter configuracaoSorteioAdapter, ConfiguracaoSorteioDao configuracaoSorteioDao, LogService logService, MessageSource messageSource, RequestService requestService) {
        this.configuracaoSorteioAdapter = configuracaoSorteioAdapter;
        this.configuracaoSorteioDao = configuracaoSorteioDao;
        this.logService = logService;
        this.messageSource = messageSource;
        this.requestService = requestService;
    }

    @Override
    public ConfiguracaoSorteioDTO saveOrUpdate(ConfiguracaoSorteioDTO configuracaoSorteioDTO) throws ServiceException {
        try {
            ConfiguracaoSorteio configuracaoSorteio;

            ConfiguracaoSorteio configuracaoSorteioAnterior = new ConfiguracaoSorteio();
            if (Uteis.intNullOrEmpty(configuracaoSorteioDTO.getCodigo())) {
                configuracaoSorteio = configuracaoSorteioAdapter.toEntity(configuracaoSorteioDTO);
                configuracaoSorteio = configuracaoSorteioDao.save(configuracaoSorteio);
            } else {
                configuracaoSorteio = configuracaoSorteioDao.findByCodigo(configuracaoSorteioDTO.getCodigo());
                configuracaoSorteioAnterior = configuracaoSorteio.clone();
                configuracaoSorteio = configuracaoSorteioAdapter.toEntity(configuracaoSorteioDTO);
                configuracaoSorteio = configuracaoSorteioDao.save(configuracaoSorteio);
            }
            ConfiguracaoSorteioDTO dtoRetornar = configuracaoSorteioAdapter.toDto(configuracaoSorteio);
            logService.incluirLogInclusaoAlteracao(configuracaoSorteio, configuracaoSorteioAnterior, "CONFIGURACAOSORTEIO", "Configuração Sorteio");
            return dtoRetornar;
        } catch (Exception e) {
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }

    @Override
    public String getRegras() {
        ConfiguracaoSorteioDTO configuracaoSorteioDTO = configuracaoSorteioAdapter.toDto(configuracaoSorteioDao.findByEmpresa(requestService.getEmpresaId()));

        StringBuilder sbSituacoes = new StringBuilder();
        if (configuracaoSorteioDTO.getSituacoesCliente().size() > 0) {
            sbSituacoes.append("Clientes nas situações: ");
            for (String situacao : configuracaoSorteioDTO.getSituacoesCliente()) {
                if (!situacao.isEmpty()) {
                    sbSituacoes.append(SituacaoClienteEnum.getSituacaoCliente(situacao).getDescricao()).append(",");
                }
            }
            sbSituacoes.deleteCharAt(sbSituacoes.length() - 1);
            sbSituacoes.append("\n");
        }

        StringBuilder sbPlanos = new StringBuilder();
        if (configuracaoSorteioDTO.getPlanos().size() > 0) {
            sbPlanos.append("Clientes com os planos: ");
            for (PlanoDTO plano : configuracaoSorteioDTO.getPlanos()) {
                sbPlanos.append(plano.getDescricao()).append(",");
            }
            sbPlanos.deleteCharAt(sbPlanos.length() - 1);
            sbPlanos.append("\n");
        }

        if (sbSituacoes.toString().isEmpty() && sbPlanos.toString().isEmpty()) {
            return "Todos clientes cadastrados";
        }

        return sbSituacoes.toString() + sbPlanos.toString();
    }

    @Override
    public ConfiguracaoSorteioDTO findByEmpresaId(Integer empresaId) throws ServiceException {
        try {
            ConfiguracaoSorteio configuracaoSorteio = configuracaoSorteioDao.findByEmpresa(empresaId);
            return configuracaoSorteioAdapter.toDto(configuracaoSorteio);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
