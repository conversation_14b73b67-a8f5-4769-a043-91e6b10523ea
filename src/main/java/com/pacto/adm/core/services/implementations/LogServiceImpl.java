package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.LogAdapter;
import com.pacto.adm.core.dao.interfaces.LogDao;
import com.pacto.adm.core.dao.interfaces.LogRepository;
import com.pacto.adm.core.dao.interfaces.UsuarioRepository;
import com.pacto.adm.core.dto.ClienteDadosGymPassDTO;
import com.pacto.adm.core.dto.LogDTO;
import com.pacto.adm.core.dto.PeriodoAcessoClienteDTO;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroLogJSON;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.ClienteObservacao;
import com.pacto.adm.core.entities.Log;
import com.pacto.adm.core.entities.MovParcela;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.contrato.ContratoOperacao;
import com.pacto.adm.core.entities.contrato.PeriodoAcessoCliente;
import com.pacto.adm.core.entities.financeiro.ItemVendaAvulsa;
import com.pacto.adm.core.entities.financeiro.VendaAvulsa;
import com.pacto.adm.core.services.interfaces.LogService;
import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;
import com.pacto.config.annotations.UseOnlyThisToLog;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Transient;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class LogServiceImpl implements LogService {

    private static final int MAXIMO_RESULTADOS = 10;
    private final LogDao logDao;
    private final LogAdapter logAdapter;
    private final RequestService requestService;
    private final UsuarioRepository usuarioRepository;
    private final LogRepository logRepository;
    private List<Log> logs;
    private String chavePrimaria = "";
    private String operacao = "";
    private Field[] fieldsMarkedUseOnlyThisToLog = new Field[]{};

    private Logger logger = Logger.getLogger(LogServiceImpl.class.getName());

    public LogServiceImpl(LogDao logDao, LogAdapter logAdapter, RequestService requestService, UsuarioRepository usuarioRepository, LogRepository logRepository) {
        this.logs = new ArrayList<>();
        this.logDao = logDao;
        this.logAdapter = logAdapter;
        this.requestService = requestService;
        this.usuarioRepository = usuarioRepository;
        this.logRepository = logRepository;
    }

    public void incluirLogInclusaoAlteracao(Object objAlterado, Object objAnterior, String nomeEntidade, String nomeEntidadeDescricao) throws Exception {
        incluirLogInclusaoAlteracao(objAlterado, objAnterior, nomeEntidade, nomeEntidadeDescricao, null);
    }

    public void incluirLogInclusaoAlteracao(Object objAlterado, Object objAnterior, String nomeEntidade, String nomeEntidadeDescricao, Integer codigoPessoa) throws Exception {
        try {
            this.gerarLogInclusaoAlteracao(objAlterado, objAnterior, nomeEntidade, nomeEntidadeDescricao, codigoPessoa);
            this.saveLogs();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void incluirLogExcluscao(Object planoAlterado, String nomeEntidade, String nomeEntidadeDescricao) throws Exception {
        incluirLogExcluscao(planoAlterado, nomeEntidade, nomeEntidadeDescricao, null);
    }

    public void incluirLogExcluscao(Object planoAlterado, String nomeEntidade, String nomeEntidadeDescricao, Integer codigoPessoa) throws Exception {
        try {
            this.gerarLogInclusaoAlteracao(planoAlterado, null, nomeEntidade, nomeEntidadeDescricao, codigoPessoa);
            this.saveLogs("EXCLUSÃO");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void incluirLogEstornoContratoOperacao(ContratoOperacao contratoOperacao) throws Exception {
        try {
            Log log = new Log();
            log.setChavePrimaria(contratoOperacao.getCodigo() + "");
            log.setNomeEntidade("CONTRATO OPERAÇÃO");
            log.setNomeEntidadeDescricao("");
            log.setNomeCampo("OBS.");
            log.setOperacao("ESTORNO DE OPERAÇÃO");
            log.setDataAlteracao(Calendario.hoje());
            log.setValorCampoAlterado(getDescricaoLogEstornoOperacao(contratoOperacao));
            log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
            log.setPessoa(contratoOperacao.getContrato().getPessoa().getCodigo());
            logDao.save(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void incluirLogGymPass(ClienteDadosGymPassDTO clienteDadosGymPassDTO, Cliente cliente) throws Exception {
        Log obj = new Log();
        obj.setChavePrimaria(cliente.getPessoa().getCodigo().toString());
        obj.setNomeEntidade("CLIENTE - GYMPASS");
        obj.setNomeEntidadeDescricao("Cliente - GymPass");
        obj.setOperacao("VALIDACÃO" + " GYMPASS");
        obj.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
        obj.setNomeCampo("GYMPASS-Cliente");
        obj.setValorCampoAnterior("TOKEN GYMPASS: " + clienteDadosGymPassDTO.getGymPassUniqueToken());
        obj.setValorCampoAlterado("");
        obj.setDataAlteracao(Calendario.hoje());
        logDao.save(obj);
    }

    public void incluirLogFalhaIntegracaoSescGO(Integer codigoPessoa, String errorMessage) {
        final Log log = new Log();
        log.setNomeCampo("Erro");
        log.setChavePrimaria(codigoPessoa.toString());
        log.setNomeEntidade("CLIENTE - INTEGRAÇÃO (SESC GO)");
        log.setPessoa(codigoPessoa);
        log.setValorCampoAlterado(errorMessage);
        log.setOperacao("Integração SESC GO - Falha ao atualizar dados do cliente");
        log.setDataAlteracao(Calendario.hoje());
        log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());

        logRepository.save(log);
    }

    public void incluirLogClienteObservacao(ClienteObservacao clienteObservacaoAnterior, ClienteObservacao clienteObservacaoAlterado, Pessoa pessoa, boolean isExcluir) throws Exception {
        try {
            Log log = new Log();
            log.setChavePrimaria(clienteObservacaoAlterado.getCodigo().toString());
            log.setNomeEntidade("OBSERVAÇÃO CLIENTE");
            log.setNomeEntidadeDescricao("Observação - Cliente");
            if (isExcluir) {
                log.setOperacao("EXCLUSÃO OBSERVAÇÃO");
            } else if (clienteObservacaoAnterior == null) {
                log.setOperacao("INCLUSÃO OBSERVAÇÃO");
            } else {
                log.setOperacao("ALTERAÇÃO OBSERVAÇÃO");
            }
            log.setNomeCampo("Campo(s)");
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
            log.setValorCampoAnterior(gerarLogObservacao(clienteObservacaoAnterior));
            log.setValorCampoAlterado(gerarLogObservacao(clienteObservacaoAlterado));
            log.setChavePrimariaEntidadeSubordinada("");
            if (pessoa != null && (isExcluir || !log.getValorCampoAlterado().equals(log.getValorCampoAnterior()))) {
                log.setPessoa(pessoa.getCodigo());
                logDao.save(log);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void incluirLogVendaAvulsa(VendaAvulsa vendaAvulsa) throws Exception {
        try {
            Log log = new Log();
            log.setChavePrimaria(vendaAvulsa.getCodigo().toString());
            log.setNomeEntidade("VENDA AVULSA");
            log.setNomeEntidadeDescricao("Venda Avulsa");
            log.setOperacao("INCLUSÃO");
            log.setNomeCampo("Campo(s)");
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao(vendaAvulsa.getResponsavel().getNome());
            log.setValorCampoAlterado(gerarLogVendaAvulsa(vendaAvulsa));
            log.setChavePrimariaEntidadeSubordinada("");

            if (vendaAvulsa.getPessoa() != null) {
                log.setPessoa(vendaAvulsa.getPessoa().getCodigo());
            }
            if (vendaAvulsa.getNomeComprador() != null) {
                logDao.save(log);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String gerarLogObservacao(ClienteObservacao clienteObservacao) {
        if (clienteObservacao == null) {
            return "";
        }
        String valores = "codigo=" + clienteObservacao.getCodigo() + "\n";
        valores += "Observação=" + clienteObservacao.getObservacao() + "\n";
        SimpleDateFormat smd = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        valores += "Data Cadastro=" + smd.format(clienteObservacao.getDataCadastro()) + "\n";
        return valores;
    }

    private String gerarLogVendaAvulsa(VendaAvulsa vendaAvulsa) {
        if (vendaAvulsa == null) {
            return "";
        }

        NumberFormat currencyFormatter = NumberFormat.getCurrencyInstance(new Locale("pt", "BR"));

        StringBuilder valores = new StringBuilder();
        valores.append("código da venda = ").append(vendaAvulsa.getCodigo()).append("\n");

        SimpleDateFormat smd = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        valores.append("Data Cadastro = ").append(smd.format(vendaAvulsa.getDataRegistro())).append("\n");

        valores.append("Tipo do comprador: ").append(getTipoComprador(vendaAvulsa.getTipoComprador())).append("\n");

        valores.append("Comprador = ").append(vendaAvulsa.getNomeComprador()).append("\n");

        valores.append("Empresa = ").append(vendaAvulsa.getEmpresa().getCodigo()).append(" ").append(vendaAvulsa.getEmpresa().getNome()).append("\n");

        valores.append("Responsável pela venda = ").append(vendaAvulsa.getResponsavel().getNome()).append("\n");

        valores.append("Valor total da venda = ").append(currencyFormatter.format(vendaAvulsa.getValorTotal())).append("\n");

        valores.append("Número de itens = ").append(vendaAvulsa.getItens().size()).append("\n");

        valores.append("Itens = [ ");
        if (vendaAvulsa.getItens() != null && !vendaAvulsa.getItens().isEmpty()) {
            int contador = 1;
            for (ItemVendaAvulsa item : vendaAvulsa.getItens()) {
                valores.append("\n    Item ").append(contador).append(": {")
                        .append("\n        código do produto: ").append(item.getProduto().getCodigo())
                        .append("\n        nome: ").append(item.getProduto().getDescricao())
                        .append("\n        quantidade: ").append(item.getQuantidade())
                        .append("\n        valor unitário: ").append(currencyFormatter.format(item.getProduto().getValorFinal()));

                if (item.getTabelaDesconto() != null) {
                    Double desconto = item.getTabelaDesconto().getValor();
                    if (desconto != null && desconto > 0) {
                        valores.append("\n        nome do desconto padrão: ").append(item.getTabelaDesconto().getDescricao());
                        valores.append("\n        valor do desconto padrão: ").append(currencyFormatter.format(desconto));
                    } else {
                        valores.append("\n        desconto padrão: não houve desconto");
                    }
                } else {
                    valores.append("\n        desconto padrão: não houve desconto");
                }

                if (item.isDescontoManual()) {
                    Double descontoExtra = item.getValorDescontoManual();
                    if (descontoExtra != null && descontoExtra > 0) {
                        valores.append("\n        desconto extra: ").append(currencyFormatter.format(descontoExtra));
                    } else {
                        valores.append("\n        desconto extra: não houve desconto");
                    }
                } else {
                    valores.append("\n        desconto extra: não houve desconto");
                }

                valores.append("    },");
                contador++;
            }

            valores.append("\n ]");

            if (vendaAvulsa.getMovParcelas() != null && !vendaAvulsa.getMovParcelas().isEmpty()) {
                valores.append("\n Número de parcelas = ").append(vendaAvulsa.getMovParcelas().size());
                valores.append("\n Parcelas = [ ");

                List<MovParcela> parcelasOrdenadas = new ArrayList<>(vendaAvulsa.getMovParcelas());
                parcelasOrdenadas.sort(Comparator.comparing(MovParcela::getDataCobranca));

                int parcelaContador = 1;
                for (MovParcela parcela : parcelasOrdenadas) {
                    valores.append("\n    Parcela ").append(parcelaContador).append(": {")
                            .append("\n        valor da parcela: ").append(currencyFormatter.format(parcela.getValorParcela()))
                            .append("\n        data da parcela: ").append(smd.format(parcela.getDataCobranca()))
                            .append("\n    },");
                    parcelaContador++;
                }
                valores.append("\n ]");
            } else {
                valores.append("\n Número de parcelas = Nenhuma parcela registrada\n");
            }

        } else {
            valores.append("Nenhum item registrado\n");
        }

        return valores.toString();
    }

    private String getTipoComprador(String tipoComprador) {
        switch (tipoComprador.toUpperCase()) {
            case "CO":
                return "Colaborador";
            case "CI":
                return "Cliente";
            case "CN":
                return "Consumidor";
            default:
                return "Não especificado";
        }
    }

    private String ajustarDescricaoVendaAvulsa(LogDTO log) {
        if (log == null || log.getDescricao() == null || log.getDescricao().isEmpty()) {
            return "Informações da venda não disponíveis.";
        }

        String descricao = log.getDescricao();

        String data = extrairValorCampo(descricao, "Data Cadastro");
        String valorTotal = extrairValorCampo(descricao, "Valor total da venda");
        String numeroDeItens = extrairValorCampo(descricao, "Número de itens");

        return "Data: " + (data.isEmpty() ? "Não informada" : data) + "<br>"
                + " Valor Total: " + (valorTotal.isEmpty() ? "Não informado" : valorTotal) + "<br>"
                + " Número de Itens: " + (numeroDeItens.isEmpty() ? "Não informado" : numeroDeItens);
    }

    private String extrairValorCampo(String texto, String campo) {
        Pattern pattern = Pattern.compile(campo + " = ([^<]+)");
        Matcher matcher = pattern.matcher(texto);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return "";
    }

    @Override
    public List<LogDTO> consultarPorPessoa(Integer codigoPessoa, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            return logAdapter.toDtos(logDao.consultarPorPessoa(codigoPessoa, paginadorDTO));
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
            throw new ServiceException("Erro ao consultar log");
        }
    }

    @Override
    public List<LogDTO> findLogsByNomeEntidadeCodPessoaChavePrimaria(String nomeEntidade, Integer chavePrimaria, Integer pessoa, PaginadorDTO paginadorDTO, FiltroLogClienteJSON filtros) throws ServiceException {
        try {
            List<Log> logs = logDao.consultarPorNomeChavePrimariaDataInicioDataFim(nomeEntidade, chavePrimaria, filtros, pessoa, paginadorDTO);
            List<LogDTO> logsAgrupados = montarDadosAgrupado(logs, true);
            return realizarPaginacaoAgrupados(paginadorDTO, logsAgrupados, filtros);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
            throw new ServiceException("Erro ao consultar log: " + e.getMessage());
        }
    }


    @Override
    public List<LogDTO> consultarPorEntidadeFiltros(String nomeEntidade, FiltroLogClienteJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Log> logs = logDao.consultarPorEntidade(nomeEntidade, filtros, null, paginadorDTO);
            List<LogDTO> logsAgrupados = montarDadosAgrupado(logs, false);
            return realizarPaginacaoAgrupados(paginadorDTO, logsAgrupados, filtros);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
            throw new ServiceException("Erro ao consultar log: " + e.getMessage());
        }
    }

    @Override
    public void incluirLogInclusaoFreepass(Cliente cliente, PeriodoAcessoCliente periodoAcessoCliente) throws Exception {
        try {
            Log logFreepass = new Log();
            String nomeUsuario = null;
            logFreepass.setPessoa(cliente.getPessoa().getCodigo());
            logFreepass.setNomeEntidade("FREEPASS" + "");
            logFreepass.setChavePrimaria(cliente.getPessoa().getCodigo().toString());
            logFreepass.setNomeEntidadeDescricao("FreePass");
            logFreepass.setDataAlteracao(Calendario.hoje());
            Usuario usuarioResponsavelFreepass = usuarioRepository.findById(cliente.getResponsavelfreepass()).orElseThrow(() -> new Exception("Usuário responsável freepass não informado!"));
            logFreepass.setOperacao("INCLUSÃO " + usuarioResponsavelFreepass.getNome());
            try {
                Usuario usuarioAtual = usuarioRepository.findById(requestService.getUsuarioAtual().getCodZw()).orElseThrow(() -> new Exception("Usuário Atual não encontrato. O responsável da alteração será o mesmo que o responsável pelo freepass!"));
                nomeUsuario = usuarioAtual.getNome();
            } catch (Exception e) {
                nomeUsuario = usuarioResponsavelFreepass.getNome();
            }
            logFreepass.setResponsavelAlteracao(nomeUsuario);
            logFreepass.setNomeCampo("FREEPASS");
            logFreepass.setValorCampoAlterado("Nome do Cliente = " + cliente.getPessoa().getNome() + "\n\rData do inicio do FreePass = " + (Uteis.getData(periodoAcessoCliente.getDataInicioAcesso())) + "\n\rData Final do FreePass= " + (Uteis.getData(periodoAcessoCliente.getDataFinalAcesso())) + "\n\rProduto = " + cliente.getFreepass().getDescricao() + "\n\r");

            logRepository.save(logFreepass);
        } catch (Exception e) {
            Usuario usuarioAtual = usuarioRepository.findById(cliente.getResponsavelfreepass()).orElseThrow(() -> new Exception("Usuário responsável freepass não informado!"));
            registrarLogErro("FREEPASS", cliente.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE FREEPASS", usuarioAtual.getNome());

        }
    }

    @Override
    public void incluirLogExclusaoFreepass(Cliente cliente, PeriodoAcessoClienteDTO periodoAcessoCliente) throws Exception {
        try {
            Log logFreepass = new Log();
            logFreepass.setPessoa(cliente.getPessoa().getCodigo());
            logFreepass.setNomeEntidade("FREEPASS" + "");
            logFreepass.setChavePrimaria(cliente.getPessoa().getCodigo().toString());
            logFreepass.setNomeEntidadeDescricao("FreePass");
            logFreepass.setDataAlteracao(Calendario.hoje());

            Usuario usuarioResponsavelFreepass = usuarioRepository.findById(cliente.getResponsavelfreepass()).orElseThrow(() -> new Exception("Usuário responsável freepass não informado!"));
            logFreepass.setOperacao("EXCLUSÃO " + usuarioResponsavelFreepass.getNome());

            Usuario usuarioAtual = usuarioRepository.findById(requestService.getUsuarioAtual().getCodZw()).orElseThrow(() -> new Exception("Usuário Atual não encontrato. O responsável da alteração será o mesmo que o responsável pelo freepass!"));
            logFreepass.setResponsavelAlteracao(usuarioAtual.getNome());

            logFreepass.setNomeCampo("FREEPASS");
            logFreepass.setValorCampoAlterado("Nome do Cliente = " + cliente.getPessoa().getNome() + "\n\rData do inicio do FreePass = " + Uteis.getData(periodoAcessoCliente.getDataInicioAcesso()) + "\n\rData Final do FreePass= " + (Uteis.getData(periodoAcessoCliente.getDataFinalAcesso())));

            logRepository.save(logFreepass);
        } catch (Exception e) {
            Usuario usuarioAtual = usuarioRepository.findById(cliente.getResponsavelfreepass()).orElseThrow(() -> new Exception("Usuário responsável freepass não informado!"));
            registrarLogErro("FREEPASS", cliente.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE FREEPASS", usuarioAtual.getNome());
        }
    }

    @Override
    public List<LogDTO> consultarPorEntidadeDataAlteracaoOperacao(FiltroLogJSON filtros, boolean buscarComAdministrador, PaginadorDTO paginadorDTO) throws ServiceException {
        List<Log> logs = logDao.consultarPorEntidadeDataAlteracaoOperacao(filtros, buscarComAdministrador, paginadorDTO);
        return logAdapter.toDtos(logs);
    }

    @Override
    public List<LogDTO> consultarPorEntidadeDataAlteracaoOperacaoComDadosCliente(FiltroLogJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        List<Log> logs = logDao.consultarPorEntidadeDataAlteracaoOperacaoComDadosCliente(filtros, paginadorDTO);
        return logAdapter.toDtos(logs);
    }

    @Override
    public List<LogDTO> consultarPorNomeEntidadeEmpresaClientePeriodoAgrupado(FiltroLogJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        List<Log> logs = logDao.consultarPorNomeEntidadeEmpresaClientePeriodoAgrupado(filtros, paginadorDTO);
        return logAdapter.toDtos(logs);
    }

    private List<LogDTO> realizarPaginacaoAgrupados(PaginadorDTO paginadorDTO, List<LogDTO> logsAgrupados, FiltroLogClienteJSON filtros) {

        if (filtros != null) {
            logsAgrupados = logsAgrupados.stream().filter(
                    log -> {
                        boolean filtroDataInicio = true;
                        boolean filtroDataFim = true;
                        boolean filtroTipo = true;
                        if (!UteisValidacao.emptyString(filtros.getParametro())) {
                            return log.getDescricao().contains(filtros.getParametro()) || log.getOperacao().contains(filtros.getParametro()) || log.getUsuario().contains(filtros.getParametro());
                        }
                        if (filtros.getDataInicio() != null) {
                            filtroDataInicio = Calendario.maiorOuIgual(log.getDataAlteracao(), filtros.getDataInicio());
                        }
                        if (filtros.getDataFim() != null) {
                            filtroDataFim = Calendario.menorOuIgual(log.getDataAlteracao(), filtros.getDataFim());
                        }
                        if (filtros.getTipo() != null && !filtros.getTipo().isEmpty()) {
                            filtroTipo = filtros.getTipo().stream().anyMatch(tipo -> log.getOperacao().contains(tipo));
                        }
                        return filtroDataInicio && filtroDataFim && filtroTipo;
                    }
            ).collect(Collectors.toList());
        }
        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos((long) logsAgrupados.size());
            int maxResults = MAXIMO_RESULTADOS;
            int indiceInicial = 0;
            maxResults = paginadorDTO.getSize() == null ? maxResults : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;

            if (indiceInicial > 0) {
                maxResults += indiceInicial;
                if (maxResults > logsAgrupados.size()) {
                    maxResults = logsAgrupados.size();
                }
            }

            if (logsAgrupados.size() >= maxResults) {
                logsAgrupados = logsAgrupados.subList(indiceInicial, maxResults);
            }

            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("operacao")) {
                    logsAgrupados = logsAgrupados.stream().sorted(
                            (log1, log2) -> {
                                if (sortOrder.equals("ASC")) {
                                    return log1.getOperacao().compareTo(log2.getOperacao());
                                }
                                return log2.getOperacao().compareTo(log1.getOperacao());
                            }
                    ).collect(Collectors.toList());
                } else if (sortField.equalsIgnoreCase("usuario")) {
                    logsAgrupados = logsAgrupados.stream().sorted(
                            (log1, log2) -> {
                                if (sortOrder.equals("ASC")) {
                                    return log1.getUsuario().compareTo(log2.getUsuario());
                                }
                                return log2.getUsuario().compareTo(log1.getUsuario());
                            }
                    ).collect(Collectors.toList());
                } else if (sortField.equalsIgnoreCase("data")) {
                    logsAgrupados = logsAgrupados.stream().sorted(
                            (log1, log2) -> {
                                if (sortOrder.equals("ASC")) {
                                    return log1.getDataAlteracao().compareTo(log2.getDataAlteracao());
                                }
                                return log2.getDataAlteracao().compareTo(log1.getDataAlteracao());
                            }
                    ).collect(Collectors.toList());
                } else if (sortField.equalsIgnoreCase("codigo")) {
                    logsAgrupados = logsAgrupados.stream().sorted(
                            (log1, log2) -> {
                                if (sortOrder.equals("ASC")) {
                                    return log1.getCodigo().compareTo(log2.getCodigo());
                                }
                                return log2.getCodigo().compareTo(log1.getCodigo());
                            }
                    ).collect(Collectors.toList());
                } else if (sortField.equalsIgnoreCase("descricao")) {
                    logsAgrupados = logsAgrupados.stream().sorted(
                            (log1, log2) -> {
                                if (sortOrder.equals("ASC")) {
                                    return log1.getDescricao().compareTo(log2.getDescricao());
                                }
                                return log2.getDescricao().compareTo(log1.getDescricao());
                            }
                    ).collect(Collectors.toList());
                }
            }
        }

        logsAgrupados.forEach(log -> {
            if ("Venda Avulsa".equalsIgnoreCase(log.getNomeEntidade())) {
                log.setDescricao(ajustarDescricaoVendaAvulsa(log));
            }
        });

        return logsAgrupados;
    }

    /**
     * Montar dados que agrupa quando DATA/HORA, OPERACAO E RESPONSAVEL forem iguais
     *
     * @param logs
     * @return
     * @throws Exception
     */
    private List<LogDTO> montarDadosAgrupado(List<Log> logs, boolean agruparSegundo) throws Exception {
        List<LogDTO> vetResultado = new ArrayList();
        LogDTO obj = new LogDTO();
        LogDTO objMontado = null;
        LogDTO objAnterior = null;
        boolean add = false;
        String descricao = null;
        for (int i = 0; i < logs.size(); i++) {
            descricao = "";
            objMontado = logAdapter.toDto(logs.get(i));
            add = false;

            if (objMontado != null) {
                if (objAnterior != null) {
                    String operacao1 = objMontado.getOperacao();
                    String operacao2 = objAnterior.getOperacao();
                    try {
                        if (objMontado.getOperacao().contains("-")) {
                            String[] aux = objMontado.getOperacao().split("-");
                            operacao1 = aux[0].trim();
                        }
                        if (objAnterior.getOperacao().contains("-")) {
                            String[] aux = objAnterior.getOperacao().split("-");
                            operacao2 = aux[0].trim();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //VERIFICANDO SE O LOG DEVERA SER AGRUPADO OU NAO, INICIALMENTE O AGRUPAMENTO OCORRE
                    //QUANDO DATA/HORA, OPERACAO E RESPONSAVEL SAO IGUAIS
                    if (((agruparSegundo && (Uteis.getDataComHHMM(objMontado.getDataAlteracao()).equals(Uteis.getDataComHHMM(objAnterior.getDataAlteracao()))))
                            || (!agruparSegundo && (Uteis.gethoraHHMMSSAjustado(objMontado.getDataAlteracao()).equals(Uteis.gethoraHHMMSSAjustado(objAnterior.getDataAlteracao())))))
                            && operacao1.equals(operacao2)
                            && objMontado.getResponsavelAlteracao().equals(objAnterior.getResponsavelAlteracao())
                            && (!UteisValidacao.emptyNumber(objMontado.getPessoa()) || objMontado.getChavePrimaria().equals(objAnterior.getChavePrimaria()))) {
                        if (!"".equals(objMontado.getNomeCampo()) && !"".equals(objMontado.getNomeCampo())) {
                            if (!"".equals(obj.getDescricao())) {
                                obj.setNomeCampo(objMontado.getNomeCampo());
                            } else {
                                obj.setNomeCampo("[" + objMontado.getNomeCampo() + "]");
                            }

                            if (objMontado.getValorCampoAnterior() != null) {
                                obj.setValorCampoAnterior(obj.getValorCampoAnterior().trim() + "<br />" + objMontado.getValorCampoAnterior().trim());

                                if (!"".equals(objMontado.getValorCampoAnterior())) {
                                    descricao = "'" + objMontado.getValorCampoAnterior().trim() + "'";
                                } else {
                                    descricao = "'Sem valor'";
                                }
                            }
                            if (objMontado.getValorCampoAlterado() != null) {
                                obj.setValorCampoAlterado(obj.getValorCampoAlterado().trim() + "<br />[" + objMontado.getValorCampoAlterado().trim() + "]");

                                if (!"".equals(objMontado.getValorCampoAlterado())) {
                                    descricao = descricao + " para '" + objMontado.getValorCampoAlterado().trim() + "'";
                                } else {
                                    descricao = descricao + " para 'Sem valor'";
                                }
                            }
                            if (!"".equals(descricao)) {
                                obj.setDescricao(obj.getDescricao() + "<br />[" + objMontado.getNomeCampo().trim() + ": " + descricao + "]");
                            }
                        }
                    } else {
                        add = true;
                    }
                } else {
                    iniciarNovoLog(obj, objMontado);
                }
                if (add) {
                    logAdapter.setCamposNovoLog(obj);
                    vetResultado.add(obj);
                    obj = new LogDTO();
                    iniciarNovoLog(obj, objMontado);
                    //continue;
                }
            }
            if (i == logs.size() - 1 && objAnterior != null && objAnterior.getCodigo().intValue() != objMontado.getCodigo().intValue()) {
                logAdapter.setCamposNovoLog(obj);
                vetResultado.add(obj);
            }
            objAnterior = objMontado;
        }
        if (vetResultado.isEmpty() && obj != null && !UteisValidacao.emptyNumber(obj.getCodigo())) {
            logAdapter.setCamposNovoLog(obj);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private List<LogDTO> montarDados(List<Log> logs) {
        List<LogDTO> lista = new ArrayList<>();
        for (Log log : logs) {
            lista.add(logAdapter.toDto(log));
        }
        return lista;
    }

    private void iniciarNovoLog(LogDTO obj, LogDTO objMontado) {
        String descricao = "";

        obj.setDescricao("");
        obj.setNomeCampo("");
        if (objMontado.getNomeCampo() != null && !"".equals(objMontado.getNomeCampo())) {
            obj.setNomeCampo(objMontado.getNomeCampo().trim());
            obj.setValorCampoAnterior("");
            if (objMontado.getValorCampoAnterior() != null) {
                obj.setValorCampoAnterior(objMontado.getValorCampoAnterior().trim());
                if (!"".equals(objMontado.getValorCampoAnterior())) {
                    descricao = "'" + objMontado.getValorCampoAnterior().trim() + "'";
                } else {
                    descricao = "'Sem valor'";
                }
            }
            obj.setValorCampoAlterado("");
            if (objMontado.getValorCampoAlterado() != null) {
                obj.setValorCampoAlterado(objMontado.getValorCampoAlterado().trim());
                if ("".equals(objMontado.getValorCampoAlterado())) {
                    descricao = descricao + " para 'Sem valor'";
                } else {
                    descricao = descricao + " para '" + objMontado.getValorCampoAlterado().trim() + "'";
                }
            }

            if (!"".equals(descricao)) {
                obj.setDescricao("[" + objMontado.getNomeCampo().trim() + ": " + descricao + "]");
            }
        }
        obj.setOperacao(objMontado.getOperacao());
        obj.setDataAlteracao(objMontado.getDataAlteracao());
        obj.setResponsavelAlteracao(objMontado.getResponsavelAlteracao());
        obj.setNomeEntidade(objMontado.getNomeEntidade());
        obj.setChavePrimaria(objMontado.getChavePrimaria());
        obj.setPessoa(objMontado.getPessoa());
        obj.setCodigo(objMontado.getCodigo());
        obj.setOrigem(objMontado.getOrigem());
    }

    private String getDescricaoLogEstornoOperacao(ContratoOperacao contratoOperacao) {
        return "Operação de " + "Estorno Contrato Operação - " + contratoOperacao.getTipoOperacao_Apresentar() + " do contrato " + contratoOperacao.getContrato().getCodigo()
                + ", iniciada no dia " + Uteis.getData(contratoOperacao.getDataInicioEfetivacaoOperacao(), "br") + " e finalizada no dia "
                + Uteis.getData(contratoOperacao.getDataFimEfetivacaoOperacao(), "br") + ", lançada em " + Uteis.getData(contratoOperacao.getDataOperacao(), "br")
                + " foi estornada, alterando o histórico do contrato e período de acesso do cliente. excluido desmarcação de aula a pardir da data da operação ";
    }

    private void gerarLogInclusaoAlteracao(Object objetoAlterado, Object objetoAnterior, String nomeEntidade, String nomeEntidadeDescricao) throws Exception {
        gerarLogInclusaoAlteracao(objetoAlterado, objetoAnterior, nomeEntidade, nomeEntidadeDescricao, "", "");
    }

    private void gerarLogInclusaoAlteracao(Object objetoAlterado, Object objetoAnterior, String nomeEntidade, String nomeEntidadeDescricao, Integer codigoPessoa) throws Exception {
        gerarLogInclusaoAlteracao(objetoAlterado, objetoAnterior, null, nomeEntidade, nomeEntidadeDescricao, "", "", codigoPessoa);
    }

    private void gerarLogInclusaoAlteracao(Object objetoAlterado, Object objetoAnterior, String nomeEntidade, String nomeEntidadeDescricao, String chavePrimariaEntidadeSubordinada, String nomeCampo) throws Exception {
        gerarLogInclusaoAlteracao(objetoAlterado, objetoAnterior, null, nomeEntidade, nomeEntidadeDescricao, chavePrimariaEntidadeSubordinada, nomeCampo, null);
    }

    private void gerarLogInclusaoAlteracao(Object objetoAlterado, Object objetoAnterior, String nomeEntidadeMae, String nomeEntidade, String nomeEntidadeDescricao, String chavePrimariaEntidadeSubordinada, String nomeCampo, Integer codigoPessoa) throws Exception {

        if (objetoAlterado == null)
            throw new Exception("Objetos alterado não pode ser nulo!");
        if (objetoAnterior == null)
            objetoAnterior = objetoAlterado.getClass().newInstance();

        if (!objetoAlterado.getClass().getSimpleName().equals(objetoAnterior.getClass().getSimpleName()))
            throw new Exception("Objetos de classes diferentes!");

        if (!objetoAlterado.getClass().isAnnotationPresent(Entity.class))
            throw new Exception("Objeto alterado não é uma entidade!");

        if (!objetoAnterior.getClass().isAnnotationPresent(Entity.class))
            throw new Exception("Objeto anterior não é uma entidade!");

        if (nomeEntidadeMae == null) nomeEntidadeMae = nomeEntidade;

        Field[] fields = fieldsMarkedUseOnlyThisToLog.length > 0 ? fieldsMarkedUseOnlyThisToLog : objetoAlterado.getClass().getDeclaredFields();
        fieldsMarkedUseOnlyThisToLog = new Field[]{};

        if (chavePrimaria.isEmpty()) {
            chavePrimaria = getChavePrimariaObjeto(objetoAlterado, objetoAnterior);
        }
        Log log = new Log();
        for (Field field : fields) {
            if (field.isAnnotationPresent(NotLogged.class)) {
                continue;
            }
            if (field.isAnnotationPresent(Id.class)) {
                continue;
            }
            log = new Log();
            field.setAccessible(true);
            if (
                    (field.isAnnotationPresent(ManyToMany.class) || field.isAnnotationPresent(OneToMany.class))
            ) {
                gerarLogLista((Collection<?>) field.get(objetoAnterior), (Collection<?>) field.get(objetoAlterado), nomeEntidadeMae, getNomeEntidade(nomeEntidadeMae, field), field.getName());
            } else if (field.isAnnotationPresent(OneToOne.class) || field.isAnnotationPresent(ManyToOne.class)) {
                if (field.get(objetoAlterado) == null && field.get(objetoAnterior) == null) {
                    continue;
                }
                gerarLogObject(field.get(objetoAnterior), field.get(objetoAlterado), nomeEntidadeMae, getNomeEntidade(nomeEntidadeMae, field), field.getName());
            } else {
                log.setChavePrimaria(chavePrimaria);
                log.setOperacao(operacao);
                if (field.isAnnotationPresent(Transient.class)) {
                    continue;
                }
                if (field.get(objetoAlterado) == null || field.get(objetoAlterado).equals("")) {
                    continue;
                }
                if (!field.get(objetoAlterado).equals(field.get(objetoAnterior)) || operacao.equals("EXCLUSÃO")) {
                    log.setNomeEntidade(nomeEntidade);
                    log.setNomeEntidadeDescricao(nomeEntidadeMae + " - " + nomeEntidadeDescricao);
                    log.setDataAlteracao(new Date());
                    log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
                    if (!nomeCampo.isEmpty()) {
                        log.setNomeCampo(nomeCampo);
                    } else {
                        log.setNomeCampo(field.getName().toLowerCase());
                    }
                    log.setChavePrimariaEntidadeSubordinada(chavePrimariaEntidadeSubordinada);
                    if (field.get(objetoAnterior) == null) {
                        log.setValorCampoAnterior("");
                    } else {
                        if (field.get(objetoAnterior) instanceof Double) {
                            log.setValorCampoAnterior(BigDecimal.valueOf((Double) field.get(objetoAnterior)).setScale(2, RoundingMode.HALF_UP).toString());
                        } else if (field.get(objetoAnterior) instanceof Date) {
                            SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
                            String data = formato.format(field.get(objetoAnterior));
                            log.setValorCampoAnterior(data);
                        } else {
                            log.setValorCampoAnterior(field.get(objetoAnterior).toString());
                        }
                    }
                    if (field.get(objetoAlterado) == null) {
                        log.setValorCampoAlterado("");
                    } else {
                        if (field.get(objetoAlterado) instanceof Double) {
                            log.setValorCampoAlterado(BigDecimal.valueOf((Double) field.get(objetoAlterado)).setScale(2, RoundingMode.HALF_UP).toString());
                        } else if (field.get(objetoAlterado) instanceof Date) {
                            SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
                            String data = formato.format(field.get(objetoAlterado));
                            log.setValorCampoAlterado(data);
                        } else {
                            log.setValorCampoAlterado(field.get(objetoAlterado).toString());
                        }
                    }
                    log.setPessoa(codigoPessoa);
                    logs.add(log);
                }
            }
            field.setAccessible(false);
        }
    }

    private void gerarLogObject(Object anterior, Object alterado, String nomeEntidadeMae, String nomeEntidadeDescricao, String nomeCampo) throws IllegalAccessException, InstantiationException, ClassNotFoundException {

        if (anterior == null) {
            anterior = alterado.getClass().newInstance();
        }

        Log log = new Log();
        log.setChavePrimaria(chavePrimaria);
        log.setOperacao(operacao);
        log.setNomeEntidade(nomeEntidadeMae);
        log.setNomeEntidadeDescricao(nomeEntidadeDescricao);
        log.setDataAlteracao(new Date());
        log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
        log.setNomeCampo(nomeCampo);
        Field[] fields = getFieldsMarkedUseOnlyThisToLog(anterior).length > 1 ?
                getFieldsMarkedUseOnlyThisToLog(anterior) : getFIeldsNotMarkedWithNotLogged(anterior);
        StringBuilder valorCampoAlterado = new StringBuilder(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
        StringBuilder valorCampoAlteradoAux = new StringBuilder(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
        StringBuilder valorCampoAnterior = new StringBuilder(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
        StringBuilder valorCampoAnteriorAux = new StringBuilder(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(Id.class)) {
                log.setChavePrimariaEntidadeSubordinada(field.get(alterado).toString());
                if (field.get(alterado) != null) {
                    valorCampoAlteradoAux.append(field.getName()).append(": ").append(field.get(alterado)).append(", ");
                }
                if (field.get(anterior) != null) {
                    valorCampoAnteriorAux.append(field.getName()).append(": ").append(field.get(anterior)).append(", ");
                }
                continue;
            }
            if (field.get(anterior) == null && field.get(alterado) == null) {
                field.setAccessible(false);
                continue;
            }
            if (!Objects.equals(field.get(alterado), field.get(anterior)) || operacao.equals("EXCLUSÃO")) {
                if ((field.isAnnotationPresent(ManyToMany.class) || field.isAnnotationPresent(OneToMany.class)) && !operacao.equals("INCLUSÃO")) {
                    gerarLogLista((Collection<?>) field.get(anterior), (Collection<?>) field.get(alterado),
                            nomeEntidadeMae, getNomeEntidade(nomeEntidadeMae, field), field.getName());
                } else {
                    if (field.get(anterior) == null) {
                        log.setValorCampoAnterior("");
                    } else {
                        valorCampoAnterior.append(log.getValorCampoAnterior() != null ? log.getValorCampoAnterior() : "");
                        valorCampoAnterior.append(makeAppend(field, anterior, alterado));
                    }
                    if (field.get(alterado) == null) {
                        log.setValorCampoAlterado("");
                    } else {
                        valorCampoAlterado.append(log.getValorCampoAlterado() != null ? log.getValorCampoAlterado() : "");
                        valorCampoAlterado.append(makeAppend(field, alterado, anterior));
                    }
                }
            }
            field.setAccessible(false);
        }

        valorCampoAnterior = new StringBuilder(valorCampoAnterior.toString().trim());
        if (valorCampoAnterior.length() > 0) {
            valorCampoAnterior.deleteCharAt(valorCampoAnterior.length() - 1);
            valorCampoAnteriorAux.append(valorCampoAnterior);
            log.setValorCampoAnterior(valorCampoAnteriorAux.toString());
        }

        valorCampoAlterado = new StringBuilder(valorCampoAlterado.toString().trim());
        if (valorCampoAlterado.length() > 0) {
            valorCampoAlterado.deleteCharAt(valorCampoAlterado.length() - 1);
            valorCampoAlteradoAux.append(valorCampoAlterado);
            log.setValorCampoAlterado(valorCampoAlteradoAux.toString());
        }

        if ((log.getValorCampoAlterado() != null && !log.getValorCampoAlterado().isEmpty())
                || (log.getValorCampoAnterior() != null && !log.getValorCampoAnterior().isEmpty())) {
            logs.add(log);
        }
    }

    private void gerarLogLista(Collection<?> anteriores, Collection<?> alterados, String nomeEntidadeMae, String nomeEntidadeDescricao, String nomeCampo) {
        alterados = alterados == null ? new ArrayList<>() :
                sortByCodigo(alterados);
        anteriores = anteriores == null ? new ArrayList<>() :
                sortByCodigo(anteriores);
        if (alterados.equals(anteriores) || (alterados.isEmpty() && anteriores.isEmpty())) {
            return;
        }
        AtomicReference<StringBuilder> sbValorAnterior = new AtomicReference<>(new StringBuilder("["));
        AtomicReference<StringBuilder> sbValorAlterado = new AtomicReference<>(new StringBuilder("["));
        Log log = new Log();
        log.setNomeCampo(nomeCampo);
        log.setChavePrimaria(chavePrimaria);
        if (operacao.equals("INCLUSÃO") || operacao.equals("EXCLUSÃO")) {
            log.setOperacao(operacao);
        } else {
            if (anteriores.size() > alterados.size()) {
                log.setOperacao("EXCLUSÃO em " + getNomeDescritivo(nomeEntidadeDescricao));
            } else if (anteriores.size() < alterados.size()) {
                log.setOperacao("INCLUSÃO em " + getNomeDescritivo(nomeEntidadeDescricao));
            } else {
                log.setOperacao("ALTERAÇÃO em " + getNomeDescritivo(nomeEntidadeDescricao));
            }
        }
        log.setNomeEntidade(nomeEntidadeMae);
        log.setNomeEntidadeDescricao(nomeEntidadeDescricao);
        log.setDataAlteracao(new Date());
        log.setResponsavelAlteracao(requestService.getUsuarioAtual().getUsername());
        if (!anteriores.isEmpty()) {
            List<?> finalAlterados = new ArrayList<>(alterados);
            anteriores.forEach(
                    anterior -> {
                        Field[] fieldsAnterior = getFieldsMarkedUseOnlyThisToLog(anterior).length > 1 ?
                                getFieldsMarkedUseOnlyThisToLog(anterior) : getFIeldsNotMarkedWithNotLogged(anterior);
                        try {
                            Object objectCompare = null;
                            List<?> filtered = finalAlterados.stream().filter(o -> getId(o).equals(getId(anterior))).collect(Collectors.toList());
                            if (filtered.size() == 1) {
                                objectCompare = filtered.get(0);
                            }
                            sbValorAnterior.get().append(populateSbList(fieldsAnterior, anterior, objectCompare));
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    }
            );
        }
        if (!alterados.isEmpty()) {
            List<?> finalAnteriores = new ArrayList<>(anteriores);
            alterados.forEach(
                    alterado -> {
                        Field[] fieldsAlterado = getFieldsMarkedUseOnlyThisToLog(alterado).length > 1 ?
                                getFieldsMarkedUseOnlyThisToLog(alterado) : getFIeldsNotMarkedWithNotLogged(alterado);
                        try {
                            Object objectCompare = null;
                            List<?> filtered = finalAnteriores.stream().filter(o -> getId(o).equals(getId(alterado))).collect(Collectors.toList());
                            if (filtered.size() == 1) {
                                objectCompare = filtered.get(0);
                            }
                            sbValorAlterado.get().append(populateSbList(fieldsAlterado, alterado, objectCompare));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
            );
        }
        if (!alterados.isEmpty()) {
            if (sbValorAlterado.toString().length() > 1) {
                sbValorAlterado.set(new StringBuilder(sbValorAlterado.toString().trim()));
                sbValorAlterado.get().deleteCharAt(sbValorAlterado.get().length() - 1);
                sbValorAlterado.get().append("]");
            } else {
                sbValorAlterado.set(new StringBuilder());
            }
        } else {
            sbValorAlterado.set(new StringBuilder());
        }
        if (!anteriores.isEmpty()) {
            if (sbValorAnterior.toString().length() > 1) {
                sbValorAnterior.set(new StringBuilder(sbValorAnterior.toString().trim()));
                sbValorAnterior.get().deleteCharAt(sbValorAnterior.get().length() - 1);
                sbValorAnterior.get().append("]");
            } else {
                sbValorAnterior.set(new StringBuilder());
            }
        } else {
            sbValorAnterior.set(new StringBuilder());
        }
        log.setValorCampoAlterado(sbValorAlterado.get().toString());
        log.setValorCampoAnterior(sbValorAnterior.get().toString());
        if (!sbValorAlterado.toString().isEmpty() || !sbValorAnterior.toString().isEmpty()) {
            logs.add(log);
        }
    }

    private StringBuilder populateSbList(Field[] fields, Object object, Object objectToCompare) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder();
        StringBuilder sbCodigo = new StringBuilder("{");
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(Id.class)) {
                sbCodigo.append(field.getName()).append(": ").append(field.get(object)).append(", ");
            } else {
                sb.append(makeAppend(field, object, objectToCompare));
            }
            field.setAccessible(false);
        }
        if (sb.length() > 1) {
            sb = new StringBuilder(sb.toString().trim());
            sbCodigo.append(sb);
            sbCodigo.deleteCharAt(sbCodigo.length() - 1);
            sbCodigo.append("},");
        } else {
            sbCodigo = new StringBuilder();
        }
        return sbCodigo;
    }

    private StringBuilder makeAppend(Field field, Object object, Object objectToCompare) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder();
        if (field.get(object) != null) {
            Object o = field.get(object);
            Object o2 = null;
            if (field.get(object) instanceof Double) {
                o = BigDecimal.valueOf((Double) field.get(object)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                if (objectToCompare != null) {
                    o2 = field.get(objectToCompare);
                    if (o2 != null) {
                        o2 = BigDecimal.valueOf((Double) field.get(objectToCompare)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                    }
                }
            }
            if (objectToCompare == null || !o.equals(o2)) {
                if (field.get(object) instanceof Collection && ((Collection) field.get(object)).size() == 0) {
                    return new StringBuilder();
                }
                sb.append(field.getName()).append(": ");
                if (field.isAnnotationPresent(OneToOne.class) || field.isAnnotationPresent(ManyToOne.class)) {
                    StringBuilder sb2 = getSbFromObject(field.get(object), objectToCompare == null ? null : field.get(objectToCompare));
                    if (!sb2.toString().isEmpty()) {
                        sb.append(sb2);
                    } else {
                        sb = new StringBuilder();
                    }
                } else if (field.isAnnotationPresent(ManyToMany.class) || field.isAnnotationPresent(OneToMany.class)) {
                    sb.append(getSbFromCollection((Collection<?>) field.get(object), objectToCompare == null ? null : (Collection<?>) field.get(objectToCompare)));
                } else {
                    if (field.get(object) instanceof Double) {
                        sb.append(BigDecimal.valueOf((Double) field.get(object)).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    } else if (field.get(object) instanceof Date) {
                        SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
                        String data = formato.format(field.get(object));
                        sb.append(data);
                    } else {
                        sb.append(field.get(object));
                    }
                }
                sb.append(", ");
            }
        }
        if (sb.toString().trim().length() == 1) {
            sb = new StringBuilder();
        }
        return sb;
    }

    private StringBuilder getSbFromObject(Object object, Object objectToCompare) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder("{");
        Field[] fields = getFieldsMarkedUseOnlyThisToLog(object).length > 1 ?
                getFieldsMarkedUseOnlyThisToLog(object) : getFIeldsNotMarkedWithNotLogged(object);
        for (Field f : fields) {
            f.setAccessible(true);
            if (f.get(object) != null) {
                sb.append(makeAppend(f, object, objectToCompare));
            }
            f.setAccessible(false);
        }
        if (sb.length() > 1) {
            sb = new StringBuilder(sb.toString().trim());
            sb.deleteCharAt(sb.length() - 1);
            sb.append("}");
            return sb;
        }
        return new StringBuilder();
    }

    private StringBuilder getSbFromCollection(Collection<?> object, Collection<?> objectToCompare) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder();
        List<?> objects = object == null ? new ArrayList<>() : sortByCodigo(object);
        List<?> objectsToCompare = objectToCompare == null ? new ArrayList<>() : sortByCodigo(objectToCompare);

        AtomicReference<StringBuilder> finalSb = new AtomicReference<>(sb);
        if (objects.size() != 0) {
            sb.append("[");
            objects.forEach(
                    obj -> {
                        StringBuilder sb2 = new StringBuilder();
                        Field[] fields = getFieldsMarkedUseOnlyThisToLog(obj).length > 1 ?
                                getFieldsMarkedUseOnlyThisToLog(obj) : getFIeldsNotMarkedWithNotLogged(obj);
                        Field codigoField = null;
                        Object objectCompare = null;
                        List<?> filtered = objectsToCompare.stream().filter(o -> getId(o).equals(getId(obj))).collect(Collectors.toList());
                        if (filtered.size() == 1) {
                            objectCompare = filtered.get(0);
                        }
                        for (Field field1 : fields) {
                            try {
                                field1.setAccessible(true);
                                if (field1.isAnnotationPresent(Id.class)) {
                                    codigoField = field1;
                                } else {
                                    sb2.append(makeAppend(field1, obj, objectCompare));
                                }
                                field1.setAccessible(false);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        if (sb2.length() > 1) {
                            if (codigoField != null) {
                                codigoField.setAccessible(true);
                                try {
                                    finalSb.get().append("{").append(codigoField.getName()).append(": ").append(codigoField.get(obj)).append(", ");
                                } catch (IllegalAccessException e) {
                                    e.printStackTrace();
                                }
                                codigoField.setAccessible(false);
                            }
                            sb2 = new StringBuilder(sb2.toString().trim());
                            sb2.deleteCharAt(sb2.length() - 1);
                            sb2.append("}, ");
                            finalSb.get().append(sb2);
                        }
                    }
            );
            sb = new StringBuilder(finalSb.toString().trim());
            sb.deleteCharAt(sb.length() - 1);
            sb.append("]");
        }
        return sb;
    }

    private Field[] getFieldsMarkedUseOnlyThisToLog(Object object) {
        return Arrays.stream(object.getClass().getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(UseOnlyThisToLog.class) ||
                        f.isAnnotationPresent(Id.class)).toArray(Field[]::new);
    }

    private Field[] getFIeldsNotMarkedWithNotLogged(Object object) {
        return Arrays.stream(object.getClass().getDeclaredFields())
                .filter(f -> !f.isAnnotationPresent(NotLogged.class) ||
                        f.isAnnotationPresent(Id.class)).toArray(Field[]::new);
    }

    private String getNomeEntidade(String nomeEntidadeMae, Field field) throws ClassNotFoundException {
        String simpleName;
        Class<?> type = field.getType();
        if (field.isAnnotationPresent(ManyToMany.class) || field.isAnnotationPresent(OneToMany.class)) {
            type = Class.forName(((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0].getTypeName());
        }
        simpleName = type.getSimpleName();
        String nomeEntidadeDesc = nomeEntidadeMae + " - " + simpleName;
        if (field.isAnnotationPresent(NomeEntidadeLog.class)) {
            nomeEntidadeDesc += "/" + field.getAnnotation(NomeEntidadeLog.class).value();
        } else if (type.isAnnotationPresent(NomeEntidadeLog.class)) {
            nomeEntidadeDesc += "/" + type.getAnnotation(NomeEntidadeLog.class).value();
        }
        return nomeEntidadeDesc;
    }

    private List<?> sortByCodigo(Collection<?> collectionAnterior) {
        return collectionAnterior.stream().sorted(
                (o1, o2) -> {
                    try {
                        Field field1 = Arrays.stream(o1.getClass().getDeclaredFields()).filter(f -> f.getName().equals("codigo")).collect(Collectors.toList()).get(0);
                        field1.setAccessible(true);
                        Object c1 = field1.get(o1);
                        Object c2 = field1.get(o2);
                        field1.setAccessible(false);
                        if (c1 == null && c2 == null) {
                            return 0;
                        }
                        if (c1 == null) {
                            return (Integer) c2;
                        }
                        if (c2 == null) {
                            return (Integer) c1;
                        }
                        if (c1 instanceof Integer && c2 instanceof Integer) {
                            return Integer.compare((int) c1, (int) c2);
                        } else {
                            return Long.compare((long) c1, (long) c2);
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                    return 0;
                }
        ).collect(Collectors.toList());
    }

    private void saveLogs() throws Exception {
        for (Log log : logs) {
            try {
                logDao.save(log);
            } catch (Exception e) {
                this.logs = new ArrayList<>();
                this.operacao = "";
                this.chavePrimaria = "";
                e.printStackTrace();
                throw e;
            }
        }
        this.logs = new ArrayList<>();
        this.operacao = "";
        this.chavePrimaria = "";
    }

    private void saveLogs(String operacao) throws Exception {
        for (Log log : logs) {
            try {
                log.setOperacao(operacao);
                logDao.save(log);
            } catch (Exception e) {
                e.printStackTrace();
                this.logs = new ArrayList<>();
                this.operacao = "";
                this.chavePrimaria = "";
                throw e;
            }
        }
        this.logs = new ArrayList<>();
        this.operacao = "";
        this.chavePrimaria = "";
    }

    private String getChavePrimariaObjeto(Object objetoAlterado, Object objetoAnterior) throws IllegalAccessException {
        Object object = objetoAlterado != null ? objetoAlterado : objetoAnterior;
        String chavePrimaria = "";
        if (object == null) {
            return chavePrimaria;
        }
        for (Field field : object.getClass().getDeclaredFields()) {
            if (field.isAnnotationPresent(Id.class)) {
                field.setAccessible(true);
                if (operacao.isEmpty()) {
                    if (field.get(objetoAlterado) == null || (objetoAnterior != null && field.get(objetoAnterior) == null)) {
                        operacao = "INCLUSÃO";
                    } else {
                        operacao = "ALTERAÇÃO";
                    }
                }
                if (field.get(object) != null) {
                    chavePrimaria = field.get(object).toString();
                }
                field.setAccessible(false);
                break;
            }
        }
        return chavePrimaria;
    }

    private Object getId(Object object) {
        Assert.notNull(object, "Object must not be null.");

        Field idField = Arrays.stream(object.getClass().getDeclaredFields()).filter(df -> df.isAnnotationPresent(Id.class)).collect(Collectors.toList()).get(0);
        idField.setAccessible(true);
        Object id = null;
        try {
            id = idField.get(object);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        idField.setAccessible(false);
        return id;
    }

    private String getNomeDescritivo(String nomeDescricao) {
        String[] s = nomeDescricao.split("-\\s|/\\s|/");
        if (s.length == 0) {
            return nomeDescricao.trim();
        }
        return s[s.length - 1].trim().toUpperCase();
    }

    @Override
    public void save(LogDTO logDTO) throws Exception {
        Log log = logAdapter.toEntity(logDTO);
        if (log.getDataAlteracao() == null) {
            log.setDataAlteracao(Calendario.hoje());
        }
        if (log.getResponsavelAlteracao() == null) {
            log.setResponsavelAlteracao(
                    requestService.getUsuarioAtual().getUsername()
            );
        }
        logDao.save(log);
    }

    @Override
    public List<LogDTO> consultarPorEntidade(String entidade, FiltroLogClienteJSON filtros, String chavePrimaria, PaginadorDTO paginadorDTO) throws Exception {
        try {
            List<Log> logs = logDao.consultarPorEntidade(entidade, filtros, chavePrimaria, null);
            List<LogDTO> logsAgrupados = montarDadosAgrupado(logs, true);
            return realizarPaginacaoAgrupados(paginadorDTO, logsAgrupados, filtros);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
            throw new ServiceException("Erro ao consultar log: " + e.getMessage());
        }

    }

    @Override
    public void registrarLogErro(String nomeEntidade, int codPessoa, String msg, String responsavel) {
        Log log = new Log();
        log.setNomeCampo("Erro");
        log.setChavePrimaria("");
        log.setNomeEntidade(nomeEntidade);
        log.setPessoa(codPessoa);
        log.setValorCampoAnterior(msg);
        log.setValorCampoAlterado(msg);
        log.setOperacao("ERRO AO CRIAR LOG");
        log.setResponsavelAlteracao(responsavel);

        logRepository.save(log);
    }


}
