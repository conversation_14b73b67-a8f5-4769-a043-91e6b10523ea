package com.pacto.adm.core.services.interfaces;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoJoinDTO;
import com.pacto.config.exceptions.ServiceException;


public interface ConfiguracaoIntegracaoJoinService {

    ConfiguracaoIntegracaoJoinDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException;

    ConfiguracaoIntegracaoJoinDTO salvar(ConfiguracaoIntegracaoJoinDTO configuracaoIntegracaoJoinDTO) throws ServiceException;

}
