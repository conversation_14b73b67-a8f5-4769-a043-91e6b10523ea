package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.HistoricoContratoAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.dao.interfaces.ContratoDao;
import com.pacto.adm.core.dao.interfaces.HistoricoContratoDao;
import com.pacto.adm.core.dto.AtestadoContratoDTO;
import com.pacto.adm.core.dto.HistoricoContratoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.contrato.HistoricoContrato;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.HistoricoContratoService;
import com.pacto.adm.core.services.interfaces.ValidacaoHistoricoContratoService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HistoricoContratoServiceImpl implements HistoricoContratoService {

    @Autowired
    private HistoricoContratoAdapter historicoContratoAdapter;
    @Autowired
    private HistoricoContratoDao historicoContratoDao;
    @Autowired
    private ContratoDao contratoDao;
    @Autowired
    private ValidacaoHistoricoContratoService validarHistoricoContratoService;
    @Autowired
    UsuarioAdapter usuarioAdapter;

    public List<HistoricoContratoDTO> findAllByContrato(Integer codContrato, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            return historicoContratoAdapter.toDtos(historicoContratoDao.findAllByContrato(codContrato, paginadorDTO));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public HistoricoContratoDTO findById(Integer id) throws ServiceException {
        try {
            return historicoContratoAdapter.toDto(historicoContratoDao.findById(id));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Boolean inicializarDadosHistoricoContratoAtestado(AtestadoContratoDTO atestadoContratoDTO) throws Exception {
        Boolean existeAvencer = false;
        HistoricoContrato hisAVencer = null;
        HistoricoContrato obj = new HistoricoContrato();
        obj = historicoContratoDao.obterHistoricoContratoPorCodigoContratoDataInicioDataFim(atestadoContratoDTO.getContrato().getCodigo(), atestadoContratoDTO.getDataInicio());
        if (obj != null && obj.getAvencer()) {
            hisAVencer = (HistoricoContrato) obj.clone();
            //valido que dia começou o historio se foi no mesmo dia do novo registro de historico.
            if (Uteis.getCompareData(obj.getDataInicioSituacao(), atestadoContratoDTO.getDataInicio()) == 0) {
                historicoContratoDao.delete(obj);
            } else {
                obj.setDataFinalSituacao(Uteis.obterDataAnterior(atestadoContratoDTO.getDataInicio(), 1));
                historicoContratoDao.update(obj);
            }

            existeAvencer = true;
        } else if (obj != null) {
            //valido que dia começou o historio se foi no mesmo dia do novo registro de historico.
            if (Uteis.getCompareData(obj.getDataInicioSituacao(), atestadoContratoDTO.getDataInicio()) == 0) {
                obj.setDataFinalSituacao(Uteis.obterDataAnterior(atestadoContratoDTO.getDataInicio(), 0));
            } else {
                obj.setDataFinalSituacao(Uteis.obterDataAnterior(atestadoContratoDTO.getDataInicio(), 1));
            }
            historicoContratoDao.update(obj);
        }
        Contrato contrato = contratoDao.findById(atestadoContratoDTO.getContrato().getCodigo());
        obj = new HistoricoContrato();
        obj.setContrato(contrato);
        obj.setDataRegistro(Calendario.hoje());
        obj.setResponsavelRegistro(usuarioAdapter.toEntity(atestadoContratoDTO.getResponsavelOperacao()));
        obj.setSituacaoRelativaHistorico("");
        obj.setDescricao("ATESTADO");
        obj.setTipoHistorico("AT");
        obj.setDataFinalSituacao(atestadoContratoDTO.getDataTermino());
        obj.setDataInicioSituacao(atestadoContratoDTO.getDataInicio());

        validarHistoricoContratoService.validarPeriodoHistoricoContratoOperacao(
                atestadoContratoDTO.getDataInicio(),
                atestadoContratoDTO.getDataTermino(),
                atestadoContratoDTO.getContrato().getCodigo(),
                obj);

        historicoContratoDao.save(obj);

        obj = new HistoricoContrato();
        obj.setContrato(contrato);
        obj.setDataRegistro(Calendario.hoje());
        obj.setResponsavelRegistro(usuarioAdapter.toEntity(atestadoContratoDTO.getResponsavelOperacao()));
        obj.setSituacaoRelativaHistorico("");
        obj.setDescricao("RETORNO-ATESTADO");
        obj.setTipoHistorico("RA");
        obj.setDataInicioSituacao(atestadoContratoDTO.getDataInicioRetorno());
        if (existeAvencer) {
            hisAVencer.setCodigo(0);
            hisAVencer.setDataRegistro(Calendario.hoje());
            obj.setDataFinalSituacao(atestadoContratoDTO.getDataInicioRetorno());
            hisAVencer.setDataInicioSituacao(obj.getDataFinalSituacao());
            hisAVencer.setDataFinalSituacao(atestadoContratoDTO.getDataInicioRetorno());
        } else {
            obj.setDataFinalSituacao(atestadoContratoDTO.getDataInicioRetorno());
        }
        historicoContratoDao.save(obj);
        if (existeAvencer) {
            historicoContratoDao.save(hisAVencer);
        }
        return existeAvencer;
    }

    @Override
    public void alterarUltimoHistorico(Integer codigoContrato, Date dataTerminoRetorno) throws Exception {
        HistoricoContrato obj = historicoContratoDao.obterUltimoHistoricoContratoPorContrato(codigoContrato);
        if (obj.getAvencer()) {
            historicoContratoDao.delete(obj);
            obj = historicoContratoDao.obterUltimoHistoricoContratoPorContrato(codigoContrato);
        }
        obj.setDataFinalSituacao(dataTerminoRetorno);
        historicoContratoDao.update(obj);
    }

    @Override
    public boolean obterHistoricoContratoPorCodigoContratoDescricao(Integer codContrato, String descricao) throws Exception {
        return historicoContratoDao.obterHistoricoContratoPorCodigoContratoDescricao(codContrato, descricao);
    }

}
