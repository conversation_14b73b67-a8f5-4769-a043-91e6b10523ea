package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.entities.financeiro.Boleto;
import com.pacto.adm.core.objects.EstornoMovProduto;
import com.pacto.adm.core.services.interfaces.BoletoService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BoletoServiceImpl implements BoletoService {

    @Override
    public List<Boleto> excluirBoletoMovProduto(EstornoMovProduto estornoMovProdutoVO) throws Exception {
        return null;
    }
}
