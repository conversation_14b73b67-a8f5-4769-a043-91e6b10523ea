package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.AtestadoContratoDTO;
import com.pacto.adm.core.dto.ClienteComBonusDTO;
import com.pacto.adm.core.dto.ClientesCanceladosDTO;
import com.pacto.adm.core.dto.ContratoOperacaoDTO;
import com.pacto.adm.core.dto.ContratoOperacaoRetroativaDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroContratoOperacaoJSON;
import com.pacto.adm.core.entities.contrato.ContratoOperacao;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ContratoOperacaoService {

    List<ContratoOperacaoDTO> findAll(FiltroContratoOperacaoJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    List<ContratoOperacaoDTO> findAllByContrato(Integer codContrato, FiltroContratoOperacaoJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    ContratoOperacaoDTO findById(Integer id) throws Exception;

    void estornarContratoOperacao(Integer codigoContratoOperacao) throws Exception;

    AtestadoContratoDTO salvarOuAtualizarAtestadoContrato(AtestadoContratoDTO atestadoContratoDTO) throws Exception;

    AtestadoContratoDTO incluirOperacaoAtestado(AtestadoContratoDTO atestadoContratoDTO) throws Exception;

    List<ClientesCanceladosDTO> consultarClientesCancelados(FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClienteComBonusDTO> findClientesComBonus(FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    Boolean validarSeExisteTrancamentoSemRetorno(Integer codigoContrato, boolean exception) throws Exception;

    AtestadoContratoDTO gerarPeriodoRetornoAtestado(AtestadoContratoDTO atestadoContratoDTO) throws Exception;

    long obterNrDiasContratoOperacao(ContratoOperacao contratoOperacao) throws Exception;

    boolean consultarCodigoContratoETipoOperacao(Integer codContrato, String tipoOperacao) throws Exception;

    boolean existeOperacaoParaEsteContrato(Integer codContrato, String tipoOperacao) throws Exception;

    List<ContratoOperacaoRetroativaDTO> consultarOperacoesContratoRetroativa(
            FiltroBIControleOperacoesJSON filtros, PaginadorDTO paginadorDTO
    ) throws ServiceException;
}
