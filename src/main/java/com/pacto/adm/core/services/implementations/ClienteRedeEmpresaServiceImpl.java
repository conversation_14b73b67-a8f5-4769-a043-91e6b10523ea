package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ClienteRedeEmpresaAdapter;
import com.pacto.adm.core.dao.interfaces.ClienteRedeEmpresaDao;
import com.pacto.adm.core.dto.ClienteRedeEmpresaDTO;
import com.pacto.adm.core.entities.ClienteRedeEmpresa;
import com.pacto.adm.core.services.interfaces.ClienteRedeEmpresaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ClienteRedeEmpresaServiceImpl implements ClienteRedeEmpresaService {

    private final ClienteRedeEmpresaDao clienteRedeEmpresaDao;
    private final ClienteRedeEmpresaAdapter clienteRedeEmpresaAdapter;

    public ClienteRedeEmpresaServiceImpl(ClienteRedeEmpresaDao clienteRedeEmpresaDao, ClienteRedeEmpresaAdapter clienteRedeEmpresaAdapter) {
        this.clienteRedeEmpresaDao = clienteRedeEmpresaDao;
        this.clienteRedeEmpresaAdapter = clienteRedeEmpresaAdapter;
    }

    @Override
    public List<ClienteRedeEmpresaDTO> findByCpf(String cpf) throws ServiceException {
        try {
            return clienteRedeEmpresaAdapter.toDtos(clienteRedeEmpresaDao.findByCpf(cpf));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteRedeEmpresaDTO saveOrUpdate(ClienteRedeEmpresaDTO clienteRedeEmpresaDTO) throws ServiceException {
        try {
            validarDados(clienteRedeEmpresaDTO);
            ClienteRedeEmpresa clienteRedeEmpresa = clienteRedeEmpresaAdapter.toEntity(clienteRedeEmpresaDTO);

            ClienteRedeEmpresa clienteRedeEmpresaExistente = clienteRedeEmpresaDao.findByFilters(clienteRedeEmpresaDTO.getCpf(), clienteRedeEmpresaDTO.getChaveEmpresa(), clienteRedeEmpresaDTO.getCodigoEmpresa(), clienteRedeEmpresaDTO.getCodigoMatricula());
            if (clienteRedeEmpresaExistente != null) {
                clienteRedeEmpresa.setCodigo(clienteRedeEmpresaExistente.getCodigo());
                clienteRedeEmpresaDao.update(clienteRedeEmpresa);
            } else {
                clienteRedeEmpresaDao.save(clienteRedeEmpresa);
            }
            return clienteRedeEmpresaAdapter.toDto(clienteRedeEmpresa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void validarDados(ClienteRedeEmpresaDTO clienteRedeEmpresaDTO) throws ServiceException {
        if (UteisValidacao.emptyString(clienteRedeEmpresaDTO.getCpf())) {
            throw new ServiceException("O CPF do cliente é obrigatório.");
        }
        String cpf = Uteis.removerMascara(clienteRedeEmpresaDTO.getCpf());
        if (cpf.length() != 11) {
            throw new ServiceException("O CPF do cliente é inválido.");
        }
        clienteRedeEmpresaDTO.setCpf(cpf);
        if (UteisValidacao.emptyString(clienteRedeEmpresaDTO.getChaveEmpresa())) {
            throw new ServiceException("A chave da empresa é obrigatória.");
        }
        if (UteisValidacao.emptyNumber(clienteRedeEmpresaDTO.getCodigoEmpresa())) {
            throw new ServiceException("O código da empresa é obrigatório.");
        }
        if (UteisValidacao.emptyNumber(clienteRedeEmpresaDTO.getCodigoMatricula())) {
            throw new ServiceException("O código da matrícula é obrigatório.");
        }
        if (UteisValidacao.emptyString(clienteRedeEmpresaDTO.getNome())) {
            throw new ServiceException("O nome do cliente é obrigatório.");
        }
        if (UteisValidacao.emptyString(clienteRedeEmpresaDTO.getNomeEmpresa())) {
            throw new ServiceException("O nome da empresa é obrigatório.");
        }
    }
}
