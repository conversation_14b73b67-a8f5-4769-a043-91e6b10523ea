package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.auladesmarcada.ContadorReposicoesDTO;
import com.pacto.adm.core.dto.auladesmarcada.ReposicaoAulaColetivaDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.Date;
import java.util.List;

public interface AulaDesmarcadaService {

    Integer contarAulasDesmarcadasPorPeriodo(int contrato, int horarioTurma, Date datainicio, Date datafim);

    void excluirAulaDesmarcadaPorRetornoAfastamentoAntecipado(Integer codigoContrato, Date dataRetorno, Date dataFimAfastamento)throws Exception;

    ContadorReposicoesDTO contagemReposicoesAulaColetiva(Integer matricula) throws ServiceException;

    List<ReposicaoAulaColetivaDTO> reposicoesAulaColetiva(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException;

}
