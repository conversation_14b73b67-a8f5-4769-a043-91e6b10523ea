package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.ClienteRedeEmpresaDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ClienteRedeEmpresaService {

    List<ClienteRedeEmpresaDTO> findByCpf(String cpf) throws ServiceException;

    ClienteRedeEmpresaDTO saveOrUpdate(ClienteRedeEmpresaDTO clienteRedeEmpresaDTO) throws ServiceException;
}
