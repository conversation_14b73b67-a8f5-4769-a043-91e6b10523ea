package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.JSONMapper;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class DiscoveryServiceImpl implements DiscoveryService {

    private static final ConcurrentHashMap<String, CompletableFuture<ClientDiscoveryDataDTO>> clientFutures = new ConcurrentHashMap<>();
    private final HttpServico httpServico;
    private final RequestService requestService;
    private final Map<String, ClientDiscoveryDataDTO> clients = new ConcurrentHashMap<>();
    @Value("${discovery.url}")
    private String urlDiscovery;

    public DiscoveryServiceImpl(HttpServico httpServico, RequestService requestService) {
        this.httpServico = httpServico;
        this.requestService = requestService;
    }

    public void clearCache() {
        clients.clear();
        clientFutures.clear();
    }

    public ClientDiscoveryDataDTO getClientDiscovery() throws ServiceException {
        // Extrair os dados necessários do requestService no contexto principal
        return getClientDiscovery(requestService.getUsuarioAtual().getChave(), requestService.getToken());
    }

    public ClientDiscoveryDataDTO getClientDiscovery(final String chave, final String token) throws ServiceException {
        // Verificar o cache
        ClientDiscoveryDataDTO cachedClient = clients.get(chave);
        if (cachedClient != null) {
            return cachedClient;
        }

        // Passar os dados necessários para a execução assíncrona
        return clientFutures.computeIfAbsent(chave, key ->
                CompletableFuture.supplyAsync(() -> {
                    try {
                        // Chamar findDiscoveryData com os dados pré-extraídos
                        ClientDiscoveryDataDTO client = findDiscoveryData(chave, token);
                        clients.put(chave, client);
                        return client;
                    } catch (Exception e) {
                        throw new CompletionException(new ServiceException(e));
                    }
                })
        ).join();
    }

    // Alterar findDiscoveryData para aceitar os parâmetros como argumentos
    private ClientDiscoveryDataDTO findDiscoveryData(String chave, String token) throws Exception {
        final String urlDiscoveryFindKey = String.format("%s/find/%s", urlDiscovery, chave);
        ResponseEntity<String> resposta = httpServico.doJson(urlDiscoveryFindKey, null, HttpMethod.GET, token);
        return JSONMapper.getObject(
                new JSONObject(resposta.getBody()).getJSONObject("content"), ClientDiscoveryDataDTO.class
        );
    }

    public List<String> getDiscoveryCache() {
        return new ArrayList<>(clients.keySet());
    }

}
