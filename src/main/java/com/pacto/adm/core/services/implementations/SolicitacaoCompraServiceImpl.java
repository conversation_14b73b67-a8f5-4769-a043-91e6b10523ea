package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.ArquivoAdapter;
import com.pacto.adm.core.adapters.solicitacaocompra.SolicitacaoCompraAdapter;
import com.pacto.adm.core.dao.interfaces.ArquivoDao;
import com.pacto.adm.core.dao.interfaces.solicitacaocompra.SolicitacaoCompraDao;
import com.pacto.adm.core.dto.ArquivoDTO;
import com.pacto.adm.core.dto.filtros.FiltroSolicitacaoCompraJSON;
import com.pacto.adm.core.dto.solicitacaocompra.SolicitacaoCompraDTO;
import com.pacto.adm.core.entities.Arquivo;
import com.pacto.adm.core.entities.solicitacaocompra.SolicitacaoCompra;
import com.pacto.adm.core.enumerador.MidiaEntidadeEnum;
import com.pacto.adm.core.mscomunication.mediams.MediaMs;
import com.pacto.adm.core.mscomunication.mediams.dto.MediaDTO;
import com.pacto.adm.core.services.interfaces.LogService;
import com.pacto.adm.core.services.interfaces.SolicitacaoCompraService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.UteisValidacao;
import lombok.AllArgsConstructor;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class SolicitacaoCompraServiceImpl implements SolicitacaoCompraService {

    private final Logger LOG = LoggerFactory.getLogger(SolicitacaoCompraServiceImpl.class);

    @Autowired
    private final SolicitacaoCompraAdapter solicitacaoCompraAdapter;
    private final SolicitacaoCompraDao solicitacaoCompraDao;
    private final ArquivoAdapter arquivoAdapter;
    private final ArquivoDao arquivoDao;
    private final RequestService requestService;
    private final LogService logService;
    private final MediaMs mediaMs;

    private final Integer MAX_REGISTROS_PAGINADAS = 10;

    @Override
    public List<SolicitacaoCompraDTO> listarSolicitacoes(FiltroSolicitacaoCompraJSON filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<SolicitacaoCompra> solicitacoes = solicitacaoCompraDao.findAll(filtro, paginadorDTO);
            return solicitacaoCompraAdapter.toDtos(solicitacoes);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    @Override
    public SolicitacaoCompraDTO buscarSolicitacao(Integer id) throws ServiceException {
        try {
            SolicitacaoCompra solicitacaoCompra = solicitacaoCompraDao.findById(id);
            SolicitacaoCompraDTO solicitacaoCompraDTO = solicitacaoCompraAdapter.toDto(solicitacaoCompra);

            return solicitacaoCompraDTO;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public SolicitacaoCompraDTO saveOrUpdate(SolicitacaoCompraDTO solicitacaoCompraDTO) throws ServiceException {
        try {
            solicitacaoCompraDao.getCurrentSession().clear();
            SolicitacaoCompra solicitacaoCompra;
            SolicitacaoCompra solicitacaoCompraAnterior = new SolicitacaoCompra();

            if (UteisValidacao.emptyNumber(solicitacaoCompraDTO.getCodigo())) {
                solicitacaoCompra = solicitacaoCompraAdapter.toEntity(solicitacaoCompraDTO);
                solicitacaoCompra = solicitacaoCompraDao.save(solicitacaoCompra);
                solicitacaoCompraDTO.setArquivos(saveArquivo(solicitacaoCompra, solicitacaoCompraDTO.getArquivos()));
            } else {
                solicitacaoCompra = solicitacaoCompraDao.findById(solicitacaoCompraDTO.getCodigo());
                solicitacaoCompraAnterior = solicitacaoCompra.clone();
                solicitacaoCompra = solicitacaoCompraAdapter.toResquestEntity(solicitacaoCompraDTO, solicitacaoCompraAnterior);
                solicitacaoCompra = solicitacaoCompraDao.update(solicitacaoCompra);
            }

            logService.incluirLogInclusaoAlteracao(solicitacaoCompra, solicitacaoCompraAnterior, "SOLICITACAO_COMPRA", "SOLICITACAO_COMPRA");

            return solicitacaoCompraAdapter.toDto(solicitacaoCompra);

        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause() instanceof ConstraintViolationException) {
                ConstraintViolationException constraintViolationException = ((ConstraintViolationException) e.getCause());
                LOG.error(e.getMessage(), e.getCause());
                throw new ServiceException("erro.validacao", constraintViolationException.getSQLException().getMessage());
            }
            LOG.error(e.getMessage(), e.getCause());
            throw new ServiceException("erro.interno ", e.getCause().getMessage());
        }
    }

    public List<ArquivoDTO> saveArquivo(SolicitacaoCompra solicitacaoCompra, List<ArquivoDTO> arquivosDTO) throws ServiceException {
        try {
            arquivoDao.getCurrentSession().clear();
            if (arquivosDTO != null && !UteisValidacao.emptyList(arquivosDTO)) {
                for (ArquivoDTO arquivoDTO : arquivosDTO) {
                    if (!UteisValidacao.emptyString(arquivoDTO.getDados())) {
                        arquivoDTO.setSolicitacaoCompra(solicitacaoCompra.getCodigo());
                        arquivoDTO.setFotoKey(uploadArquivo(arquivoDTO));
                        Arquivo arquivo = arquivoAdapter.toEntity(arquivoDTO);
                        arquivo = arquivoDao.save(arquivo);
                        arquivoDTO = arquivoAdapter.toDto(arquivo);
                    }
                }
            }
            return arquivosDTO;
        } catch (Exception e) {
            throw new ServiceException("Erro ao salvar o arquivo", e);
        }
    }


    public String uploadArquivo(ArquivoDTO arquivoDTO) throws Exception {
        try {
            MediaDTO mediaDTO = new MediaDTO();
            mediaDTO.setEncriptarIdentificador(true);
            mediaDTO.setChave(this.requestService.getUsuarioAtual().getChave());
            mediaDTO.setIdentificador(arquivoDTO.getSolicitacaoCompra() + "-" + Calendario.hoje().getTime());
            mediaDTO.setTipo("ANEXO_DOCUMENTOS_COLABORADOR_"+(arquivoDTO.getExtensao().replace(".", "").toUpperCase()));
            mediaDTO.setExtensao(arquivoDTO.getExtensao());
            mediaDTO.setArquivo(arquivoDTO.getDados());
            return mediaMs.uploadFile(mediaDTO);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e.getCause());
            throw new ServiceException("Não foi possivel salvar o arquivo: " + arquivoDTO.getNome() + " "+e.getMessage());
        }
    }
}
