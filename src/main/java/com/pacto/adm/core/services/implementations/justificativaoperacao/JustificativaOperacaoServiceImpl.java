package com.pacto.adm.core.services.implementations.justificativaoperacao;

import com.pacto.adm.core.dao.interfaces.nativerepositories.justificativaoperacao.JustificativaOperacaoNativeRepository;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.entities.contrato.JustificativaOperacao;
import com.pacto.adm.core.services.interfaces.justificativaoperacao.JustificativaOperacaoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class JustificativaOperacaoServiceImpl implements JustificativaOperacaoService {

    private final JustificativaOperacaoNativeRepository justificativaOperacaoNativeRepository;

    public JustificativaOperacaoServiceImpl(JustificativaOperacaoNativeRepository justificativaOperacaoNativeRepository) {
        this.justificativaOperacaoNativeRepository = justificativaOperacaoNativeRepository;
    }

    @Override
    public List<JustificativaOperacao> contratosCanceladosTransferidosOutroAluno(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            return justificativaOperacaoNativeRepository.contratosCanceladosTransferidosOutroAluno(filtroBIControleOperacoesJSON, paginadorDTO);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("erro-consultar-contratos-cancelados-transferidos", "Ocorreu um erro ao consultar os contratos cancelados transferidos para outro aluno: ", e);
        }
    }
}
