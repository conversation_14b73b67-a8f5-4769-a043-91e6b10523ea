package com.pacto.adm.core.util;

import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;

import java.text.DateFormat;
import java.util.Calendar;
import java.util.Date;

public class Util {

    public static String mascararDado(String valor, Integer qtdUltimosDigitosNaoMascarar) {
        if (UteisValidacao.emptyString(valor) ||
                UteisValidacao.emptyString(valor.trim())) {
            return valor;
        }
        Integer tamanhoTotal = valor.length();
        Integer totalMascarar = (tamanhoTotal > qtdUltimosDigitosNaoMascarar ? (tamanhoTotal - qtdUltimosDigitosNaoMascarar) : tamanhoTotal);
        Integer atual = 0;
        String valorNovo = "";
        for(int i = 0; i < valor.length(); ++i) {
            ++atual;
            if (atual <= totalMascarar) {
                valorNovo += "*";
            } else {
                valorNovo += valor.charAt(i);
            }
        }
        return valorNovo;
    }

    public static String trocarAcentuacaoHTMLPorAcentuacao(String prm) {

        String HTML_A_AGUDO = "&aacute;";
        String HTML_A_AGUDOMAIUSCULO = "&Aacute;";
        String HTML_A_CIRCUNFLEXO = "&acirc;";
        String HTML_A_CIRCUNFLEXOMAIUSCULO = "&Acirc;";
        String HTML_A_CRASE = "&agrave;";
        String HTML_A_TIO = "&atilde;";
        String HTML_A_TIOMAIUSCULO = "&Atilde;";
        String HTML_E_AGUDO = "&eacute;";
        String HTML_E_AGUDOMAIUSCULO = "&Eacute;";
        String HTML_E_CIRCUNFLEXO = "&ecirc;";
        String HTML_E_CIRCUNFLEXOMAIUSCULO = "&Ecirc;";
        String HTML_I_AGUDO = "&iacute;";
        String HTML_I_AGUDOMAIUSCULO = "&Iacute;";
        String HTML_O_AGUDO = "&oacute;";
        String HTML_O_AGUDOMAIUSCULO = "&Oacute;";
        String HTML_U_AGUDO = "&uacute;";
        String HTML_U_AGUDOMAIUSCULO = "&Uacute;";
        String HTML_O_TIO = "&otilde;";
        String HTML_O_TIOMAIUSCULO = "&Otilde;";
        String HTML_U_TREMA = "&uuml;";
        String HTML_C_CEDILHA = "&ccedil;";
        String HTML_C_CEDILHAMAIUSCULO = "&Ccedil;";
        String HTML_ASPAS_ESQUERDA = "&ldquo";


        String nova = prm;

        if (prm.contains(HTML_A_AGUDO)) {
            nova = prm.replaceAll(HTML_A_AGUDO, String.valueOf(Uteis.A_AGUDO));
        }
        if (nova.contains(HTML_A_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_A_AGUDOMAIUSCULO, String.valueOf(Uteis.A_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_A_CIRCUNFLEXO)) {
            nova = nova.replaceAll(HTML_A_CIRCUNFLEXO, String.valueOf(Uteis.A_CIRCUNFLEXO));
        }
        if (nova.contains(HTML_A_CIRCUNFLEXOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_A_CIRCUNFLEXOMAIUSCULO, String.valueOf(Uteis.A_CIRCUNFLEXOMAIUSCULO));
        }
        if (nova.contains(HTML_A_CRASE)) {
            nova = nova.replaceAll(HTML_A_CRASE, String.valueOf(Uteis.A_CRASE));
        }
        if (nova.contains(HTML_A_TIO)) {
            nova = nova.replaceAll(HTML_A_TIO, String.valueOf(Uteis.A_TIO));
        }
        if (nova.contains(HTML_A_TIOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_A_TIOMAIUSCULO, String.valueOf(Uteis.A_TIOMAIUSCULO));
        }
        if (nova.contains(HTML_E_AGUDO)) {
            nova = nova.replaceAll(HTML_E_AGUDO, String.valueOf(Uteis.E_AGUDO));
        }
        if (nova.contains(HTML_E_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_E_AGUDOMAIUSCULO, String.valueOf(Uteis.E_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_E_CIRCUNFLEXO)) {
            nova = nova.replaceAll(HTML_E_CIRCUNFLEXO, String.valueOf(Uteis.E_CIRCUNFLEXO));
        }
        if (nova.contains(HTML_E_CIRCUNFLEXOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_E_CIRCUNFLEXOMAIUSCULO, String.valueOf(Uteis.E_CIRCUNFLEXOMAIUSCULO));
        }
        if (nova.contains(HTML_I_AGUDO)) {
            nova = nova.replaceAll(HTML_I_AGUDO, String.valueOf(Uteis.I_AGUDO));
        }
        if (nova.contains(HTML_I_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_I_AGUDOMAIUSCULO, String.valueOf(Uteis.I_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_O_AGUDO)) {
            nova = nova.replaceAll(HTML_O_AGUDO, String.valueOf(Uteis.O_AGUDO));
        }
        if (nova.contains(HTML_O_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_O_AGUDOMAIUSCULO, String.valueOf(Uteis.O_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_U_AGUDO)) {
            nova = nova.replaceAll(HTML_U_AGUDO, String.valueOf(Uteis.U_AGUDO));
        }
        if (nova.contains(HTML_U_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_U_AGUDOMAIUSCULO, String.valueOf(Uteis.U_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_O_TIO)) {
            nova = nova.replaceAll(HTML_O_TIO, String.valueOf(Uteis.O_TIO));
        }
        if (nova.contains(HTML_O_TIOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_O_TIOMAIUSCULO, String.valueOf(Uteis.O_TIOMAIUSCULO));
        }
        if (nova.contains(HTML_U_TREMA)) {
            nova = nova.replaceAll(HTML_U_TREMA, String.valueOf(Uteis.U_TREMA));
        }
        if (nova.contains(HTML_C_CEDILHA)) {
            nova = nova.replaceAll(HTML_C_CEDILHA, String.valueOf(Uteis.C_CEDILHA));
        }
        if (nova.contains(HTML_C_CEDILHAMAIUSCULO)) {
            nova = nova.replaceAll(HTML_C_CEDILHAMAIUSCULO, String.valueOf(Uteis.C_CEDILHAMAIUSCULO));
        }
        if (nova.contains("<p>")) {
            nova = nova.replaceAll("<p>", "");
        }
        if (nova.contains("</p>")) {
            nova = nova.replaceAll("</p>", "");
        }
        if (nova.contains("&nbsp;")) {
            nova = nova.replaceAll("&nbsp;", " ");
        }
        return (nova);
    }

    public static String removerEspacosInicioFimString(String texto) {
        if (!UteisValidacao.emptyString(texto)) {
            texto = rtrim(texto);
            texto = ltrim(texto);
        }
        return texto;
    }

    public static String rtrim(String toTrim) {
        char[] val = toTrim.toCharArray();
        int len = val.length;

        while (len > 0 && val[len - 1] <= ' ') {
            len--;
        }
        return len < val.length ? toTrim.substring(0, len) : toTrim;
    }

    public static String ltrim(String toTrim) {
        int st = 0;
        char[] val = toTrim.toCharArray();
        int len = val.length;

        while (st < len && val[st] <= ' ') {
            st++;
        }
        return st > 0 ? toTrim.substring(st, len) : toTrim;
    }

    public static Date obterPrimeiroDiaMes(Date dataPrm) throws Exception {
        if (dataPrm == null) {
            return null;
        }
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);
        int mes = dataCalendar.get(Calendar.MONTH);
        int ano = dataCalendar.get(Calendar.YEAR);

//        DecimalFormat df = new DecimalFormat("00");
//        String mesFormatado = df.format(mes + 1);

//        return getDate(mesFormatado + "/01/" + ano);
        return getDate("01/" + (mes + 1) + "/" + ano);
    }


    public static Date obterUltimoDiaMes(Date data) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(data);

        int dia = obterNumeroDiasDoMes(data);
        dataCalendar.set(Calendar.DAY_OF_MONTH, dia);
        return dataCalendar.getTime();
    }

    public static int obterNumeroDiasDoMes(Date dataInicial) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataInicial);
        int numeroDias = dataCalendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        return numeroDias;
    }

    public static java.util.Date getDate(String data) throws Exception {
        java.util.Date valorData = null;
        DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
        valorData = formatador.parse(data);
        Calendar cal = Calendario.getInstance();
        cal.setTime(Calendario.hoje());
        int hora = cal.get(Calendar.HOUR_OF_DAY);
        int minuto = cal.get(Calendar.MINUTE);
        int segundo = cal.get(Calendar.SECOND);

        cal.setTime(valorData);
        cal.set(Calendar.HOUR_OF_DAY, hora);
        cal.set(Calendar.MINUTE, minuto);
        cal.set(Calendar.SECOND, segundo);

        return cal.getTime();
    }

}
